#!/bin/bash

# Cleanup script to delete all markdown files across the repository
# This script will find and delete all .md files, excluding node_modules directories
# and specific files/directories as requested

set -e  # Exit on any error

echo "🧹 Markdown Files Cleanup Script"
echo "================================="
echo ""

# Function to display help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -n, --dry-run  Show what would be deleted without actually deleting"
    echo "  -y, --yes      Skip confirmation prompt"
    echo ""
    echo "This script will delete ALL .md files in the repository with the following exclusions:"
    echo "  - node_modules directories"
    echo "  - API_ENDPOINT_MIGRATION_GUIDE.md"
    echo "  - SCHEMA_CONSOLIDATION_SUMMARY.md"
    echo "  - docs/external_integrations/ directory"
    echo "  - docs/refactor_policy_schemas/ directory"
    echo "  - fast_api_service_best_practices_governance_platform.md"
}

# Default options
DRY_RUN=false
SKIP_CONFIRMATION=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Find all markdown files (excluding node_modules and specified exclusions)
echo "🔍 Searching for markdown files..."
MD_FILES=$(find . -name "*.md" -type f | grep -v node_modules | \
    grep -v "./API_ENDPOINT_MIGRATION_GUIDE.md" | \
    grep -v "./SCHEMA_CONSOLIDATION_SUMMARY.md" | \
    grep -v "./docs/external_integrations/" | \
    grep -v "./docs/refactor_policy_schemas/" | \
    grep -v "./fast_api_service_best_practices_governance_platform.md" | \
    sort)

if [ -z "$MD_FILES" ]; then
    echo "✅ No markdown files found to delete."
    exit 0
fi

echo "📋 Found the following markdown files:"
echo "$MD_FILES" | sed 's/^/  /'
echo ""

FILE_COUNT=$(echo "$MD_FILES" | wc -l)
echo "📊 Total files to delete: $FILE_COUNT"
echo ""

if [ "$DRY_RUN" = true ]; then
    echo "🔍 DRY RUN MODE - No files will be deleted"
    echo "The above files would be deleted in a real run."
    exit 0
fi

# Confirmation prompt (unless skipped)
if [ "$SKIP_CONFIRMATION" = false ]; then
    echo "⚠️  WARNING: This will permanently delete all markdown files listed above!"
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Operation cancelled."
        exit 0
    fi
fi

echo ""
echo "🗑️  Deleting markdown files..."

# Delete files one by one with progress
DELETED_COUNT=0
FAILED_COUNT=0

while IFS= read -r file; do
    if [ -f "$file" ]; then
        if rm "$file" 2>/dev/null; then
            echo "  ✅ Deleted: $file"
            ((DELETED_COUNT++))
        else
            echo "  ❌ Failed to delete: $file"
            ((FAILED_COUNT++))
        fi
    else
        echo "  ⚠️  File not found: $file"
    fi
done <<< "$MD_FILES"

echo ""
echo "📊 Cleanup Summary:"
echo "  ✅ Successfully deleted: $DELETED_COUNT files"
if [ $FAILED_COUNT -gt 0 ]; then
    echo "  ❌ Failed to delete: $FAILED_COUNT files"
fi

if [ $FAILED_COUNT -eq 0 ]; then
    echo ""
    echo "🎉 All markdown files have been successfully deleted!"
else
    echo ""
    echo "⚠️  Some files could not be deleted. Check permissions or file locks."
    exit 1
fi
