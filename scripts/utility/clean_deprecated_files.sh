#!/bin/bash

# Script to clean all .deprecated extension files
# Author: Generated script
# Usage: ./clean_deprecated_files.sh [--dry-run] [--interactive]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default options
DRY_RUN=false
INTERACTIVE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --interactive)
            INTERACTIVE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--dry-run] [--interactive]"
            echo "  --dry-run      Show what would be deleted without actually deleting"
            echo "  --interactive  Ask for confirmation before deleting each file"
            echo "  --help         Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to log messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find all .deprecated files
log_info "Searching for .deprecated files..."
DEPRECATED_FILES=$(find . -name "*.deprecated" -type f 2>/dev/null)

if [ -z "$DEPRECATED_FILES" ]; then
    log_info "No .deprecated files found."
    exit 0
fi

# Count files
FILE_COUNT=$(echo "$DEPRECATED_FILES" | wc -l)
log_info "Found $FILE_COUNT .deprecated files"

# Calculate total size
TOTAL_SIZE=$(echo "$DEPRECATED_FILES" | xargs du -ch 2>/dev/null | tail -1 | cut -f1)
log_info "Total size: $TOTAL_SIZE"

if [ "$DRY_RUN" = true ]; then
    log_warning "DRY RUN MODE - No files will be deleted"
    echo
    echo "Files that would be deleted:"
    echo "$DEPRECATED_FILES"
    exit 0
fi

# Show files to be deleted
echo
echo "Files to be deleted:"
echo "$DEPRECATED_FILES"
echo

# Ask for confirmation if not in interactive mode
if [ "$INTERACTIVE" = false ]; then
    echo -e "${YELLOW}Are you sure you want to delete all $FILE_COUNT .deprecated files? (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled."
        exit 0
    fi
fi

# Delete files
DELETED_COUNT=0
FAILED_COUNT=0

while IFS= read -r file; do
    if [ "$INTERACTIVE" = true ]; then
        echo -e "${YELLOW}Delete '$file'? (y/N/q)${NC}"
        read -r response
        case $response in
            [Yy]*)
                # Delete the file
                ;;
            [Qq]*)
                log_info "Operation cancelled by user."
                exit 0
                ;;
            *)
                log_info "Skipping '$file'"
                continue
                ;;
        esac
    fi
    
    if rm "$file" 2>/dev/null; then
        log_success "Deleted: $file"
        ((DELETED_COUNT++))
    else
        log_error "Failed to delete: $file"
        ((FAILED_COUNT++))
    fi
done <<< "$DEPRECATED_FILES"

# Summary
echo
log_info "Cleanup completed!"
log_success "Successfully deleted: $DELETED_COUNT files"
if [ $FAILED_COUNT -gt 0 ]; then
    log_error "Failed to delete: $FAILED_COUNT files"
fi

# Show space freed
if [ $DELETED_COUNT -gt 0 ]; then
    log_success "Disk space freed: $TOTAL_SIZE"
fi
