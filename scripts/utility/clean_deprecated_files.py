#!/usr/bin/env python3
"""
Script to clean all .deprecated extension files
Author: Generated script
Usage: python3 clean_deprecated_files.py [--dry-run] [--interactive] [--path PATH]
"""

import os
import sys
import argparse
import glob
from pathlib import Path
from typing import List, Tuple

# ANSI color codes
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def log_info(message: str) -> None:
    """Log info message with color."""
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def log_success(message: str) -> None:
    """Log success message with color."""
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def log_warning(message: str) -> None:
    """Log warning message with color."""
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def log_error(message: str) -> None:
    """Log error message with color."""
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def find_deprecated_files(search_path: str = ".") -> List[Path]:
    """Find all .deprecated files in the given path."""
    deprecated_files = []
    search_pattern = os.path.join(search_path, "**", "*.deprecated")
    
    for file_path in glob.glob(search_pattern, recursive=True):
        if os.path.isfile(file_path):
            deprecated_files.append(Path(file_path))
    
    return sorted(deprecated_files)

def get_total_size(files: List[Path]) -> Tuple[int, str]:
    """Calculate total size of files in bytes and human-readable format."""
    total_bytes = sum(file.stat().st_size for file in files if file.exists())
    
    # Convert to human-readable format
    for unit in ['B', 'KB', 'MB', 'GB']:
        if total_bytes < 1024.0:
            return total_bytes, f"{total_bytes:.1f} {unit}"
        total_bytes /= 1024.0
    return total_bytes, f"{total_bytes:.1f} TB"

def confirm_deletion(message: str) -> bool:
    """Ask user for confirmation."""
    while True:
        response = input(f"{Colors.YELLOW}{message} (y/N){Colors.NC} ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no', '']:
            return False
        else:
            print("Please enter 'y' for yes or 'n' for no.")

def delete_files(files: List[Path], interactive: bool = False) -> Tuple[int, int]:
    """Delete files and return (deleted_count, failed_count)."""
    deleted_count = 0
    failed_count = 0
    
    for file_path in files:
        if interactive:
            if not confirm_deletion(f"Delete '{file_path}'?"):
                log_info(f"Skipping '{file_path}'")
                continue
            
            # Check for quit option
            response = input(f"{Colors.YELLOW}Continue? (y/N/q to quit){Colors.NC} ").strip().lower()
            if response in ['q', 'quit']:
                log_info("Operation cancelled by user.")
                break
        
        try:
            file_path.unlink()
            log_success(f"Deleted: {file_path}")
            deleted_count += 1
        except OSError as e:
            log_error(f"Failed to delete '{file_path}': {e}")
            failed_count += 1
    
    return deleted_count, failed_count

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Clean all .deprecated extension files",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Show what would be deleted without actually deleting"
    )
    parser.add_argument(
        "--interactive", 
        action="store_true", 
        help="Ask for confirmation before deleting each file"
    )
    parser.add_argument(
        "--path", 
        default=".", 
        help="Path to search for .deprecated files (default: current directory)"
    )
    
    args = parser.parse_args()
    
    # Validate search path
    if not os.path.exists(args.path):
        log_error(f"Path '{args.path}' does not exist.")
        sys.exit(1)
    
    # Find deprecated files
    log_info("Searching for .deprecated files...")
    deprecated_files = find_deprecated_files(args.path)
    
    if not deprecated_files:
        log_info("No .deprecated files found.")
        return
    
    # Show summary
    file_count = len(deprecated_files)
    total_bytes, total_size_str = get_total_size(deprecated_files)
    
    log_info(f"Found {file_count} .deprecated files")
    log_info(f"Total size: {total_size_str}")
    
    # Show files
    print("\nFiles found:")
    for file_path in deprecated_files:
        file_size = file_path.stat().st_size if file_path.exists() else 0
        print(f"  {file_path} ({file_size} bytes)")
    
    # Dry run mode
    if args.dry_run:
        log_warning("DRY RUN MODE - No files will be deleted")
        return
    
    # Confirm deletion
    print()
    if not args.interactive:
        if not confirm_deletion(f"Are you sure you want to delete all {file_count} .deprecated files?"):
            log_info("Operation cancelled.")
            return
    
    # Delete files
    deleted_count, failed_count = delete_files(deprecated_files, args.interactive)
    
    # Summary
    print()
    log_info("Cleanup completed!")
    log_success(f"Successfully deleted: {deleted_count} files")
    
    if failed_count > 0:
        log_error(f"Failed to delete: {failed_count} files")
    
    if deleted_count > 0:
        log_success(f"Disk space freed: {total_size_str}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Operation cancelled by user.{Colors.NC}")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)
