#!/bin/bash

# Build and Push Docker Images to Azure Container Registry
# Registry: ViteaDevACR
# Target Platform: Ubuntu/Linux (amd64)
# Note: PostgreSQL uses standard postgres:15-alpine image (no custom build needed)

set -e  # Exit on any error

# Configuration
REGISTRY_NAME="viteadevacr"
REGISTRY_URL="${REGISTRY_NAME}.azurecr.io"

# Image tags
API_IMAGE="${REGISTRY_URL}/pilot-api"
FRONTEND_IMAGE="${REGISTRY_URL}/pilot-frontend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if logged into Azure CLI
check_azure_login() {
    log_info "Checking Azure CLI login status..."
    if ! az account show &>/dev/null; then
        log_error "Not logged into Azure CLI. Please run 'az login' first."
        exit 1
    fi
    log_info "Azure CLI login verified."
}

# Login to Azure Container Registry
acr_login() {
    log_info "Logging into Azure Container Registry: ${REGISTRY_NAME}"
    if ! az acr login --name "${REGISTRY_NAME}"; then
        log_error "Failed to login to Azure Container Registry"
        exit 1
    fi
    log_info "Successfully logged into ACR."
}

# Build and push API image
build_and_push_api() {
    log_info "Building API image..."
    
    # Generate timestamp once
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    
    # Build for linux/amd64 platform (Ubuntu compatibility)
    docker build \
        --platform linux/amd64 \
        -t "${API_IMAGE}:latest" \
        -t "${API_IMAGE}:${TIMESTAMP}" \
        ./enhanced-api-project
    
    if [ $? -eq 0 ]; then
        log_info "API image built successfully."
        
        log_info "Pushing API image to registry..."
        docker push "${API_IMAGE}:latest"
        docker push "${API_IMAGE}:${TIMESTAMP}"
        
        if [ $? -eq 0 ]; then
            log_info "API image pushed successfully."
        else
            log_error "Failed to push API image."
            exit 1
        fi
    else
        log_error "Failed to build API image."
        exit 1
    fi
}

# Build and push Frontend image
build_and_push_frontend() {
    log_info "Building Frontend image..."
    
    # Generate timestamp once
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    
    # Check if environment variables exist for Azure AD
    if [ -z "$REACT_APP_AZURE_CLIENT_ID" ] || [ -z "$REACT_APP_AZURE_TENANT_ID" ]; then
        log_warn "Azure AD environment variables not set. Building without Azure AD integration."
        log_warn "Set REACT_APP_AZURE_CLIENT_ID and REACT_APP_AZURE_TENANT_ID if needed."
    fi
    
    # Build for linux/amd64 platform (Ubuntu compatibility)
    docker build \
        --platform linux/amd64 \
        --build-arg REACT_APP_AZURE_CLIENT_ID="${REACT_APP_AZURE_CLIENT_ID}" \
        --build-arg REACT_APP_AZURE_TENANT_ID="${REACT_APP_AZURE_TENANT_ID}" \
        -t "${FRONTEND_IMAGE}:latest" \
        -t "${FRONTEND_IMAGE}:${TIMESTAMP}" \
        -f Dockerfile.frontend.dynamic \
        .
    
    if [ $? -eq 0 ]; then
        log_info "Frontend image built successfully."
        
        log_info "Pushing Frontend image to registry..."
        docker push "${FRONTEND_IMAGE}:latest"
        docker push "${FRONTEND_IMAGE}:${TIMESTAMP}"
        
        if [ $? -eq 0 ]; then
            log_info "Frontend image pushed successfully."
        else
            log_error "Failed to push Frontend image."
            exit 1
        fi
    else
        log_error "Failed to build Frontend image."
        exit 1
    fi
}

# Main execution
main() {
    log_info "Starting Docker build and push process..."
    log_info "Registry: ${REGISTRY_URL}"
    log_info "Target Platform: linux/amd64 (Ubuntu compatible)"
    log_info "Note: PostgreSQL uses standard postgres:15-alpine (no build required)"
    
    # Pre-flight checks
    check_azure_login
    acr_login
    
    # Build and push images
    build_and_push_api
    build_and_push_frontend
    
    log_info "All images built and pushed successfully!"
    log_info ""
    log_info "Images available at:"
    log_info "  API: ${API_IMAGE}:latest"
    log_info "  Frontend: ${FRONTEND_IMAGE}:latest"
    log_info "  PostgreSQL: postgres:15-alpine (standard image)"
    log_info ""
    log_info "To use these images, update your docker-compose.yml to use:"
    log_info "  pilot-api: image: ${API_IMAGE}:latest"
    log_info "  pilot-frontend: image: ${FRONTEND_IMAGE}:latest"
}

# Check if required commands exist
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v az &> /dev/null; then
        log_error "Azure CLI is not installed or not in PATH"
        exit 1
    fi
}

# Run dependency check and main function
check_dependencies
main