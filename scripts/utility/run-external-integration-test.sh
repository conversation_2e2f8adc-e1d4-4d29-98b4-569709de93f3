#!/bin/bash

# External Integration Test Runner
# This script helps run external integration tests against Vitea

set -e

echo "🚀 External Integration Test Runner"
echo "=================================="
echo ""

# Configuration
VITEA_API_BASE="${VITEA_API_BASE:-http://localhost:8000/api/v1}"
WEBHOOK_RECEIVER_PORT="${WEBHOOK_RECEIVER_PORT:-9000}"
VITEA_AUTH_TOKEN="${VITEA_AUTH_TOKEN:-admin-token}"
INTEGRATIONS_TEST_TOKEN="${INTEGRATIONS_TEST_TOKEN:-test-token}"
WEBHOOK_SECRET="${INTEGRATION_WEBHOOK_SECRET_ACTIVE:-test-secret}"

echo "Configuration:"
echo "  Vitea API: $VITEA_API_BASE"
echo "  Webhook Port: $WEBHOOK_RECEIVER_PORT"
echo "  Auth Token: $VITEA_AUTH_TOKEN"
echo ""

# Check if services are running
echo "🔍 Checking Vitea services..."

# Check Enhanced API  
if curl -f -s "http://localhost:8000/health" > /dev/null 2>&1; then
    echo "  ✅ Enhanced API is running"
else
    echo "  ❌ Enhanced API not accessible at $VITEA_API_BASE"
    echo "     Please start services with: ./start-all-services.sh"
    exit 1
fi

# Check if node_modules exists for the test
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Export environment variables
export VITEA_API_BASE
export WEBHOOK_RECEIVER_PORT  
export VITEA_AUTH_TOKEN
export INTEGRATIONS_TEST_TOKEN
export INTEGRATION_WEBHOOK_SECRET_ACTIVE="$WEBHOOK_SECRET"

# Parse command line arguments
COMMAND="${1:-full-test}"

case "$COMMAND" in
    "full-test")
        echo "🧪 Running complete integration test..."
        echo "   This will test the full external integration flow:"
        echo "   • Webhook receiver setup"
        echo "   • Agent role policies updates"
        echo "   • Policy status updates"
        echo "   • Snapshot API pulling"
        echo ""
        node tests/external-integration-example.js
        ;;
        
    "webhook-only")
        echo "📡 Starting webhook receiver only..."
        echo "   Listening on http://localhost:$WEBHOOK_RECEIVER_PORT/webhooks/policy-events"
        echo "   Press Ctrl+C to stop"
        echo ""
        node tests/external-integration-example.js webhook-only
        ;;
        
    "update-agent")
        if [ -z "$2" ]; then
            echo "Usage: $0 update-agent <agent_id> [policy_ids] [role_id] [group_id]"
            echo "Example: $0 update-agent 89c0d4ee-b4f0-4494-a8fc-3fe6e79de729 'policy1,policy2' role-id group-id"
            exit 1
        fi
        
        AGENT_ID="$2"
        POLICY_IDS="${3:-}"
        ROLE_ID="${4:-sample-role-uuid}"
        GROUP_ID="${5:-sample-group-uuid}"
        
        echo "🔄 Updating agent role policies..."
        echo "   Agent ID: $AGENT_ID"
        echo "   Policy IDs: $POLICY_IDS"
        echo "   Role ID: $ROLE_ID"
        echo "   Group ID: $GROUP_ID"
        echo ""
        node tests/external-integration-example.js update-agent "$AGENT_ID" "$POLICY_IDS" "$ROLE_ID" "$GROUP_ID"
        ;;
        
    "update-policy")
        if [ -z "$2" ]; then
            echo "Usage: $0 update-policy <policy_id> <is_active>"
            echo "Example: $0 update-policy 5ce919b4-7fb2-4ecf-af56-ac103f3701d6 true"
            exit 1
        fi
        
        POLICY_ID="$2"
        IS_ACTIVE="${3:-true}"
        
        echo "🔄 Updating policy status..."
        echo "   Policy ID: $POLICY_ID"
        echo "   Active: $IS_ACTIVE"
        echo ""
        node tests/external-integration-example.js update-policy "$POLICY_ID" "$IS_ACTIVE"
        ;;
        
    "snapshot")
        echo "📸 Pulling assignment snapshot..."
        echo ""
        node tests/external-integration-example.js snapshot
        ;;
        
    "test-user-examples")
        echo "🧪 Testing user's specific examples..."
        echo ""
        
        # Test user's first example: update agent_role_policies
        echo "1️⃣ Testing agent_role_policies update..."
        AGENT_ID="89c0d4ee-b4f0-4494-a8fc-3fe6e79de729"
        POLICY_IDS="5ce919b4-7fb2-4ecf-af56-ac103f3701d6,597df3ba-ae0d-4e38-ba40-aab0f84beb88,1cad0241-8fff-4fa2-aa2c-bed60f28d67f,aa3c7ffd-0a16-4d40-9210-74ef909ba2fd"
        
        node tests/external-integration-example.js update-agent "$AGENT_ID" "$POLICY_IDS" "sample-role-uuid" "sample-group-uuid"
        
        echo ""
        echo "2️⃣ Testing policy update..."
        POLICY_ID="5ce919b4-7fb2-4ecf-af56-ac103f3701d6"
        
        node tests/external-integration-example.js update-policy "$POLICY_ID" "true"
        
        echo ""
        echo "✅ User examples completed!"
        ;;
        
    "help"|"-h"|"--help")
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  full-test           Run complete integration test (default)"
        echo "  webhook-only        Start webhook receiver only"
        echo "  update-agent        Update agent role policies"
        echo "  update-policy       Update policy status"
        echo "  snapshot            Pull assignment snapshot"
        echo "  test-user-examples  Test the specific examples from user"
        echo "  help                Show this help"
        echo ""
        echo "Examples:"
        echo "  $0                                    # Run full test"
        echo "  $0 webhook-only                       # Start webhook receiver"
        echo "  $0 update-agent agent-id policy1,policy2 role-id group-id"
        echo "  $0 update-policy policy-id true"
        echo "  $0 snapshot                           # Pull snapshot data"
        echo "  $0 test-user-examples                 # Test user's specific examples"
        echo ""
        echo "Environment Variables:"
        echo "  VITEA_API_BASE              Base URL for Vitea API"
        echo "  WEBHOOK_RECEIVER_PORT       Port for webhook receiver"
        echo "  VITEA_AUTH_TOKEN           Auth token for API calls"
        echo "  INTEGRATIONS_TEST_TOKEN    Token for snapshot API"
        echo "  INTEGRATION_WEBHOOK_SECRET_ACTIVE  Webhook HMAC secret"
        ;;
        
    *)
        echo "❌ Unknown command: $COMMAND"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac

echo ""
echo "✅ External integration test completed!"