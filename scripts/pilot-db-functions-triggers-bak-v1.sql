-- ==============================================================================
-- VITEA.AI POLICY MANAGEMENT SYSTEM - CONSOLIDATED DATABASE SCHEMA V2
-- ==============================================================================
-- 
-- This script consolidates all original migration scripts plus the new template
-- management system to recreate the complete PostgreSQL schema.
-- It preserves all original constraint names, index names, and structures.
--
-- Version: 2.0.0
-- Updated: August 29, 2025
-- 
-- Major Changes in V2:
-- - Integrated template management system into policy_schemas table
-- - Added generate_default_template() function for auto-generation
-- - Removed deprecated policy_templates table completely
-- - Added template_source tracking and indexes
--
-- Source files consolidated:
-- - configs/database-schema.sql (base schema)
-- - configs/enhanced-database-schema.sql (enhanced tables) 
-- - configs/20250716_*.sql (rego and template enhancements)
-- - configs/20250806_*.sql (roles, groups, agents, access control)
-- - configs/20250811_*.sql (agent role policies)
-- - configs/20250812_*.sql (external integrations)
-- - configs/fix_search_policies_function.sql (function fixes)
-- - scripts/migrate-templates-to-schemas.sql (template management system)
--
-- Usage:
--   psql -h <host> -U <username> -d <database> -f 00-create-complete-postgres-schema-consolidated-v2.sql
--
-- Prerequisites:
--   - PostgreSQL 15+
--   - Database must already exist
--   - User must have CREATE privileges
--
-- ==============================================================================

BEGIN;

-- ==============================================================================
-- EXTENSIONS AND BASE SETUP
-- ==============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- ENUMS (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- Access level for Agent ↔ Role ACL
DO $$ BEGIN
    CREATE TYPE access_level_enum AS ENUM ('view', 'manage');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Generic severity levels (used later by policy_groups)
DO $$ BEGIN
    CREATE TYPE severity_level_enum AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Lifecycle status (active / deprecated / draft)
DO $$ BEGIN
    CREATE TYPE lifecycle_status_enum AS ENUM ('draft', 'active', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Link type enum (from 20250806_03_agent_access.sql)
DO $$ BEGIN
    CREATE TYPE link_type_enum AS ENUM ('direct', 'via_group');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- User status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE user_status_enum AS ENUM ('active', 'suspended', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Agent status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE agent_status_enum AS ENUM ('active', 'pending', 'maintenance', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ==============================================================================
-- BASE TABLES (from database-schema.sql)
-- ==============================================================================

-- Create audit_log table
CREATE TABLE IF NOT EXISTS audit_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    user_role VARCHAR(50),
    resource_name VARCHAR(255),
    access_level VARCHAR(50),
    data_classification VARCHAR(50)
);

-- Create testing_sessions table
CREATE TABLE IF NOT EXISTS testing_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(255) NOT NULL,
    test_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    test_data JSONB,
    results JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- Create evaluation_results table
CREATE TABLE IF NOT EXISTS evaluation_results (
    evaluation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES testing_sessions(session_id),
    test_case VARCHAR(255),
    policy_id UUID,
    input_data JSONB,
    expected_result JSONB,
    actual_result JSONB,
    passed BOOLEAN,
    error_message TEXT,
    execution_time_ms INTEGER,
    evaluated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==============================================================================
-- POLICY SCHEMAS TABLE WITH TEMPLATE MANAGEMENT
-- ==============================================================================


-- Index for fast schema lookup
CREATE INDEX IF NOT EXISTS idx_policy_schemas_active 
ON policy_schemas(schema_name) WHERE is_active = true;

-- Index for template source filtering
CREATE INDEX IF NOT EXISTS idx_policy_schemas_template_source 
ON policy_schemas(template_source) WHERE is_active = true;

-- Index for guardrail lookups
CREATE INDEX IF NOT EXISTS idx_policy_schemas_guardrail 
ON policy_schemas(guardrail_id) WHERE guardrail_id IS NOT NULL;

-- Updated timestamp trigger (reuses existing function)
CREATE TRIGGER update_policy_schemas_updated_at 
BEFORE UPDATE ON policy_schemas 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==============================================================================
-- TEMPLATE GENERATION FUNCTION
-- ==============================================================================

-- Function to auto-generate default template from JSON schema
CREATE OR REPLACE FUNCTION public.generate_default_template(schema_content JSONB)
RETURNS JSONB AS $$
DECLARE
    template JSONB := '{}';
    prop_key TEXT;
    prop_def JSONB;
    nested_template JSONB;
BEGIN
    -- Handle properties
    IF schema_content ? 'properties' THEN
        FOR prop_key, prop_def IN
            SELECT key, value FROM jsonb_each(schema_content->'properties')
        LOOP
            -- Check for default value
            IF prop_def ? 'default' THEN
                template := jsonb_set(template,
                    ARRAY[prop_key],
                    prop_def->'default');
            -- Check for const value
            ELSIF prop_def ? 'const' THEN
                template := jsonb_set(template,
                    ARRAY[prop_key],
                    prop_def->'const');
            -- Check for enum and use first value
            ELSIF prop_def ? 'enum' AND jsonb_array_length(prop_def->'enum') > 0 THEN
                template := jsonb_set(template,
                    ARRAY[prop_key],
                    prop_def->'enum'->0);
            -- Handle nested objects
            ELSIF prop_def->>'type' = 'object' THEN
                nested_template := public.generate_default_template(prop_def);
                template := jsonb_set(template,
                    ARRAY[prop_key],
                    nested_template);
            -- Handle arrays with default items
            ELSIF prop_def->>'type' = 'array' THEN
                IF prop_def->'items' ? 'enum' THEN
                    template := jsonb_set(template,
                        ARRAY[prop_key],
                        jsonb_build_array(prop_def->'items'->'enum'->0));
                ELSE
                    template := jsonb_set(template,
                        ARRAY[prop_key],
                        '[]'::jsonb);
                END IF;
            -- Set type-specific defaults for required fields
            ELSIF schema_content->'required' @> to_jsonb(prop_key) THEN
                CASE prop_def->>'type'
                    WHEN 'string' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '""');
                    WHEN 'number', 'integer' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '0');
                    WHEN 'boolean' THEN
                        template := jsonb_set(template, ARRAY[prop_key], 'false');
                    WHEN 'array' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '[]');
                    WHEN 'object' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '{}');
                END CASE;
            END IF;
        END LOOP;
    END IF;
    
    RETURN template;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION public.generate_default_template(JSONB) IS 'Generates a default template from a JSON schema by extracting defaults, const values, and enum first values';

-- ==============================================================================
-- TRIGGER TO AUTO-GENERATE TEMPLATES ON SCHEMA INSERT/UPDATE
-- ==============================================================================

CREATE OR REPLACE FUNCTION auto_generate_template_on_schema_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only auto-generate if template is null or source is auto_generated
    IF NEW.default_template IS NULL OR NEW.template_source = 'auto_generated' THEN
        NEW.default_template := public.generate_default_template(NEW.schema_content);
        NEW.template_source := 'auto_generated';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for auto-generating templates
CREATE TRIGGER auto_generate_template
BEFORE INSERT OR UPDATE OF schema_content ON policy_schemas
FOR EACH ROW
WHEN (NEW.template_source IS NULL OR NEW.template_source = 'auto_generated')
EXECUTE FUNCTION auto_generate_template_on_schema_change();

-- ==============================================================================
-- POLICY GROUPS TABLE (from 20250806_02_policy_groups.sql)
-- ==============================================================================

-- Create index for active policy groups
CREATE INDEX idx_policy_groups_status ON policy_groups(status);

-- ==============================================================================
-- POLICIES TABLE (from enhanced-database-schema.sql)
-- ==============================================================================

-- Create indexes for performance
-- CREATE INDEX idx_policies_group_id ON policies(policy_group_id);
CREATE INDEX idx_policies_guardrail ON policies(guardrail_id);
CREATE INDEX idx_policies_type ON policies(policy_type);
CREATE INDEX idx_policies_active ON policies(is_active);
CREATE INDEX idx_policies_created_by ON policies(created_by);

-- ==============================================================================
-- ROLES TABLE (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- Create index for role hierarchy
-- CREATE INDEX idx_roles_parent ON roles(parent_role_id);

-- ==============================================================================
-- AGENTS TABLE (from 20250806_03_agent_access.sql)
-- ==============================================================================

-- Create indexes for agent lookups
-- CREATE INDEX idx_agents_type ON agents(type);
-- CREATE INDEX idx_agents_status ON agents(status);

-- ==============================================================================
-- AGENT GROUPS TABLE (from 20250806_03_agent_access.sql)
-- ==============================================================================

-- ==============================================================================
-- PRIVILEGES TABLE (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- ==============================================================================
-- ENUM MANAGEMENT TABLES (from enhanced-database-schema.sql)
-- ==============================================================================

CREATE INDEX idx_enum_values_category ON enum_values(category_id);
CREATE INDEX idx_enum_values_active ON enum_values(is_active);

-- ==============================================================================
-- JUNCTION TABLES (from various migration files)
-- ==============================================================================

-- Junction table: Users ↔ Roles (from 20250806_01_roles_privileges.sql)
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(user_id),
    expires_at TIMESTAMP WITH TIME ZONE,
    PRIMARY KEY (user_id, role_id)
);

-- Create indexes for user role lookups
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);

-- ==============================================================================
-- AGENT ROLE POLICIES TABLE (from 20250811_agent_role_policies.sql)
-- ==============================================================================
-- Create indexes for efficient lookups
CREATE INDEX idx_agent_role_policies_agent ON agent_role_policies(agent_id);
CREATE INDEX idx_agent_role_policies_role ON agent_role_policies(role_id);
CREATE INDEX idx_agent_role_policies_policy ON agent_role_policies(policy_id);
CREATE INDEX idx_agent_role_policies_group ON agent_role_policies(group_id);

-- Create a compound index for common query patterns
CREATE INDEX idx_agent_role_policies_lookup ON agent_role_policies(agent_id, role_id, policy_id, group_id);

-- ==============================================================================
-- EXTERNAL INTEGRATIONS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS external_integrations (
    integration_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(100) NOT NULL, -- e.g., 'webhook', 'api', 'database', 'messaging'
    config JSONB NOT NULL, -- Connection details, credentials (encrypted), settings
    status VARCHAR(50) DEFAULT 'inactive', -- 'active', 'inactive', 'error', 'maintenance'
    health_check_url VARCHAR(500),
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(50), -- 'healthy', 'unhealthy', 'unknown'
    capabilities JSONB DEFAULT '[]'::jsonb, -- What this integration can do
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    CONSTRAINT unique_integration_name UNIQUE(name)
);

-- Create indexes for integration lookups
CREATE INDEX idx_external_integrations_type ON external_integrations(type);
CREATE INDEX idx_external_integrations_status ON external_integrations(status);
CREATE INDEX idx_external_integrations_health ON external_integrations(health_status);

-- ==============================================================================
-- INTEGRATION EVENTS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_events (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL, -- 'data_received', 'data_sent', 'error', 'health_check'
    direction VARCHAR(20), -- 'inbound', 'outbound'
    status VARCHAR(50) NOT NULL, -- 'success', 'failure', 'pending', 'retry'
    request_data JSONB,
    response_data JSONB,
    error_details JSONB,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for event queries
CREATE INDEX idx_integration_events_integration ON integration_events(integration_id);
CREATE INDEX idx_integration_events_type ON integration_events(event_type);
CREATE INDEX idx_integration_events_status ON integration_events(status);
CREATE INDEX idx_integration_events_created ON integration_events(created_at DESC);

-- ==============================================================================
-- INTEGRATION POLICIES TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    direction VARCHAR(20) NOT NULL, -- 'inbound', 'outbound', 'both'
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    conditions JSONB DEFAULT '{}'::jsonb, -- When to apply this policy
    transformations JSONB DEFAULT '{}'::jsonb, -- Data transformations to apply
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_integration_policy UNIQUE(integration_id, policy_id, direction)
);

-- Create indexes for integration policy lookups
CREATE INDEX idx_integration_policies_integration ON integration_policies(integration_id);
CREATE INDEX idx_integration_policies_policy ON integration_policies(policy_id);
CREATE INDEX idx_integration_policies_active ON integration_policies(is_active);

-- ==============================================================================
-- INTEGRATION WEBHOOKS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_webhooks (
    webhook_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    url VARCHAR(500) NOT NULL,
    method VARCHAR(10) DEFAULT 'POST',
    headers JSONB DEFAULT '{}'::jsonb,
    events TEXT[], -- Array of event types to trigger this webhook
    is_active BOOLEAN DEFAULT true,
    secret_token VARCHAR(255), -- For webhook signature verification
    retry_policy JSONB DEFAULT '{"max_attempts": 3, "backoff": "exponential"}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for webhook lookups
CREATE INDEX idx_integration_webhooks_integration ON integration_webhooks(integration_id);
CREATE INDEX idx_integration_webhooks_active ON integration_webhooks(is_active);

-- ==============================================================================
-- INTEGRATION OUTBOX TABLE (ensure exists before triggers enqueue events)
-- ==============================================================================
CREATE TABLE IF NOT EXISTS public.integration_outbox (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID,
    destination TEXT NOT NULL DEFAULT 'webhook',
    event_type TEXT NOT NULL,
    event_version INTEGER NOT NULL DEFAULT 1,
    payload_json JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    attempts INTEGER NOT NULL DEFAULT 0,
    next_attempt_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_error TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for outbox processing
CREATE INDEX IF NOT EXISTS idx_integration_outbox_status ON public.integration_outbox(status);
CREATE INDEX IF NOT EXISTS idx_integration_outbox_next_attempt ON public.integration_outbox(next_attempt_at);

-- Maintain updated_at on updates
DROP TRIGGER IF EXISTS trg_outbox_updated_at ON public.integration_outbox;
CREATE TRIGGER trg_outbox_updated_at
BEFORE UPDATE ON public.integration_outbox
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ==============================================================================
-- POLICY VERSIONS TABLE (for audit trail)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS policy_versions (
    version_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    definition JSONB NOT NULL,
    rego_content TEXT,
    package_name VARCHAR(255),
    policy_type VARCHAR(255),
    schema_version VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    change_summary TEXT,
    CONSTRAINT unique_policy_version UNIQUE(policy_id, version_number)
);

CREATE INDEX idx_policy_versions_policy ON policy_versions(policy_id);
CREATE INDEX idx_policy_versions_created ON policy_versions(created_at DESC);

-- ==============================================================================
-- FEATURE FLAGS TABLE (for gradual rollout)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS feature_flags (
    flag_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT false,
    rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    conditions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(user_id)
);

-- ==============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ==============================================================================

-- Apply update trigger to all tables with updated_at column
-- CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON policy_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_agent_groups_updated_at BEFORE UPDATE ON agent_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_enum_categories_updated_at BEFORE UPDATE ON enum_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- CREATE TRIGGER update_enum_values_updated_at BEFORE UPDATE ON enum_values FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_external_integrations_updated_at BEFORE UPDATE ON external_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_policies_updated_at BEFORE UPDATE ON integration_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_webhooks_updated_at BEFORE UPDATE ON integration_webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==============================================================================
-- STORED PROCEDURES AND FUNCTIONS
-- ==============================================================================

-- Function to search policies with various filters
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    definition JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.policy_id,
        p.name,
        p.description,
        p.category,
        p.severity,
        p.is_active,
        p.definition,
        p.created_at,
        p.updated_at
    FROM policies p
    WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR p_search_term = '' OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    ORDER BY p.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function to get agent's effective policies through roles
CREATE OR REPLACE FUNCTION get_agent_effective_policies(
    p_agent_id UUID
) RETURNS TABLE(
    policy_id UUID,
    policy_name VARCHAR(255),
    role_id UUID,
    role_name VARCHAR(255),
    link_type link_type_enum,
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH agent_direct_roles AS (
        -- Direct roles assigned to the agent
        SELECT 
            ar.role_id,
            r.name as role_name,
            'direct'::link_type_enum as link_type
        FROM agent_roles ar
        JOIN roles r ON ar.role_id = r.role_id
        WHERE ar.agent_id = p_agent_id
    ),
    agent_group_roles AS (
        -- Roles inherited through agent groups
        SELECT DISTINCT
            agr.role_id,
            r.name as role_name,
            'via_group'::link_type_enum as link_type
        FROM agent_group_members agm
        JOIN agent_group_roles agr ON agm.group_id = agr.group_id
        JOIN roles r ON agr.role_id = r.role_id
        WHERE agm.agent_id = p_agent_id
    ),
    all_agent_roles AS (
        SELECT * FROM agent_direct_roles
        UNION
        SELECT * FROM agent_group_roles
    )
    SELECT DISTINCT
        arp.policy_id,
        p.name as policy_name,
        aar.role_id,
        aar.role_name,
        aar.link_type,
        arp.priority
    FROM all_agent_roles aar
    JOIN agent_role_policies arp ON aar.role_id = arp.role_id
    JOIN policies p ON arp.policy_id = p.policy_id
    WHERE arp.agent_id = p_agent_id
        AND arp.is_active = true
        AND p.is_active = true
    ORDER BY arp.priority DESC, p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get role hierarchy
-- CREATE OR REPLACE FUNCTION get_role_hierarchy(
--     p_role_id UUID
-- ) RETURNS TABLE(
--     role_id UUID,
--     name VARCHAR(255),
--     level INTEGER
-- ) AS $$
-- WITH RECURSIVE role_tree AS (
--     SELECT r.role_id, r.name, r.parent_role_id, 0 as level
--     FROM roles r
--     WHERE r.role_id = p_role_id
    
--     UNION ALL
    
--     SELECT r.role_id, r.name, r.parent_role_id, rt.level + 1
--     FROM roles r
--     JOIN role_tree rt ON r.parent_role_id = rt.role_id
-- )
-- SELECT role_id, name, level
-- FROM role_tree
-- ORDER BY level;
-- $$ LANGUAGE SQL;

-- Function to check webhook delivery status
CREATE OR REPLACE FUNCTION check_webhook_delivery_status(
    p_integration_id UUID,
    p_hours_back INTEGER DEFAULT 24
) RETURNS TABLE(
    total_events BIGINT,
    successful_events BIGINT,
    failed_events BIGINT,
    pending_events BIGINT,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE status = 'success') as successful_events,
        COUNT(*) FILTER (WHERE status = 'failure') as failed_events,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_events,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND(100.0 * COUNT(*) FILTER (WHERE status = 'success') / COUNT(*), 2)
            ELSE 0
        END as success_rate
    FROM integration_events
    WHERE integration_id = p_integration_id
        AND created_at >= NOW() - INTERVAL '1 hour' * p_hours_back;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- INITIAL DATA SETUP
-- ==============================================================================

-- Insert default enum categories
ALTER TABLE enum_categories ADD CONSTRAINT enum_categories_name_unique UNIQUE (name);

INSERT INTO enum_categories (name, description, policy_type, field_path, is_active) VALUES
    ('Medical Roles', 'Roles that can access medical data', 'medical_privacy', 'allowed_roles', true),
    ('Medical Fields', 'Medical fields requiring special protection', 'medical_privacy', 'protected_fields', true),
    ('Data Privacy Roles', 'Roles that can access sensitive data', 'data_privacy', 'allowed_roles', true),
    ('Data Privacy Fields', 'Data fields requiring privacy protection', 'data_privacy', 'protected_fields', true),
    ('Access Control Roles', 'Roles for access control policies', 'access_control', 'allowed_roles', true),
    ('Compliance Roles', 'Roles for compliance policies', 'compliance', 'allowed_roles', true),
    ('Severity Levels', 'Policy severity levels', 'all', 'severity', true),
    ('e2e_policy_types', 'Policy types for E2E testing', 'e2e_comprehensive_test', 'policy_type', true)
ON CONFLICT (name) DO NOTHING;

-- Insert default enum values
-- INSERT INTO enum_values (category_id, value, label, sort_order) 
-- SELECT category_id, value, label, sort_order FROM (
--     VALUES 
--         ('policy_categories', 'data_privacy', 'Data Privacy', 1),
--         ('policy_categories', 'access_control', 'Access Control', 2),
--         ('policy_categories', 'compliance', 'Compliance', 3),
--         ('policy_categories', 'security', 'Security', 4),
--         ('severity_levels', 'low', 'Low', 1),
--         ('severity_levels', 'medium', 'Medium', 2),
--         ('severity_levels', 'high', 'High', 3),
--         ('severity_levels', 'critical', 'Critical', 4),
--         ('agent_types', 'api', 'API Agent', 1),
--         ('agent_types', 'worker', 'Worker Agent', 2),
--         ('agent_types', 'monitor', 'Monitoring Agent', 3),
--         ('integration_types', 'webhook', 'Webhook', 1),
--         ('integration_types', 'api', 'REST API', 2),
--         ('integration_types', 'database', 'Database', 3),
--         ('integration_types', 'messaging', 'Message Queue', 4)
-- ) AS v(cat_name, value, label, sort_order)
-- JOIN enum_categories ec ON ec.name = cat_name
-- ON CONFLICT (category_id, value) DO NOTHING;


INSERT INTO enum_values (category_id, value, display_name, description, sort_order, is_active)
SELECT ec.category_id, v.value, v.display_name, v.description, v.sort_order, v.is_active
FROM (
    VALUES 
        -- Medical Roles
        ('Medical Roles', 'doctor', 'Doctor', 'Medical doctors with full access', 1, true),
        ('Medical Roles', 'nurse', 'Nurse', 'Nursing staff with patient care access', 2, true),
        ('Medical Roles', 'admin', 'Administrator', 'System administrators', 3, true),
        ('Medical Roles', 'pharmacist', 'Pharmacist', 'Pharmacy staff with medication access', 4, true),
        ('Medical Roles', 'lab_tech', 'Lab Technician', 'Laboratory technicians', 5, true),
        ('Medical Roles', 'specialist', 'Specialist', 'Medical specialists', 6, true),
        ('Medical Roles', 'resident', 'Resident', 'Medical residents', 7, true),
        
        -- Medical Fields
        ('Medical Fields', 'diagnosis', 'Diagnosis', 'Patient diagnosis information', 1, true),
        ('Medical Fields', 'medication', 'Medication', 'Prescribed medications', 2, true),
        ('Medical Fields', 'lab_orders', 'Lab Orders', 'Laboratory test orders', 3, true),
        ('Medical Fields', 'medical_record_number', 'Medical Record Number', 'Patient record identifier', 4, true),
        ('Medical Fields', 'treatment_plan', 'Treatment Plan', 'Patient treatment plans', 5, true),
        ('Medical Fields', 'billing_info', 'Billing Information', 'Medical billing data', 6, true),
        ('Medical Fields', 'patient_notes', 'Patient Notes', 'Clinical notes and observations', 7, true),
        ('Medical Fields', 'prescriptions', 'Prescriptions', 'Medication prescriptions', 8, true),
        ('Medical Fields', 'vital_signs', 'Vital Signs', 'Patient vital signs', 9, true),
        ('Medical Fields', 'allergies', 'Allergies', 'Patient allergy information', 10, true),
        ('Medical Fields', 'family_history', 'Family History', 'Patient family medical history', 11, true),
        ('Medical Fields', 'immunizations', 'Immunizations', 'Patient immunization records', 12, true),
        
        -- Data Privacy Roles
        ('Data Privacy Roles', 'admin', 'Administrator', 'System administrators', 1, true),
        ('Data Privacy Roles', 'manager', 'Manager', 'Department managers', 2, true),
        ('Data Privacy Roles', 'analyst', 'Analyst', 'Data analysts', 3, true),
        ('Data Privacy Roles', 'user', 'User', 'General users', 4, true),
        ('Data Privacy Roles', 'viewer', 'Viewer', 'Read-only users', 5, true),
        ('Data Privacy Roles', 'editor', 'Editor', 'Content editors', 6, true),
        
        -- Data Privacy Fields
        ('Data Privacy Fields', 'personal_info', 'Personal Information', 'Personal identification data', 1, true),
        ('Data Privacy Fields', 'contact_info', 'Contact Information', 'Contact details', 2, true),
        ('Data Privacy Fields', 'financial_data', 'Financial Data', 'Financial information', 3, true),
        ('Data Privacy Fields', 'employment_data', 'Employment Data', 'Employment information', 4, true),
        ('Data Privacy Fields', 'health_data', 'Health Data', 'Health-related information', 5, true),
        
        -- Access Control Roles
        ('Access Control Roles', 'admin', 'Administrator', 'System administrators', 1, true),
        ('Access Control Roles', 'manager', 'Manager', 'Department managers', 2, true),
        ('Access Control Roles', 'user', 'User', 'General users', 3, true),
        ('Access Control Roles', 'guest', 'Guest', 'Temporary users', 4, true),
        
        -- Compliance Roles
        ('Compliance Roles', 'compliance_officer', 'Compliance Officer', 'Compliance monitoring staff', 1, true),
        ('Compliance Roles', 'auditor', 'Auditor', 'Internal auditors', 2, true),
        ('Compliance Roles', 'legal', 'Legal', 'Legal department staff', 3, true),
        ('Compliance Roles', 'admin', 'Administrator', 'System administrators', 4, true),
        
        -- Severity Levels
        ('Severity Levels', 'low', 'Low', 'Low priority issues', 1, true),
        ('Severity Levels', 'medium', 'Medium', 'Medium priority issues', 2, true),
        ('Severity Levels', 'high', 'High', 'High priority issues', 3, true),
        ('Severity Levels', 'critical', 'Critical', 'Critical priority issues', 4, true),
        
        -- Agent Types (from original query)
        ('agent_types', 'api', 'API Agent', 'API service agents', 1, true),
        ('agent_types', 'worker', 'Worker Agent', 'Background worker agents', 2, true),
        ('agent_types', 'monitor', 'Monitoring Agent', 'System monitoring agents', 3, true),
        
        -- Integration Types (from original query)
        ('integration_types', 'webhook', 'Webhook', 'Webhook integrations', 1, true),
        ('integration_types', 'api', 'REST API', 'REST API integrations', 2, true),
        ('integration_types', 'database', 'Database', 'Database integrations', 3, true),
        ('integration_types', 'messaging', 'Message Queue', 'Message queue integrations', 4, true),
        
        -- E2E Policy Types (from enum_categories)
        ('e2e_policy_types', 'data_privacy', 'Data Privacy', 'Data privacy policy type for testing', 1, true),
        ('e2e_policy_types', 'access_control', 'Access Control', 'Access control policy type for testing', 2, true),
        ('e2e_policy_types', 'compliance', 'Compliance', 'Compliance policy type for testing', 3, true),
        ('e2e_policy_types', 'security', 'Security', 'Security policy type for testing', 4, true)
        
) AS v(cat_name, value, display_name, description, sort_order, is_active)
JOIN enum_categories ec ON ec.name = v.cat_name
ON CONFLICT (category_id, value) DO NOTHING;


-- Insert default roles

ALTER TABLE roles ADD CONSTRAINT roles_name_unique UNIQUE (name);

INSERT INTO roles (role_id, code, name, description, is_system_role) VALUES 
    ('b1b2c3d4-e5f6-7890-abcd-111111111111', 'HIPAA_COMPLIANCE_OFFICER', 'HIPAA Privacy and Compliance Officer', 'Responsible for HIPAA compliance monitoring and privacy oversight', FALSE),
    ('************************************', 'HIPAA_CLINICAL_REVIEWER', 'Clinical Staff with PHI Access', 'Healthcare staff authorized to review patient medical information', FALSE),
    ('b1b2c3d4-e5f6-7890-abcd-333333333333', 'HIPAA_MEDICAL_DIRECTOR', 'Senior Physician with Full Access', 'Senior medical staff with unrestricted access to patient data', FALSE),
    ('b1b2c3d4-e5f6-7890-abcd-444444444444', 'HIPAA_CASE_MANAGER', 'Patient Care Coordinator', 'Staff responsible for coordinating patient care across services', FALSE),
    ('b1b2c3d4-e5f6-7890-abcd-555555555555', 'HIPAA_ADMIN', 'System Administrator', 'Technical staff with system administration privileges', FALSE)
ON CONFLICT (name) DO NOTHING;


-- Insert feature flags
INSERT INTO feature_flags (name, description, is_enabled, rollout_percentage) VALUES
    ('template_management', 'Enable new template management system', true, 100),
    ('policy_versioning', 'Enable policy version tracking', true, 100),
    ('external_integrations', 'Enable external system integrations', true, 100),
    ('enhanced_audit_logging', 'Enable enhanced audit logging', true, 100)
ON CONFLICT (name) DO NOTHING;

COMMIT;

-- ==============================================================================
-- INTEGRATION FUNCTIONS (OUTSIDE TRANSACTION)
-- ==============================================================================

-- Integration helper function (from 20250812_01_external_integrations.sql)
CREATE OR REPLACE FUNCTION integration_enqueue_event(p_event_type TEXT, p_payload JSONB)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.integration_outbox (event_type, payload_json, status, attempts, next_attempt_at)
  VALUES (p_event_type, p_payload, 'pending', 0, CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;

-- Integration trigger functions (from 20250812_01_external_integrations.sql)
CREATE OR REPLACE FUNCTION trg_agents_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'agent.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'agent.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'agent.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', OLD.agent_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  -- Schema-qualify to avoid failures when search_path is empty (e.g., pg_dump headers)
  PERFORM public.integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Also redefine other integration trigger functions with schema-qualified calls
CREATE OR REPLACE FUNCTION public.trg_roles_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'role.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'role.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'role.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','role','resource_id', OLD.role_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM public.integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.trg_policies_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'policy.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'policy.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'policy.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','policy','resource_id', OLD.policy_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM public.integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.trg_arp_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'assignment.linked';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object(
        'resource_type','agent_role_policy',
        'resource_id', jsonb_build_object(
          'agent_id', NEW.agent_id,
          'role_id', NEW.role_id,
          'group_id', NEW.group_id,
          'policy_id', NEW.policy_id
        )
      ),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'assignment.unlinked';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object(
        'resource_type','agent_role_policy',
        'resource_id', jsonb_build_object(
          'agent_id', OLD.agent_id,
          'role_id', OLD.role_id,
          'group_id', OLD.group_id,
          'policy_id', OLD.policy_id
        )
      ),
      'data', jsonb_build_object('change_type','deleted')
    );
  ELSE
    RETURN COALESCE(NEW, OLD);
  END IF;
  PERFORM public.integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Normalize triggers to avoid duplicates from schema load (01) vs this script (02)
-- Agents
DROP TRIGGER IF EXISTS trg_agents_integration ON agents;
DROP TRIGGER IF EXISTS trg_integration_agents ON public.agents;
CREATE TRIGGER trg_integration_agents
AFTER INSERT OR UPDATE OR DELETE ON public.agents
FOR EACH ROW EXECUTE FUNCTION public.trg_agents_enqueue();

-- Roles
DROP TRIGGER IF EXISTS trg_roles_integration ON public.roles;
CREATE TRIGGER trg_roles_integration
AFTER INSERT OR UPDATE OR DELETE ON public.roles
FOR EACH ROW EXECUTE FUNCTION public.trg_roles_enqueue();

-- Policies
DROP TRIGGER IF EXISTS trg_policies_integration ON public.policies;
CREATE TRIGGER trg_policies_integration
AFTER INSERT OR UPDATE OR DELETE ON public.policies
FOR EACH ROW EXECUTE FUNCTION public.trg_policies_enqueue();

-- Agent Role Policies
DROP TRIGGER IF EXISTS trg_integration_arp ON public.agent_role_policies;
CREATE TRIGGER trg_integration_arp
AFTER INSERT OR DELETE ON public.agent_role_policies
FOR EACH ROW EXECUTE FUNCTION public.trg_arp_enqueue();

-- ==============================================================================
-- POST-DEPLOYMENT VERIFICATION
-- ==============================================================================

-- Verify template management is properly configured
DO $$
DECLARE
    schema_count INTEGER;
    template_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO schema_count FROM policy_schemas WHERE is_active = true;
    SELECT COUNT(*) INTO template_count FROM policy_schemas WHERE default_template IS NOT NULL AND is_active = true;
    
    RAISE NOTICE '';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'TEMPLATE MANAGEMENT STATUS';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'Active schemas: %', schema_count;
    RAISE NOTICE 'Schemas with templates: %', template_count;
    RAISE NOTICE '=====================================';
END $$;

-- ==============================================================================
-- SUMMARY MESSAGE
-- ==============================================================================

\echo ''
\echo '=============================================================================='
\echo 'Vitea.ai Policy Management System - Consolidated Schema V2 Deployed'
\echo '=============================================================================='
\echo ''
\echo 'Schema Statistics:'
\echo '  - Total tables created: 34'
\echo '  - Total functions created: 17+ (including template generation)'
\echo '  - Total triggers created: 14+ (including auto-template generation)'
\echo '  - Total indexes created: 40+'
\echo '  - Total enums created: 6'
\echo ''
\echo 'New Template Management Features:'
\echo '  ✓ Integrated template management in policy_schemas table'
\echo '  ✓ Auto-generation function: generate_default_template()'
\echo '  ✓ Template source tracking (auto_generated, manual_override, etc.)'
\echo '  ✓ Automatic template generation trigger on schema changes'
\echo '  ✓ Template-specific indexes for performance'
\echo ''
\echo 'Migration Notes:'
\echo '  - policy_templates table has been REMOVED'
\echo '  - Templates are now in policy_schemas.default_template column'
\echo '  - Use API endpoints for template management:'
\echo '    GET    /api/v1/schemas/:name/template'
\echo '    PUT    /api/v1/schemas/:name/template'
\echo '    DELETE /api/v1/schemas/:name/template'
\echo '    POST   /api/v1/schemas/regenerate-templates'
\echo ''
\echo 'Next Steps:'
\echo '  1. Run ANALYZE to optimize query planning'
\echo '  2. Verify templates are auto-generating for new schemas'
\echo '  3. Test template API endpoints'
\echo '  4. Monitor system for 24-48 hours'
\echo ''
\echo 'Deployment successful!'
\echo '=============================================================================='
\echo ''