server {
    listen 80;
    server_name localhost;

    # Admin UI - served at /admin (with and without trailing slash)
    location /admin {
        alias /usr/share/nginx/html/admin/;
        try_files $uri $uri/ /admin/index.html;
        index index.html;
    }

    # Admin UI static assets
    location ~ ^/admin/static/(.*)$ {
        alias /usr/share/nginx/html/admin/static/$1;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main chatbot interface - served at root
    location / {
        root /usr/share/nginx/html/frontend;
        try_files $uri $uri/ /index.html;
    }

    # API Gateway - Route different endpoints to different services
       
    # Policy/Admin API endpoints (pilot-api container) - policies, agents, enums, metrics
    location ~ ^/api/v1/(policies|agents|enums|metrics|policy-groups) {
        proxy_pass http://pilot-api:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Fallback for other /api requests - default to Policy API (pilot-api:8000) 
    location /api/ {
        proxy_pass http://pilot-api:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }

    # Health check endpoint for container
    location /health {
        return 200 'Frontend container healthy';
        add_header Content-Type text/plain;
    }
}