import React from 'react';
import ReactDOM from 'react-dom/client';
import { PublicClientApplication } from '@azure/msal-browser';
import App from './App';
import './index.css';

const msalConfig = {
  auth: {
    clientId: process.env.REACT_APP_AZURE_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${process.env.REACT_APP_AZURE_TENANT_ID}`,
    redirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  }
};

export const msalInstance = new PublicClientApplication(msalConfig);

const root = ReactDOM.createRoot(document.getElementById('root'));

// MSAL should be initialized and any redirects handled before the app renders
async function startApp() {
  try {
    console.log("Starting MSAL initialization...");
    await msalInstance.initialize();
    console.log("MSAL initialized successfully");
    await msalInstance.handleRedirectPromise();
    console.log("MSAL redirect handled");
  } catch (error) {
    console.error("MSAL initialization error:", error);
  }
  
  // Always render the app, regardless of MSAL success/failure
  console.log("Rendering React app...");
  root.render(<App />);
}

startApp();
