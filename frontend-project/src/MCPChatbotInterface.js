import React, { useState, useEffect, useRef } from 'react';
import { 
  Send, 
  MessageCircle, 
  Shield, 
  Brain, 
  Database, 
  Filter, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  User,
  Bot,
  Settings,
  Activity,
  X
} from 'lucide-react';
import patients from './patients';

// Get API base URL from environment or use default
const getApiBaseUrl = () => {
  const currentHost = window.location.hostname;

  console.log('Current hostname for API:', currentHost);

  if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
    // Azure deployment - use Application Gateway HTTPS routing
    return 'https://vitea-pilot-demo.eastus.cloudapp.azure.com';
  } else {
    // Local development - use localhost API
    return 'http://localhost:8001';
  }
};

const API_BASE_URL = getApiBaseUrl();

const MCP_STEPS = [
  { id: 1, name: 'User submits query', icon: User, description: 'User input captured and validated' },
  { id: 2, name: 'Get active policies', icon: Shield, description: 'Retrieve all active governance policies' },
  { id: 3, name: 'Policy rules retrieved', icon: Database, description: 'Policy definitions and rules loaded' },
  { id: 4, name: 'Pre-process request', icon: Filter, description: 'Input sanitization and validation' },
  { id: 5, name: 'Sanitized request to chatbot', icon: Brain, description: 'Send clean request to AI model' },
  { id: 6, name: 'Route through gateway', icon: Settings, description: 'Route request through MCP gateway' },
  { id: 7, name: 'MCP invokes APIs', icon: Activity, description: 'Call relevant backend services' },
  { id: 8, name: 'Database queries', icon: Database, description: 'Execute database operations' },
  { id: 9, name: 'Raw data returned', icon: Database, description: 'Retrieve raw response data' },
  { id: 10, name: 'Policy filtering', icon: Filter, description: 'Apply policy-based content filtering' },
  { id: 11, name: 'Filtered response', icon: CheckCircle, description: 'Generate policy-compliant response' },
  { id: 12, name: 'UI rendering', icon: MessageCircle, description: 'Display final response to user' }
];

function MCPChatbotInterface() {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentSession, setCurrentSession] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [mcpSteps, setMcpSteps] = useState([]);
  const [showMCPDetails, setShowMCPDetails] = useState(false);
  const [processingStep, setProcessingStep] = useState(0);
  const [error, setError] = useState(null); // New state for errors
  const [selectedPatientId, setSelectedPatientId] = useState('');
  const selectedPatient = patients.find(p => p.id === selectedPatientId);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    initializeSession();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const initializeSession = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/chat/session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-Id': 'demo-user-123'
        },
        body: JSON.stringify({
          metadata: { source: 'web_interface', user_agent: navigator.userAgent }
        })
      });

      if (!response.ok) {
        // Throw an error if the response is not OK
        const errorData = await response.json().catch(() => ({ message: 'Failed to initialize session.' }));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const session = await response.json();
      setCurrentSession(session);
      setError(null); // Clear any previous errors
      
      // Add welcome message
      setMessages([{
          id: 'welcome',
          role: 'assistant',
          content: 'Welcome to Duke AI! I\'m here to help with your healthcare-related questions while ensuring all responses comply with privacy policies and regulations. What would you like to know?',
          timestamp: new Date().toISOString(),
          metadata: { type: 'welcome' }
        }]);
    } catch (error) {
      console.error('Error initializing session:', error);
      setError(`Failed to start chat session: ${error.message}. Please refresh the page.`);
    }
  };

  const simulateStepProgress = () => {
    return new Promise((resolve) => {
      let step = 0;
      const interval = setInterval(() => {
        step++;
        setProcessingStep(step);
        
        if (step >= 12) {
          clearInterval(interval);
          setProcessingStep(0);
          resolve();
        }
      }, 300); // Each step takes 300ms
    });
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !currentSession || isProcessing) return;

    const userMessage = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsProcessing(true);

    try {
      // Simulate MCP step progression
      await simulateStepProgress();

      const response = await fetch(`${API_BASE_URL}/api/v1/chat/${currentSession.session_id}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-Id': 'demo-user-123'
        },
        body: JSON.stringify({
          message: userMessage.content
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        const assistantMessage = {
          id: `msg-${Date.now()}-assistant`,
          role: 'assistant',
          content: result.message,
          timestamp: new Date().toISOString(),
          metadata: result.metadata
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Fetch MCP flow details for this session
        await fetchMCPFlow();
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage = {
        id: `msg-${Date.now()}-error`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.',
        timestamp: new Date().toISOString(),
        metadata: { type: 'error' }
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  const fetchMCPFlow = async () => {
    if (!currentSession) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/chat/${currentSession.session_id}/flow`, {
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });

      if (response.ok) {
        const flowSteps = await response.json();
        setMcpSteps(flowSteps);
      }
    } catch (error) {
      console.error('Error fetching MCP flow:', error);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-blue-50 to-indigo-50 font-sans antialiased">
      {/* Main Chat Interface */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 shadow-sm backdrop-blur-lg bg-opacity-80">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <div className="flex items-center space-x-4">
              <div className="bg-gradient-to-br from-blue-600 to-indigo-700 p-3 rounded-2xl shadow-lg transform hover:scale-105 transition-transform duration-200">
                <Brain className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">XYZ Medical AI Chatbot</h1>
                <p className="text-sm text-gray-600">Healthcare AI</p>
              </div>
            </div>
            {/* Move dropdown and button together */}
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="patient-select" className="sr-only">Select Patient</label>
                <select
                  id="patient-select"
                  value={selectedPatientId}
                  onChange={e => setSelectedPatientId(e.target.value)}
                  className="px-4 py-2 bg-white text-gray-700 rounded-lg border border-gray-200 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                >
                  <option value="">Select Patient</option>
                  {patients.map(patient => (
                    <option key={patient.id} value={patient.id}>
                      {patient.name} (ID: {patient.id})
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={() => setShowMCPDetails(!showMCPDetails)}
                className="flex items-center space-x-2 px-4 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 border border-gray-200 shadow-sm"
              >
                <Activity className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">MCP Flow</span>
              </button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-4xl mx-auto space-y-4">
            {error && (
              <div className="flex justify-center mb-6">
                <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg max-w-lg text-center shadow-md">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                    <strong className="font-medium text-red-700">Connection Error:</strong>
                    <span className="ml-2 text-red-600">{error}</span>
                  </div>
                </div>
              </div>
            )}
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-end gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'} transform transition-transform duration-200 ease-out`}
              >
                {message.role === 'assistant' && (
                  <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-2 rounded-full flex-shrink-0 shadow-sm">
                    <Bot className="h-6 w-6 text-blue-700" />
                  </div>
                )}
                
                <div
                  className={`max-w-2xl rounded-2xl p-4 shadow-md transition-all duration-200 ${
                    message.role === 'user'
                      ? 'bg-gradient-to-br from-blue-600 to-indigo-600 text-white rounded-br-none transform hover:-translate-y-1'
                      : 'bg-white text-gray-800 rounded-bl-none transform hover:-translate-y-1'
                  }`}
                >
                  <p className="text-sm leading-relaxed">{message.content}</p>
                  
                  {message.metadata && (
                    <div className={`mt-3 pt-2 border-t ${message.role === 'user' ? 'border-blue-500/30' : 'border-gray-200'}`}>
                      <div className={`flex items-center space-x-4 text-xs ${message.role === 'user' ? 'text-blue-100' : 'text-gray-500'}`}>
                        {message.metadata.policies_applied > 0 && (
                          <div className="flex items-center space-x-1" title={`${message.metadata.policies_applied} policies applied`}>
                            <Shield className="h-3 w-3" />
                            <span>{message.metadata.policies_applied} Policies</span>
                          </div>
                        )}
                        
                        {message.metadata.content_filtered && (
                          <div className="flex items-center space-x-1" title="Content was filtered">
                            <Filter className="h-3 w-3" />
                            <span>Filtered</span>
                          </div>
                        )}
                        
                        {message.metadata.processing_steps && (
                          <div className="flex items-center space-x-1" title={`${message.metadata.processing_steps} processing steps`}>
                            <Activity className="h-3 w-3" />
                            <span>{message.metadata.processing_steps} Steps</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <div className="bg-gradient-to-br from-indigo-100 to-indigo-200 p-2 rounded-full flex-shrink-0 shadow-sm">
                    <User className="h-6 w-6 text-indigo-700" />
                  </div>
                )}
              </div>
            ))}

            {/* Processing Animation */}
            {isProcessing && (
              <div className="flex items-end gap-3 justify-start transform transition-all duration-200 ease-out">
                <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-2 rounded-full flex-shrink-0 shadow-sm">
                  <Bot className="h-6 w-6 text-blue-700" />
                </div>
                <div className="bg-white rounded-2xl p-4 max-w-md shadow-md rounded-bl-none">
                  <div className="flex items-center space-x-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                        <span className="text-sm text-gray-600 animate-pulse">Processing your request...</span>
                      </div>
                      {processingStep > 0 && (
                        <div className="mt-2 space-y-1">
                          <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-blue-600 rounded-full transition-all duration-300 ease-out"
                              style={{ width: `${(processingStep / 12) * 100}%` }}
                            />
                          </div>
                          <div className="text-xs text-gray-500 flex justify-between">
                            <span>Step {processingStep}/12</span>
                            <span>{MCP_STEPS[processingStep - 1]?.name}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="bg-white border-t border-gray-200 p-4 shadow-lg backdrop-blur-lg bg-opacity-80">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={error ? "Cannot send messages." : "Ask about patient data, policies, or care guidelines..."}
                className="w-full p-4 pr-16 rounded-xl border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none resize-none transition-all duration-200 bg-white shadow-sm"
                rows="1"
                disabled={isProcessing || !!error}
                style={{
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                  minHeight: '52px',
                }}
              />
              <button
                onClick={sendMessage}
                disabled={isProcessing || !inputMessage.trim() || !!error}
                className={`absolute right-3 top-1/2 -translate-y-1/2 p-2.5 rounded-full transition-all duration-300 transform
                  ${(!isProcessing && inputMessage.trim() && !error)
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-lg hover:scale-110'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
              >
                {isProcessing ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </button>
            </div>
            
            <div className="mt-2 text-center">
              <span className="inline-flex items-center space-x-1 text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                <Clock className="h-3 w-3" />
                <span>Press Enter to send • Shift+Enter for new line • All responses are policy-filtered</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* MCP Flow Sidebar */}
      {showMCPDetails && (
        <div className="w-96 bg-white border-l border-gray-200 flex flex-col shadow-lg transition-transform duration-300 ease-in-out transform translate-x-0">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-gray-50 to-white">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">MCP Flow Details</h2>
              <p className="text-sm text-gray-600">Real-time processing steps</p>
            </div>
            <button 
              onClick={() => setShowMCPDetails(false)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 bg-gradient-to-br from-gray-50 to-white">
            <div className="space-y-3">
              {MCP_STEPS.map((step, index) => {
                const StepIcon = step.icon;
                const isCompleted = processingStep > index;
                const isActive = processingStep === index + 1;
                const stepData = mcpSteps.find(s => s.step_number === step.id);
                
                return (
                  <div
                    key={step.id}
                    className={`p-4 rounded-lg border transition-all duration-300 transform hover:-translate-y-1 ${
                      isActive
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : isCompleted
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        isActive
                          ? 'bg-blue-100 text-blue-600'
                          : isCompleted
                          ? 'bg-green-100 text-green-600'
                          : 'bg-gray-100 text-gray-500'
                      }`}>
                        <StepIcon className="h-5 w-5" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm font-medium ${
                            isActive
                              ? 'text-blue-900'
                              : isCompleted
                              ? 'text-green-900'
                              : 'text-gray-900'
                          }`}>
                            {step.id}. {step.name}
                          </p>
                          
                          {stepData?.processing_time_ms && (
                            <span className="text-xs bg-white px-2 py-1 rounded-full shadow-sm border border-gray-200">
                              {stepData.processing_time_ms}ms
                            </span>
                          )}
                        </div>
                        
                        <p className="text-xs text-gray-500 mt-1">
                          {step.description}
                        </p>
                        
                        {stepData?.input_data && (
                          <div className="mt-2 p-2 bg-white rounded-lg border border-gray-200 text-xs overflow-hidden">
                            <pre className="text-gray-700 whitespace-pre-wrap">
                              {JSON.stringify(stepData.input_data, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* MCP Statistics */}
          <div className="p-4 border-t border-gray-200 bg-gradient-to-b from-white to-gray-50">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Session Statistics</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <span className="text-gray-500 text-xs block">Messages Sent</span>
                <span className="font-semibold text-lg text-gray-900">{messages.filter(m => m.role === 'user').length}</span>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <span className="text-gray-500 text-xs block">Policies Applied</span>
                <span className="font-semibold text-lg text-gray-900">
                  {messages.reduce((sum, m) => sum + (m.metadata?.policies_applied || 0), 0)}
                </span>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <span className="text-gray-500 text-xs block">Filtered</span>
                <span className="font-semibold text-lg text-gray-900">
                  {messages.filter(m => m.metadata?.content_filtered).length}
                </span>
              </div>
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <span className="text-gray-500 text-xs block">Session ID</span>
                <span className="font-mono text-xs text-gray-900">
                  {currentSession?.session_id?.substring(0, 8)}...
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MCPChatbotInterface;
