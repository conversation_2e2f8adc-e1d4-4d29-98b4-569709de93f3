const patients = [
  {
    id: '39254',
    name: '<PERSON><PERSON><PERSON>',
    gender: 'Female',
    dob: '1965-09-24',
    maritalStatus: 'Married',
    address: 'Full address',
    encounters: [
      { type: 'Ambulatory', description: 'Ambulatory encounter' },
      { type: 'Outpatient', description: 'Outpatient encounter' }
    ],
    observations: [
      'Anesthesia documentation',
      'Hematocrit lab value',
      // ...add 11 more as needed
    ],
    medications: [
      'Theophylline for asthma',
      'Topical medications',
      'Inhalers'
    ],
    conditions: [
      { status: 'active', description: 'Asthma since 1950' },
      { status: 'active', description: 'Hypertension since 2024' },
      { status: 'resolved', description: 'MI' },
      { status: 'resolved', description: 'Pneumonia' },
      { status: 'resolved', description: 'PTCA' }
      // ...add more as needed
    ],
    summary: 'Most comprehensive patient with realistic medical history spanning decades'
  },
  {
    id: '1194',
    name: 'Thị Tám Bảy Năm HL7 Phạm',
    gender: 'Female',
    dob: '1984-11-11',
    maritalStatus: '',
    address: 'Full address',
    encounters: [
      { type: 'Diabetes screening', description: 'Diabetes screening encounter' },
      { type: 'Colon screening', description: 'Colon screening encounter' }
      // ...add more as needed
    ],
    observations: [
      'Hemoglobin',
      'RBC',
      'Hematocrit',
      'MCV'
    ],
    medications: [],
    conditions: [
      { status: 'active', description: 'Burns' },
      { status: 'resolved', description: 'MI' },
      { status: 'resolved', description: 'Bacterial infections' }
      // ...add more as needed
    ],
    summary: 'Diabetes screenings, colon screening, mix of active burns, resolved MI, bacterial infections'
  },
  {
    id: '1208',
    name: 'Tonia Pippo',
    gender: 'Female',
    dob: '1989-04-03',
    maritalStatus: '',
    address: 'Full address in Italy',
    encounters: [
      { type: 'Outpatient injection', description: 'Outpatient injection encounter' }
    ],
    observations: [
      'Lipid panel',
      'Vital signs',
      'BMI',
      'Glasgow Coma Scale'
      // ...add more as needed
    ],
    medications: [
      'Tylenol PM for restless legs'
      // ...add more as needed
    ],
    conditions: [
      { status: 'clinical', description: 'Clinical condition' }
      // ...add more as needed
    ],
    summary: 'Lipid panel, vital signs, BMI, Glasgow Coma Scale, Tylenol PM for restless legs'
  },
  {
    id: '620357',
    name: 'Tina A Roy',
    gender: 'Male',
    dob: '2001-10-10',
    maritalStatus: '',
    address: 'Full contact info',
    encounters: [
      { type: 'Inpatient', description: 'Inpatient for choroidal hemorrhage' }
      // ...add more as needed
    ],
    observations: [
      'Weight measurement'
      // ...add more as needed
    ],
    medications: [],
    conditions: [],
    summary: 'Deceased, inpatient for choroidal hemorrhage, weight measurements'
  }
];

export default patients;