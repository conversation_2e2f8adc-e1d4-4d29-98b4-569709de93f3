// frontend-project/src/App.js
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
// import { MsalProvider, AuthenticatedTemplate, UnauthenticatedTemplate, useMsal } from '@azure/msal-react';
// import { msalInstance } from './index';
import MCPChatbotInterface from './MCPChatbotInterface';

// Get API base URL safely
const getApiBaseUrl = () => {
  const currentHost = window.location.hostname;

  console.log('Current hostname for API:', currentHost);

  if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
    // Azure deployment - use Application Gateway HTTPS routing
    return 'https://vitea-pilot-demo.eastus.cloudapp.azure.com';
  } else {
    // Local development - use localhost API
    return 'http://localhost:8001';
  }
};

const API_BASE_URL = getApiBaseUrl();

const loginRequest = {
  scopes: ['User.Read']
};

function AppContent() {
  return (
    <Routes>
      <Route path="/" element={<MCPChatbotInterface />} />
    </Routes>
  );
}

function App() {
  return (
    // <MsalProvider instance={msalInstance}>
      <Router>
        <div className="App">
          {/* <AuthenticatedTemplate> */}
            <AppContent />
          {/* </AuthenticatedTemplate> */}
          {/* <UnauthenticatedTemplate> */}
            {/* <LoginPage /> */}
          {/* </UnauthenticatedTemplate> */}
        </div>
      </Router>
    // </MsalProvider>
  );
}

export default App;