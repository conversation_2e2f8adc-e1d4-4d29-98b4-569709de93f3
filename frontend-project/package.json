{"name": "vitea-frontend", "version": "1.0.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.7.1", "@azure/msal-react": "^2.0.9", "axios": "^1.5.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.3"}}