import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { pool } from '../src/db.js';
import { initializeDb } from '../src/db.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function migrateSchemas() {
    console.log('🚀 Starting policy schema migration...');
    console.log('================================================');
    
    try {
        // Initialize database connection
        await initializeDb();
        console.log('✅ Database connection established');
        
        // Read schema files
        const apiSchemaPath = path.join(__dirname, '../configs/policy_schemas.json');
        const uiSchemaPath = path.join(__dirname, '../../admin-ui-project/src/policy_schemas.json');
        
        let apiSchemas = {};
        let uiSchemas = {};
        
        // Load API schemas
        if (fs.existsSync(apiSchemaPath)) {
            const apiData = JSON.parse(fs.readFileSync(apiSchemaPath, 'utf8'));
            // Extract just the schemas object
            apiSchemas = apiData.schemas || {};
            console.log(`✅ Loaded ${Object.keys(apiSchemas).length} schemas from API config`);
            console.log(`   Schemas: ${Object.keys(apiSchemas).join(', ')}`);
        } else {
            console.log('⚠️  API schema file not found');
        }
        
        // Load UI schemas
        if (fs.existsSync(uiSchemaPath)) {
            const uiData = JSON.parse(fs.readFileSync(uiSchemaPath, 'utf8'));
            // Extract just the schemas object
            uiSchemas = uiData.schemas || {};
            console.log(`✅ Loaded ${Object.keys(uiSchemas).length} schemas from UI config`);
            console.log(`   Schemas: ${Object.keys(uiSchemas).join(', ')}`);
        } else {
            console.log('⚠️  UI schema file not found');
        }
        
        // Merge schemas (API takes precedence for conflicts)
        const allSchemas = { ...uiSchemas, ...apiSchemas };
        console.log(`\n📊 Total unique schemas to migrate: ${Object.keys(allSchemas).length}`);
        console.log(`   Combined schemas: ${Object.keys(allSchemas).join(', ')}`);
        
        // Check existing schemas in database
        console.log('\n📋 Checking existing database schemas...');
        const existingResult = await pool.query('SELECT schema_name FROM policy_schemas WHERE is_active = true');
        const existingSchemas = existingResult.rows.map(r => r.schema_name);
        console.log(`   Found ${existingSchemas.length} existing schemas: ${existingSchemas.join(', ') || 'none'}`);
        
        // Migrate each schema
        console.log('\n🔄 Starting migration...');
        console.log('================================================');
        let migrated = 0;
        let skipped = 0;
        let failed = 0;
        
        for (const [name, content] of Object.entries(allSchemas)) {
            try {
                // Determine description based on schema name
                let description = `Schema for ${name.replace(/_/g, ' ')} policies`;
                if (name === 'medical_privacy') {
                    description = 'Template for HIPAA-compliant medical privacy policies';
                } else if (name === 'data_privacy') {
                    description = 'Template for general data privacy and protection policies';
                } else if (name === 'access_control') {
                    description = 'Template for role-based access control policies';
                } else if (name === 'compliance') {
                    description = 'Template for regulatory compliance policies';
                }
                
                const result = await pool.query(`
                    INSERT INTO policy_schemas (schema_name, schema_content, description)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (schema_name) DO UPDATE SET
                        schema_content = $2,
                        description = $3,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING id, created_at, updated_at
                `, [name, content, description]);
                
                const wasUpdate = existingSchemas.includes(name);
                if (wasUpdate) {
                    console.log(`✅ Updated: ${name} (modified at ${result.rows[0].updated_at})`);
                } else {
                    console.log(`✅ Created: ${name} (created at ${result.rows[0].created_at})`);
                }
                migrated++;
            } catch (error) {
                console.error(`❌ Failed to migrate ${name}:`, error.message);
                failed++;
            }
        }
        
        console.log('\n================================================');
        console.log(`🎉 Migration completed!`);
        console.log(`   ✅ Successfully migrated: ${migrated} schemas`);
        if (failed > 0) {
            console.log(`   ❌ Failed: ${failed} schemas`);
        }
        
        // Verify final state
        console.log('\n📋 Final database state:');
        const finalResult = await pool.query(`
            SELECT schema_name, description, created_at, updated_at 
            FROM policy_schemas 
            WHERE is_active = true 
            ORDER BY schema_name
        `);
        
        console.log(`   Total active schemas: ${finalResult.rows.length}`);
        finalResult.rows.forEach(row => {
            console.log(`   - ${row.schema_name}: ${row.description}`);
        });
        
        // Create backup of original files
        console.log('\n📦 Creating backups of original files...');
        if (fs.existsSync(apiSchemaPath)) {
            const backupPath = apiSchemaPath + '.backup';
            if (!fs.existsSync(backupPath)) {
                fs.copyFileSync(apiSchemaPath, backupPath);
                console.log(`   ✅ API schema backed up to: ${backupPath}`);
            } else {
                console.log(`   ℹ️  API schema backup already exists`);
            }
        }
        
        if (fs.existsSync(uiSchemaPath)) {
            const backupPath = uiSchemaPath + '.backup';
            if (!fs.existsSync(backupPath)) {
                fs.copyFileSync(uiSchemaPath, backupPath);
                console.log(`   ✅ UI schema backed up to: ${backupPath}`);
            } else {
                console.log(`   ℹ️  UI schema backup already exists`);
            }
        }
        
        console.log('\n✨ Schema migration process complete!');
        console.log('================================================');
        console.log('\nNext steps:');
        console.log('1. Test the API endpoints to ensure schemas load from database');
        console.log('2. Update the Admin UI to fetch schemas from API');
        console.log('3. Verify policy creation/validation still works');
        console.log('4. Once verified, remove the original JSON files');
        
    } catch (error) {
        console.error('\n❌ Migration failed with critical error:', error);
        console.error('Stack trace:', error.stack);
        throw error;
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    migrateSchemas()
        .then(() => {
            console.log('\n✅ Script completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Script failed:', error.message);
            process.exit(1);
        });
}

export default migrateSchemas;