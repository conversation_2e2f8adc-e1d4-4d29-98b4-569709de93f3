import express from 'express';
import crypto from 'crypto';

const app = express();
app.use(express.json({ type: '*/*' }));

const PORT = process.env.RECEIVER_PORT || 9000;
const SECRET = process.env.INTEGRATION_WEBHOOK_SECRET_ACTIVE || '';

function verifySignature(raw, sig) {
  if (!SECRET) return true; // skip if not set
  const expected = crypto.createHmac('sha256', SECRET).update(raw).digest('base64');
  return expected === sig;
}

app.post('/notify', (req, res) => {
  try {
    const raw = JSON.stringify(req.body);
    const sig = req.header('X-Signature') || '';
    const ok = verifySignature(raw, sig);
    console.log('[Receiver] event', {
      id: req.header('X-Event-Id'),
      type: req.header('X-Event-Type'),
      version: req.header('X-Event-Version'),
      ts: req.header('X-Timestamp'),
      sigVerified: ok,
    });
    console.log('[Receiver] body', JSON.stringify(req.body));
    if (!ok) return res.status(401).json({ error: 'bad signature' });
    res.json({ ok: true });
  } catch (e) {
    console.error('[Receiver] error', e);
    res.status(500).json({ error: 'receiver error' });
  }
});

app.listen(PORT, () => {
  console.log(`[Receiver] listening on http://localhost:${PORT}/notify`);
});

