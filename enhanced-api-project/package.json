{"name": "enhanced-api-project", "version": "1.0.0", "description": "Enhanced API for Vitea AI Assistant", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "NODE_OPTIONS=--experimental-vm-modules jest"}, "dependencies": {"@azure/identity": "^4.2.1", "@azure/keyvault-secrets": "^4.8.0", "@azure/openai": "^2.0.0", "@azure/storage-blob": "^12.27.0", "@modelcontextprotocol/sdk": "^1.16.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.5.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "morgan": "^1.10.0", "openai": "^5.9.0", "pg": "^8.11.3", "tar-stream": "^2.2.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nock": "^14.0.9", "nodemon": "^3.0.1", "pg-mem": "^1.0.19", "prom-client": "^15.1.3", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["healthcare", "policy-management", "mcp-flow", "azure-openai", "hipaa-compliant"]}