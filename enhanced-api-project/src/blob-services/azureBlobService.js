// Azure Blob Storage service for Rego files
// src/blob-services/azureBlobService.js

import { BlobServiceClient } from '@azure/storage-blob';

const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
const containerName = process.env.AZURE_STORAGE_CONTAINER || 'rego-policies';

if (!connectionString) {
  throw new Error('AZURE_STORAGE_CONNECTION_STRING is not set in environment variables');
}

const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
const containerClient = blobServiceClient.getContainerClient(containerName);

export async function uploadRegoFile(policyId, regoCode, blobPath) {
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobPath);
    await blockBlobClient.upload(regoCode, Buffer.byteLength(regoCode));
    return blockBlobClient.url;
  } catch (err) {
    console.error('Error uploading Rego file to Blob:', err);
    throw err;
  }
}

export async function getRegoFile(blobPath) {
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobPath);
    const downloadBlockBlobResponse = await blockBlobClient.download();
    const downloaded = await streamToString(downloadBlockBlobResponse.readableStreamBody);
    return downloaded;
  } catch (err) {
    console.error('Error retrieving Rego file from Blob:', err);
    throw err;
  }
}

export async function deleteRegoFile(blobPath) {
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobPath);
    await blockBlobClient.deleteIfExists();
    return true;
  } catch (err) {
    console.error('Error deleting Rego file from Blob:', err);
    throw err;
  }
}

// Helper to convert stream to string
async function streamToString(readableStream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    readableStream.on('data', (data) => {
      chunks.push(data.toString());
    });
    readableStream.on('end', () => {
      resolve(chunks.join(''));
    });
    readableStream.on('error', reject);
  });
}
