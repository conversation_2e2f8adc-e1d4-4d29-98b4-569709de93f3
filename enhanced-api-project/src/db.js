import pg from 'pg';
import 'dotenv/config';
import { DefaultAzureCredential } from "@azure/identity";
import { SecretClient } from "@azure/keyvault-secrets";

const { Pool } = pg;

let pool;

export async function initializeDb() {
  try {
    console.log(`[DB] Initializing DB in NODE_ENV: ${process.env.NODE_ENV}`);
    let dbPassword = process.env.DB_PASSWORD;

    if (process.env.NODE_ENV === 'production') {
      console.log('[DB] Production environment detected, fetching secrets from Azure Key Vault...');
      const vaultName = process.env.KEY_VAULT_NAME;
      if (!vaultName) {
        throw new Error("[FATAL] KEY_VAULT_NAME environment variable not set.");
      }
      const url = `https://${vaultName}.vault.azure.net`;
      const credential = new DefaultAzureCredential();
      const client = new SecretClient(url, credential);
      const secret = await client.getSecret("db-admin-password");
      dbPassword = secret.value;
      console.log('[DB] Successfully fetched database password.');
    }

    console.log('[DB] Connecting to host:', process.env.DB_HOST || 'pilot-postgres');

    const sslConfig = process.env.NODE_ENV === 'production' 
      ? { rejectUnauthorized: false }
      : false; // Disable SSL for local development PostgreSQL

    pool = new Pool({
      host: process.env.DB_HOST || 'pilot-postgres',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'vitea_db',
      user: process.env.DB_USER || 'dbadmin',
      password: dbPassword || 'bHjaY0H/n03j2bBjcRdTyFZIcSH3EjwdF9wr2WC+qEk=',
      ssl: sslConfig
    });

    await pool.query('SELECT 1');
    console.log('[DB] Database pool connected successfully.');

  } catch (err) {
    console.error('[FATAL] DB initialization failed:', err.message);
    if (err.stack) {
        console.error('[FATAL] Error Stack:', err.stack);
    }
    process.exit(1);
  }
}

export { pool }; 