import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

import { recordAudit } from '../utils/auditLogger.js';
const router = express.Router();

// GET /roles  – list all roles
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { rows } = await pool.query('SELECT role_id, code, name, description FROM roles ORDER BY code');
    res.json(rows);
  } catch (err) {
    console.error('[Roles] list error', err);
    res.status(500).json({ error: 'Failed to fetch roles' });
  }
});

// POST /roles – create new role {code, name, description}
router.post('/', requireAdmin, async (req, res) => {
  try {
    const { code, name, description } = req.body;
    if (!code) return res.status(400).json({ error: 'code is required' });
    const { rows } = await pool.query(
      'INSERT INTO roles (code, name, description) VALUES ($1,$2,$3) RETURNING *',
      [code.toUpperCase(), name, description]
    );
    await recordAudit({ userId: req.user.id, action: 'ROLE_CREATED', resourceType: 'role', resourceId: rows[0].role_id, newValues: { code, name } });
    res.status(201).json(rows[0]);
  } catch (err) {
    if (err.code === '23505') {
      return res.status(409).json({ error: 'Role code must be unique' });
    }
    console.error('[Roles] create error', err);
    res.status(500).json({ error: 'Failed to create role' });
  }
});

// PUT /roles/:id – update
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    const { rows } = await pool.query(
      'UPDATE roles SET name = COALESCE($1,name), description = COALESCE($2,description) WHERE role_id = $3 RETURNING *',
      [name, description, id]
    );
    if (!rows.length) return res.status(404).json({ error: 'Role not found' });
    await recordAudit({ userId: req.user.id, action: 'ROLE_UPDATED', resourceType: 'role', resourceId: rows[0].role_id, newValues: { name, description } });
    res.json(rows[0]);
  } catch (err) {
    console.error('[Roles] update error', err);
    res.status(500).json({ error: 'Failed to update role' });
  }
});

// DELETE /roles/:id – hard delete (only if not linked)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check links
    const linkCheck = await pool.query('SELECT 1 FROM user_roles WHERE role_id=$1 LIMIT 1', [id]);
    if (linkCheck.rowCount) {
      return res.status(409).json({ error: 'Role is still assigned to users' });
    }
    await pool.query('DELETE FROM roles WHERE role_id=$1', [id]);
    await recordAudit({ userId: req.user.id, action: 'ROLE_DELETED', resourceType: 'role', resourceId: id });
    res.status(204).end();
  } catch (err) {
    console.error('[Roles] delete error', err);
    res.status(500).json({ error: 'Failed to delete role' });
  }
});

export default router;
