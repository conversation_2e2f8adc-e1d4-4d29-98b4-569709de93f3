import express from 'express';
import { Pool } from 'pg';
import { pool } from '../db.js';

const router = express.Router();

// Middleware to require admin access
const requireAdmin = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authorization header required' });
  }
  
  const token = authHeader.substring(7);
  if (token !== 'admin-token') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  
  next();
};

// Get enum values for a policy type and field
router.get('/values/:policyType/:fieldPath', requireAdmin, async (req, res) => {
  try {
    const { policyType, fieldPath } = req.params;
    
    const result = await pool.query(
      'SELECT value, display_name, description FROM get_enum_values($1, $2)',
      [policyType, fieldPath]
    );
    
    res.json({
      policy_type: policyType,
      field_path: fieldPath,
      values: result.rows
    });
  } catch (err) {
    console.error('Error fetching enum values:', err);
    res.status(500).json({ error: 'Failed to fetch enum values' });
  }
});

// Get all enum fields for a policy type
router.get('/fields/:policyType', requireAdmin, async (req, res) => {
  try {
    const { policyType } = req.params;
    
    const result = await pool.query(
      'SELECT field_path, category_name, description FROM get_enum_fields_for_policy_type($1)',
      [policyType]
    );
    
    res.json({
      policy_type: policyType,
      fields: result.rows
    });
  } catch (err) {
    console.error('Error fetching enum fields:', err);
    res.status(500).json({ error: 'Failed to fetch enum fields' });
  }
});

// Get all enum categories
router.get('/categories', requireAdmin, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT category_id, name, description, policy_type, field_path, is_active FROM enum_categories WHERE is_active = true ORDER BY name'
    );
    
    res.json({
      categories: result.rows
    });
  } catch (err) {
    console.error('Error fetching enum categories:', err);
    res.status(500).json({ error: 'Failed to fetch enum categories' });
  }
});

// Get enum values for a category
router.get('/categories/:categoryId/values', requireAdmin, async (req, res) => {
  try {
    const { categoryId } = req.params;
    
    const result = await pool.query(
      'SELECT value_id, value, display_name, description, sort_order, is_active FROM enum_values WHERE category_id = $1 AND is_active = true ORDER BY sort_order, value',
      [categoryId]
    );
    
    res.json({
      category_id: categoryId,
      values: result.rows
    });
  } catch (err) {
    console.error('Error fetching category values:', err);
    res.status(500).json({ error: 'Failed to fetch category values' });
  }
});

// Create new enum category
router.post('/categories', requireAdmin, async (req, res) => {
  try {
    const { name, description, policy_type, field_path } = req.body;
    
    if (!name || !policy_type || !field_path) {
      return res.status(400).json({ error: 'Name, policy_type, and field_path are required' });
    }
    
    const result = await pool.query(
      'INSERT INTO enum_categories (name, description, policy_type, field_path) VALUES ($1, $2, $3, $4) RETURNING *',
      [name, description, policy_type, field_path]
    );
    
    res.status(201).json({
      message: 'Enum category created successfully',
      category: result.rows[0]
    });
  } catch (err) {
    console.error('Error creating enum category:', err);
    if (err.code === '23505') { // Unique violation
      res.status(400).json({ error: 'Category name already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create enum category' });
    }
  }
});

// Create new enum value
router.post('/categories/:categoryId/values', requireAdmin, async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { value, display_name, description, sort_order } = req.body;
    
    if (!value) {
      return res.status(400).json({ error: 'Value is required' });
    }
    
    const result = await pool.query(
      'INSERT INTO enum_values (category_id, value, display_name, description, sort_order) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [categoryId, value, display_name, description, sort_order || 0]
    );
    
    res.status(201).json({
      message: 'Enum value created successfully',
      value: result.rows[0]
    });
  } catch (err) {
    console.error('Error creating enum value:', err);
    if (err.code === '23505') { // Unique violation
      res.status(400).json({ error: 'Value already exists in this category' });
    } else {
      res.status(500).json({ error: 'Failed to create enum value' });
    }
  }
});

// Update enum value
router.put('/values/:valueId', requireAdmin, async (req, res) => {
  try {
    const { valueId } = req.params;
    const { value, display_name, description, sort_order, is_active } = req.body;
    
    const result = await pool.query(
      'UPDATE enum_values SET value = $1, display_name = $2, description = $3, sort_order = $4, is_active = $5 WHERE value_id = $6 RETURNING *',
      [value, display_name, description, sort_order, is_active, valueId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Enum value not found' });
    }
    
    res.json({
      message: 'Enum value updated successfully',
      value: result.rows[0]
    });
  } catch (err) {
    console.error('Error updating enum value:', err);
    res.status(500).json({ error: 'Failed to update enum value' });
  }
});

// Delete enum value (soft delete)
router.delete('/values/:valueId', requireAdmin, async (req, res) => {
  try {
    const { valueId } = req.params;
    
    const result = await pool.query(
      'UPDATE enum_values SET is_active = false WHERE value_id = $1 RETURNING *',
      [valueId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Enum value not found' });
    }
    
    res.json({
      message: 'Enum value deleted successfully'
    });
  } catch (err) {
    console.error('Error deleting enum value:', err);
    res.status(500).json({ error: 'Failed to delete enum value' });
  }
});

export default router; 