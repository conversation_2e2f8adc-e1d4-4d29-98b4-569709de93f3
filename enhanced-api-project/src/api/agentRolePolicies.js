import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// GET /agents/:id/role-policies
router.get('/agents/:id/role-policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rows } = await pool.query(
      `SELECT arp.agent_id, arp.role_id, r.name AS role_name, r.code AS role_code,
              arp.group_id, pg.name AS group_name, pg.description AS group_description,
              arp.policy_id, p.name AS policy_name, p.description AS policy_description
         FROM agent_role_policies arp
         JOIN roles r ON r.role_id = arp.role_id
         JOIN policy_groups pg ON pg.group_id = arp.group_id 
             AND pg.status = 'active'
         JOIN policies p ON p.policy_id = arp.policy_id 
             AND p.deleted_at IS NULL
             AND p.is_active = true
        WHERE arp.agent_id = $1
        ORDER BY r.name, pg.name, p.name`,
      [id]
    );
    res.json(rows);
  } catch (err) {
    console.error('[AgentRolePolicies] list error', err);
    res.status(500).json({ error: 'Failed to fetch role-scoped policies' });
  }
});

// POST /agents/:id/role-policies
router.post('/agents/:id/role-policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId, groupId, policyId } = req.body || {};
    if (!roleId || !groupId || !policyId) {
      return res.status(400).json({ error: 'roleId, groupId, and policyId are required' });
    }
    // Validate group-policy pair exists
    const valid = await pool.query(
      'SELECT 1 FROM policy_group_policies WHERE group_id = $1 AND policy_id = $2',
      [groupId, policyId]
    );
    if (!valid.rows.length) {
      return res.status(400).json({ error: 'Policy is not a member of the specified group' });
    }
    await pool.query(
      `INSERT INTO agent_role_policies (agent_id, role_id, group_id, policy_id)
       VALUES ($1, $2, $3, $4)
       ON CONFLICT DO NOTHING`,
      [id, roleId, groupId, policyId]
    );
    res.status(201).json({ success: true });
  } catch (err) {
    console.error('[AgentRolePolicies] create error', err);
    res.status(500).json({ error: 'Failed to add role-scoped policy' });
  }
});

// BULK: POST /agents/:id/role-policies/bulk
router.post('/agents/:id/role-policies/bulk', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId, groupId, policyIds = [] } = req.body || {};
    if (!roleId || !groupId || !Array.isArray(policyIds) || policyIds.length === 0) {
      return res.status(400).json({ error: 'roleId, groupId and non-empty policyIds are required' });
    }

    // Validate that all provided policyIds belong to the group
    const { rows: validRows } = await pool.query(
      `SELECT policy_id FROM policy_group_policies WHERE group_id = $1 AND policy_id = ANY($2::uuid[])`,
      [groupId, policyIds]
    );
    const validSet = new Set(validRows.map(r => r.policy_id));
    const invalid = policyIds.filter(p => !validSet.has(p));
    if (invalid.length) {
      return res.status(400).json({ error: 'Some policies are not members of the specified group', invalid });
    }

    await pool.query(
      `INSERT INTO agent_role_policies (agent_id, role_id, group_id, policy_id)
       SELECT $1::uuid, $2::uuid, $3::uuid, UNNEST($4::uuid[])
       ON CONFLICT DO NOTHING`,
      [id, roleId, groupId, policyIds]
    );
    res.status(201).json({ added: validRows.length });
  } catch (err) {
    console.error('[AgentRolePolicies] bulk create error', err);
    res.status(500).json({ error: 'Failed to add role-scoped policies (bulk)' });
  }
});

// DELETE /agents/:id/role-policies
router.delete('/agents/:id/role-policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId, groupId, policyId } = req.body || {};
    if (!roleId || !groupId || !policyId) {
      return res.status(400).json({ error: 'roleId, groupId, and policyId are required' });
    }
    await pool.query(
      `DELETE FROM agent_role_policies
        WHERE agent_id = $1 AND role_id = $2 AND group_id = $3 AND policy_id = $4`,
      [id, roleId, groupId, policyId]
    );
    res.status(204).send();
  } catch (err) {
    console.error('[AgentRolePolicies] delete error', err);
    res.status(500).json({ error: 'Failed to remove role-scoped policy' });
  }
});

// ===================================================================
// UPGRADED FLAT ENDPOINTS - agent-role-policies pattern (going forward)
// ===================================================================

// GET /agent-role-policies/:agentId - List role-scoped policies for an agent
router.get('/agent-role-policies/:agentId', requireAdmin, async (req, res) => {
  try {
    const { agentId } = req.params;
    const { rows } = await pool.query(
      `SELECT arp.agent_id, arp.role_id, r.name AS role_name, r.code AS role_code,
              arp.group_id, pg.name AS group_name, pg.description AS group_description,
              arp.policy_id, p.name AS policy_name, p.description AS policy_description
         FROM agent_role_policies arp
         JOIN roles r ON r.role_id = arp.role_id
         JOIN policy_groups pg ON pg.group_id = arp.group_id 
             AND pg.status = 'active'
         JOIN policies p ON p.policy_id = arp.policy_id 
             AND p.deleted_at IS NULL
             AND p.is_active = true
        WHERE arp.agent_id = $1
        ORDER BY r.name, pg.name, p.name`,
      [agentId]
    );
    res.json(rows);
  } catch (err) {
    console.error('[AgentRolePolicies] flat list error', err);
    res.status(500).json({ error: 'Failed to fetch role-scoped policies' });
  }
});

// POST /agent-role-policies - Add single role-scoped policy to agent
router.post('/agent-role-policies', requireAdmin, async (req, res) => {
  try {
    const { agentId, roleId, groupId, policyId } = req.body || {};
    if (!agentId || !roleId || !groupId || !policyId) {
      return res.status(400).json({ error: 'agentId, roleId, groupId, and policyId are required' });
    }
    
    // Validate group-policy pair exists
    const valid = await pool.query(
      'SELECT 1 FROM policy_group_policies WHERE group_id = $1 AND policy_id = $2',
      [groupId, policyId]
    );
    if (!valid.rows.length) {
      return res.status(400).json({ error: 'Policy is not a member of the specified group' });
    }
    
    await pool.query(
      `INSERT INTO agent_role_policies (agent_id, role_id, group_id, policy_id)
       VALUES ($1, $2, $3, $4)
       ON CONFLICT DO NOTHING`,
      [agentId, roleId, groupId, policyId]
    );
    res.status(201).json({ success: true });
  } catch (err) {
    console.error('[AgentRolePolicies] flat create error', err);
    res.status(500).json({ error: 'Failed to add role-scoped policy' });
  }
});

// POST /agent-role-policies/bulk - Add multiple role-scoped policies to agent
router.post('/agent-role-policies/bulk', requireAdmin, async (req, res) => {
  try {
    const { agentId, roleId, groupId, policyIds = [] } = req.body || {};
    if (!agentId || !roleId || !groupId || !Array.isArray(policyIds) || policyIds.length === 0) {
      return res.status(400).json({ error: 'agentId, roleId, groupId and non-empty policyIds are required' });
    }

    // Validate that all provided policyIds belong to the group
    const { rows: validRows } = await pool.query(
      `SELECT policy_id FROM policy_group_policies WHERE group_id = $1 AND policy_id = ANY($2::uuid[])`,
      [groupId, policyIds]
    );
    const validSet = new Set(validRows.map(r => r.policy_id));
    const invalid = policyIds.filter(p => !validSet.has(p));
    if (invalid.length) {
      return res.status(400).json({ error: 'Some policies are not members of the specified group', invalid });
    }

    await pool.query(
      `INSERT INTO agent_role_policies (agent_id, role_id, group_id, policy_id)
       SELECT $1::uuid, $2::uuid, $3::uuid, UNNEST($4::uuid[])
       ON CONFLICT DO NOTHING`,
      [agentId, roleId, groupId, policyIds]
    );
    res.status(201).json({ added: validRows.length });
  } catch (err) {
    console.error('[AgentRolePolicies] flat bulk create error', err);
    res.status(500).json({ error: 'Failed to add role-scoped policies (bulk)' });
  }
});

// DELETE /agent-role-policies - Remove role-scoped policy from agent
router.delete('/agent-role-policies', requireAdmin, async (req, res) => {
  try {
    const { agentId, roleId, groupId, policyId } = req.body || {};
    if (!agentId || !roleId || !groupId || !policyId) {
      return res.status(400).json({ error: 'agentId, roleId, groupId, and policyId are required' });
    }
    
    await pool.query(
      `DELETE FROM agent_role_policies
        WHERE agent_id = $1 AND role_id = $2 AND group_id = $3 AND policy_id = $4`,
      [agentId, roleId, groupId, policyId]
    );
    res.status(204).send();
  } catch (err) {
    console.error('[AgentRolePolicies] flat delete error', err);
    res.status(500).json({ error: 'Failed to remove role-scoped policy' });
  }
});

export default router;

