import express from 'express';
import { requireAdmin } from '../middleware/auth.js';
import templateGenerationService from '../services/templateGenerationService.js';

const router = express.Router();

/**
 * GET /api/v1/schemas/:name/template
 * Get default template for a schema
 * Implements fallback: database > auto-generate > hardcoded
 */
router.get('/:name/template', async (req, res) => {
    try {
        const { name } = req.params;
        
        const template = await templateGenerationService.getTemplate(name);
        
        if (!template) {
            return res.status(404).json({ 
                error: `No template found for schema: ${name}` 
            });
        }
        
        res.json({
            schema_name: name,
            template: template,
            source: 'database' // Could enhance to return actual source
        });
    } catch (error) {
        console.error(`Error fetching template for ${req.params.name}:`, error);
        res.status(500).json({ 
            error: 'Failed to fetch template',
            details: error.message 
        });
    }
});

/**
 * PUT /api/v1/schemas/:name/template
 * Update template manually (admin override)
 * Body: { template: {...}, source?: 'manual_override' | 'external_provided' }
 */
router.put('/:name/template', requireAdmin, async (req, res) => {
    try {
        const { name } = req.params;
        const { template, source = 'manual_override' } = req.body;
        
        if (!template) {
            return res.status(400).json({ 
                error: 'Template is required in request body' 
            });
        }
        
        // Validate source value
        if (!['manual_override', 'external_provided'].includes(source)) {
            return res.status(400).json({ 
                error: 'Invalid source. Must be manual_override or external_provided' 
            });
        }
        
        const result = await templateGenerationService.updateTemplate(name, template, source);
        
        res.json({
            success: true,
            message: result.message,
            schema_name: name,
            template_source: source
        });
    } catch (error) {
        console.error(`Error updating template for ${req.params.name}:`, error);
        
        if (error.message.includes('not found')) {
            return res.status(404).json({ 
                error: error.message 
            });
        }
        
        res.status(500).json({ 
            error: 'Failed to update template',
            details: error.message 
        });
    }
});

/**
 * DELETE /api/v1/schemas/:name/template
 * Reset template to auto-generated from schema
 * Removes manual override and regenerates from schema definition
 */
router.delete('/:name/template', requireAdmin, async (req, res) => {
    try {
        const { name } = req.params;
        
        const result = await templateGenerationService.resetTemplate(name);
        
        res.json({
            success: true,
            message: result.message,
            schema_name: name
        });
    } catch (error) {
        console.error(`Error resetting template for ${req.params.name}:`, error);
        
        if (error.message.includes('not found')) {
            return res.status(404).json({ 
                error: error.message 
            });
        }
        
        res.status(500).json({ 
            error: 'Failed to reset template',
            details: error.message 
        });
    }
});

/**
 * POST /api/v1/schemas/regenerate-templates
 * Regenerate all auto-generated templates (admin utility)
 * Query params: 
 *   - overrideManual: boolean (default false) - whether to override manual templates
 */
router.post('/regenerate-templates', requireAdmin, async (req, res) => {
    try {
        const { overrideManual = false } = req.query;
        
        const result = await templateGenerationService.regenerateAllTemplates(
            overrideManual === 'true' || overrideManual === true
        );
        
        res.json({
            success: true,
            message: result.message,
            details: result
        });
    } catch (error) {
        console.error('Error regenerating templates:', error);
        res.status(500).json({ 
            error: 'Failed to regenerate templates',
            details: error.message 
        });
    }
});

/**
 * GET /api/v1/schemas/templates/status
 * Get status of all schema templates
 * Returns list of schemas with their template sources
 */
router.get('/templates/status', requireAdmin, async (req, res) => {
    try {
        const { pool } = await import('../db.js');
        
        const result = await pool.query(`
            SELECT 
                schema_name,
                CASE 
                    WHEN default_template IS NOT NULL THEN 'present'
                    ELSE 'missing'
                END as template_status,
                template_source,
                updated_at
            FROM policy_schemas
            WHERE is_active = true
            ORDER BY schema_name
        `);
        
        const summary = {
            total: result.rows.length,
            with_templates: result.rows.filter(r => r.template_status === 'present').length,
            without_templates: result.rows.filter(r => r.template_status === 'missing').length,
            by_source: {}
        };
        
        // Count by source
        result.rows.forEach(row => {
            const source = row.template_source || 'none';
            summary.by_source[source] = (summary.by_source[source] || 0) + 1;
        });
        
        res.json({
            summary,
            schemas: result.rows
        });
    } catch (error) {
        console.error('Error fetching template status:', error);
        res.status(500).json({ 
            error: 'Failed to fetch template status',
            details: error.message 
        });
    }
});

export default router;