import express from 'express';
import { recordAudit } from '../utils/auditLogger.js';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

const router = express.Router();

/**
 * POST /bulk-policy-assign
 * Body: {
 *   policyIds: [uuid],          // optional
 *   groupIds:  [uuid],          // optional – policy groups
 *   targetAgentIds: [uuid]      // required – agents to receive assignments
 * }
 * Creates agent_policies rows with link_type = 'direct' (for policies)
 * and 'via_group' (for group policies). Duplicate conflicts ignored.
 */
router.post('/', requireAdmin, async (req, res) => {
  const { policyIds = [], groupIds = [], targetAgentIds = [] } = req.body;
  if (!targetAgentIds.length) {
    return res.status(400).json({ error: 'targetAgentIds required' });
  }

  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // direct policies
    if (policyIds.length) {
      await client.query(
        `INSERT INTO agent_policies (agent_id, policy_id, link_type)
         SELECT a, p, 'direct'::link_type_enum
           FROM unnest($1::uuid[]) a, unnest($2::uuid[]) p
         ON CONFLICT DO NOTHING`,
        [targetAgentIds, policyIds]
      );
    }

    // via group – resolve group→policy links first
    if (groupIds.length) {
      const { rows } = await client.query(
        'SELECT group_id, policy_id FROM policy_group_policies WHERE group_id = ANY($1::uuid[])',
        [groupIds]
      );
      const policyPairs = rows; // array of {group_id, policy_id}
      for (const { policy_id } of policyPairs) {
        await client.query(
          `INSERT INTO agent_policies (agent_id, policy_id, link_type)
           SELECT a, $1, 'via_group'::link_type_enum FROM unnest($2::uuid[]) a
           ON CONFLICT DO NOTHING`,
          [policy_id, targetAgentIds]
        );
      }
    }

    await client.query('COMMIT');
    await recordAudit({ userId: req.user.id, action: 'BULK_POLICY_ASSIGN', resourceType: 'agent', resourceId: null, newValues: { policyIds, groupIds, targetAgentIds } });
    res.status(201).json({ message: 'Assignments created' });
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('[BulkAssign] error', err);
    res.status(500).json({ error: 'Bulk assignment failed' });
  } finally {
    client.release();
  }
});

export default router;
