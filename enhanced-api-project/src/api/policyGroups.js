import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';
import { recordAudit } from '../utils/auditLogger.js';

const router = express.Router();

// LIST policy groups
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { status = 'active' } = req.query;
    let sql = `
      SELECT 
        pg.*,
        COALESCE(COUNT(pgp.policy_id) FILTER (WHERE p.deleted_at IS NULL AND p.is_active = true), 0)::INT as uses_count
      FROM policy_groups pg
      LEFT JOIN policy_group_policies pgp ON pgp.group_id = pg.group_id
      LEFT JOIN policies p ON p.policy_id = pgp.policy_id
    `;
    const params = [];
    if (status !== 'all') {
      params.push(status);
      sql += ' WHERE pg.status = $1';
    }
    sql += ' GROUP BY pg.group_id ORDER BY pg.name';
    const { rows } = await pool.query(sql, params);
    res.json(rows);
  } catch (err) {
    console.error('[PolicyGroups] list error', err);
    res.status(500).json({ error: 'Failed to fetch policy groups' });
  }
});

// CREATE a new policy group
router.post('/', requireAdmin, async (req, res) => {
  try {
    const { name, description = null, is_template = false, severity = 'medium', tags = [] } = req.body;
    if (!name) return res.status(400).json({ error: 'name is required' });

    const { rows } = await pool.query(
      `INSERT INTO policy_groups (name, description, is_template, severity, tags)
       VALUES ($1,$2,$3,$4,$5)
       RETURNING *`,
      [name, description, is_template, severity, tags]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'CREATE',
      resourceType: 'policy_group',
      resourceId: rows[0].group_id,
      newValues: rows[0]
    });

    res.status(201).json(rows[0]);
  } catch (err) {
    if (err.code === '23505') {
      return res.status(409).json({ error: 'Group name already exists' });
    }
    console.error('[PolicyGroups] create error', err);
    res.status(500).json({ error: 'Failed to create policy group' });
  }
});

// UPDATE an existing policy group
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_template, severity, tags } = req.body;
    const { rows: currentRows } = await pool.query('SELECT * FROM policy_groups WHERE group_id=$1', [id]);
    if (!currentRows.length) return res.status(404).json({ error: 'Group not found' });
    const oldValues = currentRows[0];

    const { rows } = await pool.query(
      `UPDATE policy_groups
         SET name = COALESCE($2,name),
             description = COALESCE($3,description),
             is_template = COALESCE($4,is_template),
             severity = COALESCE($5,severity),
             tags = COALESCE($6,tags),
             updated_at = NOW()
       WHERE group_id = $1
       RETURNING *`,
      [id, name, description, is_template, severity, tags]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'UPDATE',
      resourceType: 'policy_group',
      resourceId: id,
      oldValues,
      newValues: rows[0]
    });

    res.json(rows[0]);
  } catch (err) {
    console.error('[PolicyGroups] update error', err);
    res.status(500).json({ error: 'Failed to update policy group' });
  }
});

// SOFT DELETE (deprecate) a policy group
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    await pool.query(
      `UPDATE policy_groups SET status='deprecated', updated_at=NOW() WHERE group_id=$1`,
      [id]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'DEPRECATE',
      resourceType: 'policy_group',
      resourceId: id
    });

    res.status(204).end();
  } catch (err) {
    console.error('[PolicyGroups] deprecate error', err);
    res.status(500).json({ error: 'Failed to deprecate policy group' });
  }
});

// RESTORE a deprecated group back to active
router.post('/:id/restore', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    await pool.query(
      `UPDATE policy_groups SET status='active', updated_at=NOW() WHERE group_id=$1`,
      [id]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'RESTORE',
      resourceType: 'policy_group',
      resourceId: id
    });

    res.status(204).end();
  } catch (err) {
    console.error('[PolicyGroups] restore error', err);
    res.status(500).json({ error: 'Failed to restore policy group' });
  }
});

// ADD policies to a group
router.post('/:id/policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { policyIds = [] } = req.body;
    if (!policyIds.length) return res.status(400).json({ error: 'policyIds required' });

    await pool.query(
      `INSERT INTO policy_group_policies (group_id, policy_id)
       SELECT $1::uuid, UNNEST($2::uuid[])
       ON CONFLICT DO NOTHING`,
      [id, policyIds]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'ADD_POLICY',
      resourceType: 'policy_group',
      resourceId: id,
      newValues: { addedPolicyIds: policyIds }
    });

    res.status(201).json({ added: policyIds.length });
  } catch (err) {
    console.error('[PolicyGroups] add policies error', err);
    res.status(500).json({ error: 'Failed to add policies to group' });
  }
});

// REMOVE a single policy from a group
router.delete('/:id/policies/:policyId', requireAdmin, async (req, res) => {
  try {
    const { id, policyId } = req.params;
    await pool.query(
      `DELETE FROM policy_group_policies WHERE group_id=$1 AND policy_id=$2`,
      [id, policyId]
    );

    await recordAudit({
      userId: req.user?.user_id || '00000000-0000-0000-0000-000000000000',
      action: 'REMOVE_POLICY',
      resourceType: 'policy_group',
      resourceId: id,
      oldValues: { policyId }
    });

    res.status(204).end();
  } catch (err) {
    console.error('[PolicyGroups] remove policy error', err);
    res.status(500).json({ error: 'Failed to remove policy from group' });
  }
});

// LIST policies in a group
router.get('/:id/policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { include_inactive = 'false' } = req.query;
    
    let sql = `SELECT p.*
         FROM policies p
         JOIN policy_group_policies pgp ON pgp.policy_id = p.policy_id
        WHERE pgp.group_id = $1
          AND p.deleted_at IS NULL`;
    
    if (include_inactive !== 'true') {
      sql += ' AND p.is_active = true';
    }
    
    sql += ' ORDER BY p.is_active DESC, p.name';
    
    const { rows } = await pool.query(sql, [id]);
    res.json(rows);
  } catch (err) {
    console.error('[PolicyGroups] list policies in group error', err);
    res.status(500).json({ error: 'Failed to fetch policies in group' });
  }
});

// NEW: list groups linked to a policy
router.get('/policy/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rows } = await pool.query(
      `SELECT pg.*
         FROM policy_groups pg
         JOIN policy_group_policies pgp ON pgp.group_id = pg.group_id
        WHERE pgp.policy_id = $1`,
      [id]
    );
    res.json(rows);
  } catch (err) {
    console.error('[PolicyGroups] list by policy error', err);
    res.status(500).json({ error: 'Failed to fetch groups for policy' });
  }
});

export default router;