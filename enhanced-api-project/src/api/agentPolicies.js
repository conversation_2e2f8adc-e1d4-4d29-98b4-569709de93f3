import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// helper query to fetch consolidated policies for an agent
const CONSOLIDATED_SQL = `
WITH direct AS (
  SELECT p.*, 'Direct' AS assignment_type
    FROM policies p
    JOIN agent_policies ap ON ap.policy_id = p.policy_id
   WHERE ap.agent_id = $1 AND ap.link_type = 'direct'
     AND p.deleted_at IS NULL
),
via_group AS (
  SELECT DISTINCT p.*, 'Via ' || pg.name AS assignment_type
    FROM policies p
    JOIN policy_group_policies pgp ON pgp.policy_id = p.policy_id
    JOIN policy_groups pg ON pg.group_id = pgp.group_id AND pg.status = 'active'
    JOIN agent_policies ap ON ap.policy_id = p.policy_id AND ap.agent_id = $1 AND ap.link_type = 'via_group'
   WHERE p.deleted_at IS NULL
)
SELECT * FROM (
  SELECT * FROM direct
  UNION
  SELECT * FROM via_group
) consolidated ORDER BY name;`;

// GET /agents/:id/policies – consolidated list with assignmentType
router.get('/:id/policies', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // If the user has an explicit allow-list of agents, enforce it.
    // An empty allowedAgents list means unrestricted admin access.
    if (req.user.allowedAgents.length && !req.user.allowedAgents.includes(id)) {
      return res.status(403).json({ error: 'Access denied to agent' });
    }
    const { rows } = await pool.query(CONSOLIDATED_SQL, [id]);
    res.json(rows);
  } catch (err) {
    console.error('[AgentPolicies] list error', err);
    res.status(500).json({ error: 'Failed to fetch policies for agent' });
  }
});

export default router;
