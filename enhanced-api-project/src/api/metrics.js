import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get system metrics for dashboard
router.get('/', requireAdmin, async (req, res) => {
  try {
    // Get historical metrics (if any)
    const metricsResult = await pool.query(
      'SELECT * FROM system_metrics ORDER BY recorded_at DESC'
    );
    
    // Calculate real-time stats
    const statsQueries = await Promise.all([
      pool.query('SELECT COUNT(*) as total_policies FROM policies'),
      pool.query('SELECT COUNT(*) as active_policies FROM policies WHERE is_active = true'),
      pool.query("SELECT COUNT(*) as critical_policies FROM policies WHERE severity = 'critical'"),
      pool.query('SELECT COUNT(*) as total_violations FROM policy_violations'),
      pool.query('SELECT COUNT(*) as unresolved_violations FROM policy_violations WHERE resolved = false'),
      pool.query('SELECT COUNT(*) as chat_sessions_today FROM mcp_chat_sessions WHERE created_at >= CURRENT_DATE'),
    ]);
    
    const stats = {
      total_policies: parseInt(statsQueries[0].rows[0].total_policies),
      active_policies: parseInt(statsQueries[1].rows[0].active_policies),
      critical_policies: parseInt(statsQueries[2].rows[0].critical_policies),
      total_violations: parseInt(statsQueries[3].rows[0].total_violations),
      unresolved_violations: parseInt(statsQueries[4].rows[0].unresolved_violations),
      chat_sessions_today: parseInt(statsQueries[5].rows[0].chat_sessions_today)
    };
    
    res.json({
      metrics: metricsResult.rows,
      current_stats: stats,
      last_updated: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching metrics:', err);
    res.status(500).json({ error: 'Failed to fetch metrics' });
  }
});

router.get('/group-stats', requireAdmin, async (req, res) => {
  try {
    const [policiesPerGroup, agentsPerGroup] = await Promise.all([
      pool.query(`
        SELECT pg.id, pg.name, COUNT(pgp.policy_id)::INT AS policy_count
        FROM policy_groups pg
        LEFT JOIN policy_group_policies pgp ON pgp.group_id = pg.id AND pgp.deleted_at IS NULL
        WHERE pg.deleted_at IS NULL AND pg.status = 'active'
        GROUP BY pg.id, pg.name
        ORDER BY policy_count DESC;
      `),
      pool.query(`
        SELECT pg.id, pg.name, COUNT(aa.agent_id)::INT AS agent_count
        FROM policy_groups pg
        LEFT JOIN agent_access aa ON aa.group_id = pg.id AND aa.deleted_at IS NULL
        WHERE pg.deleted_at IS NULL AND pg.status = 'active'
        GROUP BY pg.id, pg.name
        ORDER BY agent_count DESC;
      `)
    ]);

    res.json({
      policies_per_group: policiesPerGroup.rows,
      agents_per_group: agentsPerGroup.rows,
      last_updated: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching group stats:', err);
    res.status(500).json({ error: 'Failed to fetch group stats' });
  }
});

router.get('/orphaned-policies', requireAdmin, async (req, res) => {
  try {
    const orphanedPolicies = await pool.query(`
      SELECT p.id, p.name
      FROM policies p
      LEFT JOIN policy_group_policies pgp ON pgp.policy_id = p.id AND pgp.deleted_at IS NULL
      WHERE pgp.policy_id IS NULL AND p.deleted_at IS NULL
      ORDER BY p.name;
    `);

    res.json({
      orphaned_policies: orphanedPolicies.rows,
      count: orphanedPolicies.rowCount,
      last_updated: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching orphaned policies:', err);
    res.status(500).json({ error: 'Failed to fetch orphaned policies' });
  }
});

export default router; 