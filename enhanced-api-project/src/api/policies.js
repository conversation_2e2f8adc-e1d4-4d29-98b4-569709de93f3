import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';
import { validatePolicyMiddleware, generateDefaultPolicy, getAvailablePolicyTypes } from '../utils/schemaValidator.js';

const router = express.Router();

// Get policies; supports optional agentId/groupId filters to include assignmentType
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { active, category, severity, search, agentId, groupId, limit = 20, offset = 0 } = req.query;
    
    // Branch: agent + group → intersection (policies assigned to agent via specific group)
    if (agentId && groupId) {
      const result = await pool.query(
        `SELECT DISTINCT p.*, 
                'Via ' || r.name || ' (' || pg.name || ')' AS assignment_type
           FROM policies p
           JOIN agent_role_policies arp ON arp.policy_id = p.policy_id
           JOIN roles r ON r.role_id = arp.role_id
           JOIN policy_groups pg ON pg.group_id = arp.group_id 
               AND pg.status = 'active'
          WHERE arp.agent_id = $1
            AND arp.group_id = $2
            AND p.deleted_at IS NULL
            AND p.is_active = true
          ORDER BY p.name`,
        [agentId, groupId]
      );
      return res.json(result.rows);
    }

    // Branch: by agent → policies assigned via agent_role_policies
    if (agentId) {
      const result = await pool.query(
        `SELECT DISTINCT p.*, 
                'Via ' || r.name || ' (' || pg.name || ')' AS assignment_type
           FROM policies p
           JOIN agent_role_policies arp ON arp.policy_id = p.policy_id
           JOIN roles r ON r.role_id = arp.role_id
           JOIN policy_groups pg ON pg.group_id = arp.group_id 
               AND pg.status = 'active'
          WHERE arp.agent_id = $1
            AND p.deleted_at IS NULL
            AND p.is_active = true
          ORDER BY p.name`,
        [agentId]
      );
      return res.json(result.rows);
    }

    // Branch: by group → policies in group, assignmentType = Via <group>
    if (groupId) {
      const result = await pool.query(
        `SELECT p.*, 'Via ' || pg.name AS assignment_type
           FROM policies p
           JOIN policy_group_policies pgp ON pgp.policy_id = p.policy_id
           JOIN policy_groups pg ON pg.group_id = pgp.group_id
          WHERE pg.group_id = $1
            AND p.deleted_at IS NULL
            AND p.is_active = true`,
        [groupId]
      );
      return res.json(result.rows);
    }

    // default search without assignmentType
    const result = await pool.query(
      'SELECT * FROM search_policies($1, $2, $3, $4, $5, $6)',
      [search || null, category || null, severity || null, active === undefined ? null : active === 'true', parseInt(limit), parseInt(offset)]
    );

    // Enrich with policy group names in ONE extra query for efficiency
    const policies = result.rows;
    if (policies.length) {
      const policyIds = policies.map((p) => p.policy_id);
      const { rows: groupRows } = await pool.query(
        `SELECT pgp.policy_id, pg.name AS group_name
           FROM policy_group_policies pgp
           JOIN policy_groups pg ON pg.group_id = pgp.group_id 
               AND pg.deleted_at IS NULL 
               AND pg.status = 'active'
          WHERE pgp.policy_id = ANY($1::uuid[])`,
        [policyIds]
      );
      const groupMap = {};
      for (const { policy_id, group_name } of groupRows) {
        if (!groupMap[policy_id]) groupMap[policy_id] = [];
        groupMap[policy_id].push(group_name);
      }
      policies.forEach((p) => {
        p.policy_groups = groupMap[p.policy_id] || [];
      });
    }

    res.json({
      policies,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: policies.length > 0 ? policies[0].total_count : 0
      }
    });
  } catch (err) {
    console.error('Error fetching policies:', err);
    res.status(500).json({ error: 'Failed to fetch policies' });
  }
});

// Create new policy with enhanced validation and audit logging
router.post('/', requireAdmin, validatePolicyMiddleware, async (req, res) => {
  try {
    const { name, description, category, definition, severity, applies_to_roles, is_active, policy_type, original_policy_id = null, groupIds = [] } = req.body;

    // Validate required fields
    if (!name || !category || !severity) {
      return res.status(400).json({ error: 'Name, category, and severity are required' });
    }

    // Check for duplicate names (excluding soft-deleted)
    const duplicateCheck = await pool.query(
      'SELECT policy_id FROM policies WHERE name = $1 AND deleted_at IS NULL',
      [name]
    );
    
    if (duplicateCheck.rows.length > 0) {
      return res.status(409).json({ error: 'Policy with this name already exists' });
    }

    const result = await pool.query(
      `INSERT INTO policies (name, description, category, definition, severity, applies_to_roles, created_by, is_active, policy_type, original_policy_id) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *`,
      [name, description, category, definition, severity, applies_to_roles, req.user.id, is_active, policy_type, original_policy_id]
    );

    // Link to policy groups if provided
    if (Array.isArray(groupIds) && groupIds.length > 0) {
      await pool.query(
        'INSERT INTO policy_group_policies (group_id, policy_id) SELECT gid, $2::uuid FROM unnest($1::uuid[]) AS t(gid) ON CONFLICT DO NOTHING',
        [groupIds, result.rows[0].policy_id]
      );
    }

    // Log the policy creation event
    await pool.query(
      'SELECT log_hipaa_audit_event($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)',
      [
        req.user.id,
        'POLICY_CREATED',
        'policy',
        result.rows[0].policy_id,
        null,
        JSON.stringify({ name, category, severity, is_active }),
        req.headers['x-session-id'] || null,
        req.headers['x-request-id'] || null,
        req.user.role || 'admin',
        'policy_creation',
        'write',
        'sensitive'
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating policy:', err);
    res.status(500).json({ error: 'Failed to create policy' });
  }
});

// Update policy with enhanced validation and audit logging
router.put('/:id', requireAdmin, validatePolicyMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, category, definition, is_active, severity, applies_to_roles, policy_type, groupIds } = req.body;
    
    // Get the original policy for audit logging
    const originalPolicy = await pool.query(
      'SELECT * FROM policies WHERE policy_id = $1 AND deleted_at IS NULL',
      [id]
    );
    
    if (originalPolicy.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    // Enhanced JSON validation
    if (!definition || typeof definition !== 'object') {
      return res.status(400).json({ error: 'Policy definition must be a valid JSON object' });
    }
    
    // Validate required fields
    if (!name || !category || !severity) {
      return res.status(400).json({ error: 'Name, category, and severity are required' });
    }
    
    // Check for duplicate names (excluding current policy)
    const duplicateCheck = await pool.query(
      'SELECT policy_id FROM policies WHERE name = $1 AND policy_id != $2 AND deleted_at IS NULL',
      [name, id]
    );
    
    if (duplicateCheck.rows.length > 0) {
      return res.status(409).json({ error: 'Policy with this name already exists' });
    }
    
    const result = await pool.query(
      `UPDATE policies SET name = $1, description = $2, category = $3, definition = $4, 
       is_active = $5, severity = $6, applies_to_roles = $7, updated_by = $8, updated_at = CURRENT_TIMESTAMP, policy_type = $9
       WHERE policy_id = $10 RETURNING *`,
      [name, description, category, definition, is_active, severity, applies_to_roles, req.user.id, policy_type, id]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    // Sync policy_group links ONLY if groupIds is explicitly provided as a valid array
    // This ensures that status toggles don't affect group memberships
    if (groupIds !== undefined && groupIds !== null && Array.isArray(groupIds)) {
      // Remove links not in groupIds
      if (groupIds.length === 0) {
        // If explicitly empty array, remove all links
        await pool.query('DELETE FROM policy_group_policies WHERE policy_id = $1', [id]);
      } else {
        // Remove any links not in new set
        await pool.query('DELETE FROM policy_group_policies WHERE policy_id = $1 AND group_id <> ALL($2::uuid[])', [id, groupIds]);
        // Add missing links
        await pool.query('INSERT INTO policy_group_policies (group_id, policy_id) SELECT gid, $2::uuid FROM unnest($1::uuid[]) AS t(gid) ON CONFLICT DO NOTHING', [groupIds, id]);
      }
    }
    // If groupIds is undefined, null, or not an array, preserve existing policy group associations

    // Log the policy update event
    await pool.query(
      'SELECT log_hipaa_audit_event($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)',
      [
        req.user.id,
        'POLICY_UPDATED',
        'policy',
        id,
        JSON.stringify(originalPolicy.rows[0]),
        JSON.stringify(result.rows[0]),
        req.headers['x-session-id'] || null,
        req.headers['x-request-id'] || null,
        req.user.role || 'admin',
        'policy_update',
        'write',
        'sensitive'
      ]
    );
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error updating policy:', err);
    res.status(500).json({ error: 'Failed to update policy' });
  }
});

// Delete policy
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Soft-delete: set deleted_at timestamp
    const result = await pool.query(
      'UPDATE policies SET deleted_at = CURRENT_TIMESTAMP WHERE policy_id = $1 AND deleted_at IS NULL RETURNING *',
      [id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found or already deleted' });
    }
    res.json({ message: 'Policy soft-deleted successfully' });
  } catch (err) {
    console.error('Error soft-deleting policy:', err);
    res.status(500).json({ error: 'Failed to delete policy' });
  }
});

// Generate Rego for a policy
router.post('/:id/generate-rego', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Optionally: force_regenerate, template_override from req.body
    const result = await pool.query('SELECT generate_rego_for_policy($1) AS rego', [id]);
    const regoResult = result.rows[0]?.rego;
    if (!regoResult?.success) {
      return res.status(400).json({ error: regoResult?.error || 'Rego generation failed' });
    }
    res.json(regoResult);
  } catch (err) {
    console.error('Error generating rego:', err);
    res.status(500).json({ error: 'Failed to generate rego' });
  }
});

// Bulk Rego generation
router.post('/generate-rego/bulk', requireAdmin, async (req, res) => {
  try {
    const { policy_ids = [] } = req.body;
    const results = [];
    for (const id of policy_ids) {
      const result = await pool.query('SELECT generate_rego_for_policy($1) AS rego', [id]);
      results.push({ id, ...result.rows[0]?.rego });
    }
    res.json(results);
  } catch (err) {
    console.error('Error in bulk rego generation:', err);
    res.status(500).json({ error: 'Bulk rego generation failed' });
  }
});

// Get generated rego code for a policy (from database, not blob)
router.get('/:id/rego-code', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT rego_code FROM policies WHERE policy_id = $1', [id]);
    const regoCode = result.rows[0]?.rego_code;
    if (!regoCode) {
      return res.status(404).json({ error: 'No generated rego code found for this policy.' });
    }
    res.json({ regoCode });
  } catch (err) {
    console.error('Error fetching rego code:', err);
    res.status(500).json({ error: 'Failed to fetch rego code' });
  }
});

// Get Rego status for a policy
router.get('/:id/rego-status', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT policy_id, opa_sync_status, last_rego_generation, rego_generation_error FROM policies WHERE policy_id = $1', [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error fetching rego status:', err);
    res.status(500).json({ error: 'Failed to fetch rego status' });
  }
});

// Rollback Rego generation for a policy
router.post('/:id/rollback-rego', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT rollback_rego_generation($1) AS success', [id]);
    if (!result.rows[0]?.success) {
      return res.status(400).json({ error: 'Rollback failed' });
    }
    res.json({ success: true });
  } catch (err) {
    console.error('Error rolling back rego:', err);
    res.status(500).json({ error: 'Failed to rollback rego' });
  }
});

// Get policy violations
router.get('/violations', requireAdmin, async (req, res) => {
  try {
    const { policy_id, resolved, limit = 50 } = req.query;
    
    let query = `
      SELECT pv.*, p.name as policy_name, u.first_name, u.last_name, u.email
      FROM policy_violations pv
      LEFT JOIN policies p ON pv.policy_id = p.policy_id
      LEFT JOIN users u ON pv.user_id = u.user_id
      WHERE 1=1
    `;
    const params = [];
    
    if (policy_id) {
      query += ` AND pv.policy_id = $${params.length + 1}`;
      params.push(policy_id);
    }
    
    if (resolved !== undefined) {
      query += ` AND pv.resolved = $${params.length + 1}`;
      params.push(resolved === 'true');
    }
    
    query += ` ORDER BY pv.detected_at DESC LIMIT $${params.length + 1}`;
    params.push(parseInt(limit));
    
    const result = await pool.query(query, params);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching violations:', err);
    res.status(500).json({ error: 'Failed to fetch violations' });
  }
});

// Get policy templates
// DEPRECATED: Templates are now managed through policy_schemas table
router.get('/templates', requireAdmin, async (req, res) => {
  try {
    // Log deprecation warning
    console.warn('DEPRECATED: /api/v1/policies/templates called. Templates are now managed through policy_schemas');
    
    // Add deprecation warning to response headers
    res.set('X-Deprecated', 'true');
    res.set('X-Deprecation-Notice', 'This endpoint is deprecated. Templates are now managed through /api/v1/schemas/:name/template');
    
    const result = await pool.query(
      'SELECT * FROM policy_templates ORDER BY is_system_template DESC, created_at DESC'
    );
    res.json({
      templates: result.rows,
      _deprecated: true,
      _message: 'This endpoint is deprecated. Templates are now managed through policy_schemas table',
      _use_instead: '/api/v1/schemas/templates/status for template status'
    });
  } catch (err) {
    console.error('Error fetching policy templates:', err);
    // If table doesn't exist, return empty array with deprecation notice
    if (err.code === '42P01') { // undefined_table error code
      res.json({
        templates: [],
        _deprecated: true,
        _message: 'policy_templates table has been removed. Use /api/v1/schemas/:name/template instead'
      });
    } else {
      res.status(500).json({ error: 'Failed to fetch policy templates' });
    }
  }
});

// Get policy template by category
// DEPRECATED: Use /api/v1/schemas/:name/template instead
router.get('/templates/:category', requireAdmin, async (req, res) => {
  try {
    const { category } = req.params;
    
    // Log deprecation warning
    console.warn(`DEPRECATED: /api/v1/policies/templates/${category} called. Use /api/v1/schemas/${category}/template instead`);
    
    // Generate schema-based template (now async)
    const template = await generateDefaultPolicy(category);
    
    if (!template || Object.keys(template).length === 0) {
      return res.status(404).json({ error: 'No template found for this category' });
    }
    
    // Add deprecation warning to response headers
    res.set('X-Deprecated', 'true');
    res.set('X-Deprecation-Notice', 'This endpoint is deprecated. Use /api/v1/schemas/:name/template instead');
    
    res.json({
      template_id: `schema_${category}`,
      name: `${category.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Template`,
      description: `Schema-based template for ${category} policies`,
      template_definition: template,
      _deprecated: true,
      _use_instead: `/api/v1/schemas/${category}/template`
    });
  } catch (err) {
    console.error('Error fetching policy template by category:', err);
    res.status(500).json({ error: 'Failed to fetch policy template' });
  }
});

// Clone a policy
router.post('/:id/clone', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { new_name, new_description } = req.body;
    
    if (!new_name) {
      return res.status(400).json({ error: 'New policy name is required' });
    }
    
    // Check for duplicate names
    const duplicateCheck = await pool.query(
      'SELECT policy_id FROM policies WHERE name = $1 AND deleted_at IS NULL',
      [new_name]
    );
    
    if (duplicateCheck.rows.length > 0) {
      return res.status(409).json({ error: 'Policy with this name already exists' });
    }
    
    const result = await pool.query(
      'SELECT clone_policy($1, $2, $3, $4) as new_policy_id',
      [id, new_name, new_description || null, req.user.id]
    );
    
    if (!result.rows[0]?.new_policy_id) {
      return res.status(400).json({ error: 'Failed to clone policy' });
    }
    
    // Get the cloned policy details
    const clonedPolicy = await pool.query(
      'SELECT * FROM policies WHERE policy_id = $1',
      [result.rows[0].new_policy_id]
    );
    
    res.status(201).json(clonedPolicy.rows[0]);
  } catch (err) {
    console.error('Error cloning policy:', err);
    res.status(500).json({ error: 'Failed to clone policy' });
  }
});

// Get available policy categories
router.get('/categories', requireAdmin, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT DISTINCT category FROM policies WHERE deleted_at IS NULL ORDER BY category'
    );
    res.json(result.rows.map(row => row.category));
  } catch (err) {
    console.error('Error fetching policy categories:', err);
    res.status(500).json({ error: 'Failed to fetch policy categories' });
  }
});

// Get available policy types from schema
router.get('/types', requireAdmin, async (req, res) => {
  try {
    const policyTypes = getAvailablePolicyTypes();
    res.json(policyTypes);
  } catch (err) {
    console.error('Error fetching policy types:', err);
    res.status(500).json({ error: 'Failed to fetch policy types' });
  }
});

// Get Rego file from Blob Storage
router.get('/:id/blob', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Get blob_path from DB
    const result = await pool.query('SELECT blob_path FROM policies WHERE policy_id = $1', [id]);
    if (result.rows.length === 0 || !result.rows[0].blob_path) {
      return res.status(404).json({ error: 'Blob path not found for policy' });
    }
    const { getRegoFile } = await import('../blob-services/azureBlobService.js');
    const regoContent = await getRegoFile(result.rows[0].blob_path);
    res.type('text/plain').send(regoContent);
  } catch (err) {
    console.error('Error retrieving Rego file from Blob:', err);
    res.status(500).json({ error: 'Failed to retrieve Rego file from Blob' });
  }
});

// Delete Rego file from Blob Storage
router.delete('/:id/blob', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Get blob_path from DB
    const result = await pool.query('SELECT blob_path FROM policies WHERE policy_id = $1', [id]);
    if (result.rows.length === 0 || !result.rows[0].blob_path) {
      return res.status(404).json({ error: 'Blob path not found for policy' });
    }
    const { deleteRegoFile } = await import('../blob-services/azureBlobService.js');
    await deleteRegoFile(result.rows[0].blob_path);
    // Optionally update DB to clear blob_path/blob_url
    await pool.query('UPDATE policies SET blob_path = NULL, blob_url = NULL, opa_sync_status = $2 WHERE policy_id = $1', [id, 'deleted']);
    res.json({ success: true });
  } catch (err) {
    console.error('Error deleting Rego file from Blob:', err);
    res.status(500).json({ error: 'Failed to delete Rego file from Blob' });
  }
});

// Upload Rego file to Blob Storage
router.post('/:id/upload-blob', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { regoCode } = req.body;
    if (!regoCode) {
      return res.status(400).json({ error: 'regoCode is required in request body' });
    }
    // Get blob_path from DB (or generate)
    const result = await pool.query('SELECT blob_path FROM policies WHERE policy_id = $1', [id]);
    let blobPath = result.rows[0]?.blob_path;
    if (!blobPath) {
      // Generate default blob path if not present
      blobPath = `active/policy_${id}_manual_upload.rego`;
    }
    const { uploadRegoFile } = await import('../blob-services/azureBlobService.js');
    const blobUrl = await uploadRegoFile(id, regoCode, blobPath);
    // Update DB with blob_url and status
    await pool.query('UPDATE policies SET blob_path = $2, blob_url = $3, opa_sync_status = $4 WHERE policy_id = $1', [id, blobPath, blobUrl, 'uploaded']);
    res.json({ success: true, blobUrl });
  } catch (err) {
    console.error('Error uploading Rego file to Blob:', err);
    res.status(500).json({ error: 'Failed to upload Rego file to Blob' });
  }
});

// Bundle all active rego files into a .tar.gz archive
import tar from 'tar-stream';
import zlib from 'zlib';

router.post('/bundle-rego', requireAdmin, async (req, res) => {
  try {
    // Get all active policies with a rego blob
    const result = await pool.query('SELECT policy_id, blob_path FROM policies WHERE is_active = true AND blob_path IS NOT NULL');
    const policies = result.rows;
    if (policies.length === 0) {
      return res.status(404).json({ error: 'No active rego files found to bundle.' });
    }

    // Prepare tar archive in memory
    const pack = tar.pack();
    for (const policy of policies) {
      try {
        const { getRegoFile } = await import('../blob-services/azureBlobService.js');
        const regoContent = await getRegoFile(policy.blob_path);
        const filename = `policy_${policy.policy_id}.rego`;
        pack.entry({ name: filename }, regoContent);
      } catch (err) {
        // Optionally skip or fail on missing file
        console.error(`Error fetching rego for policy ${policy.policy_id}:`, err);
      }
    }
    pack.finalize();

    // Pipe tar to gzip and send as response with timestamped filename
    const now = new Date();
    const pad = (n) => n.toString().padStart(2, '0');
    const timestamp = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const bundleFilename = `rego_bundle_${timestamp}.tar.gz`;
    res.setHeader('Content-Type', 'application/gzip');
    res.setHeader('Content-Disposition', `attachment; filename="${bundleFilename}"`);
    const gzip = zlib.createGzip();
    pack.pipe(gzip).pipe(res);
  } catch (err) {
    console.error('Error bundling rego files:', err);
    res.status(500).json({ error: 'Failed to bundle rego files.' });
  }
});

export default router;
 
// ===== Policy assignments (agents and roles) for a given policy =====
// GET /api/v1/policies/:id/assignments
router.get('/:id/assignments', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Fetch policy header info (includes version, status, severity)
    const policyResult = await pool.query(
      `SELECT policy_id, name, description, version, is_active, severity
         FROM policies
        WHERE policy_id = $1 AND deleted_at IS NULL`,
      [id]
    );
    if (policyResult.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }

    // Fetch agent-role assignments for this policy using the new agent_role_policies table.
    // This table directly links agents, roles, policy groups, and policies.
    const assignmentsResult = await pool.query(
      `SELECT 
          a.agent_id,
          a.name AS agent_name,
          a.description AS agent_description,
          a.agent_type,
          a.is_active AS agent_is_active,
          r.role_id,
          r.name AS role_name,
          r.description AS role_description,
          pg.group_id,
          pg.name AS group_name,
          'Via Role & Group' AS assignment_type
       FROM agent_role_policies arp
       JOIN agents a ON a.agent_id = arp.agent_id
       JOIN roles r ON r.role_id = arp.role_id
       JOIN policy_groups pg ON pg.group_id = arp.group_id
      WHERE arp.policy_id = $1
      ORDER BY a.name ASC, r.name ASC`,
      [id]
    );

    res.json({ policy: policyResult.rows[0], assignments: assignmentsResult.rows });
  } catch (err) {
    console.error('Error fetching policy assignments:', err);
    res.status(500).json({ error: 'Failed to fetch policy assignments' });
  }
});