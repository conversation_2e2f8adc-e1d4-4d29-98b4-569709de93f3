import express from 'express';
import { pool } from '../db.js';
import { requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// GET / - list agents (optionally filter by status)
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { status = 'active' } = req.query;
    let sql = 'SELECT * FROM agents';
    const params = [];
    if (status !== 'all') {
      params.push(status);
      sql += ' WHERE status = $1';
    }
    sql += ' ORDER BY name';
    const { rows } = await pool.query(sql, params);
    res.json(rows);
  } catch (err) {
    console.error('[Agents] list error', err);
    res.status(500).json({ error: 'Failed to fetch agents' });
  }
});

// GET /:id - single agent
router.get('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rows } = await pool.query('SELECT * FROM agents WHERE agent_id = $1', [id]);
    if (!rows.length) return res.status(404).json({ error: 'Agent not found' });
    res.json(rows[0]);
  } catch (err) {
    console.error('[Agents] get one error', err);
    res.status(500).json({ error: 'Failed to fetch agent' });
  }
});

// POST / - create new agent
router.post('/', requireAdmin, async (req, res) => {
  try {
    const {
      name,
      description = null,
      agent_type = 'policy_engine',
      endpoint_url = null,
      is_active = true,
      vendor = null,
      department = null,
      risk_score = 0.0,
      status = 'active',
    } = req.body || {};

    if (!name || typeof name !== 'string' || !name.trim()) {
      return res.status(400).json({ error: 'Agent name is required' });
    }

    const insert = await pool.query(
      `INSERT INTO agents (name, description, agent_type, endpoint_url, is_active, vendor, department, risk_score, status, created_by)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
       RETURNING *`,
      [
        name.trim(),
        description,
        agent_type,
        endpoint_url,
        Boolean(is_active),
        vendor,
        department,
        Number(risk_score) || 0.0,
        status,
        req.user?.id || null,
      ]
    );

    res.status(201).json(insert.rows[0]);
  } catch (err) {
    console.error('[Agents] create error', err);
    res.status(500).json({ error: 'Failed to create agent' });
  }
});

// GET /:id/roles - agent roles
router.get('/:id/roles', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rows } = await pool.query(
      `SELECT r.role_id, r.code, r.name, r.description
         FROM agent_access aa
         JOIN roles r ON r.role_id = aa.role_id
        WHERE aa.agent_id = $1
        ORDER BY r.name NULLS LAST`,
      [id]
    );
    res.json(rows);
  } catch (err) {
    console.error('[Agents] roles list error', err);
    res.status(500).json({ error: 'Failed to fetch roles for agent' });
  }
});

// POST /:id/access - grant role to agent
router.post('/:id/access', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId } = req.body || {};
    if (!roleId) return res.status(400).json({ error: 'roleId required' });
    await pool.query(
      'INSERT INTO agent_access (agent_id, role_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
      [id, roleId]
    );
    res.status(201).json({ success: true });
  } catch (err) {
    console.error('[Agents] grant access error', err);
    res.status(500).json({ error: 'Failed to grant access' });
  }
});

// DELETE /:id/access - revoke role (expects roleId in body)
router.delete('/:id/access', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { roleId } = req.body || {};
    if (!roleId) return res.status(400).json({ error: 'roleId required' });
    await pool.query('DELETE FROM agent_access WHERE agent_id = $1 AND role_id = $2', [id, roleId]);
    res.status(204).send();
  } catch (err) {
    console.error('[Agents] revoke access error', err);
    res.status(500).json({ error: 'Failed to revoke access' });
  }
});

// DELETE /:id/policies/:policyId - remove direct policy assignment
router.delete('/:id/policies/:policyId', requireAdmin, async (req, res) => {
  try {
    const { id, policyId } = req.params;
    await pool.query(
      `DELETE FROM agent_policies WHERE agent_id = $1 AND policy_id = $2 AND link_type = 'direct'`,
      [id, policyId]
    );
    res.status(204).send();
  } catch (err) {
    console.error('[Agents] delete direct policy error', err);
    res.status(500).json({ error: 'Failed to remove policy assignment' });
  }
});

// DELETE /:id/groups/:groupId - remove via_group assignments for a group
router.delete('/:id/groups/:groupId', requireAdmin, async (req, res) => {
  try {
    const { id, groupId } = req.params;
    await pool.query(
      `DELETE FROM agent_policies ap
         USING policy_group_policies pgp
        WHERE ap.agent_id = $1
          AND ap.policy_id = pgp.policy_id
          AND pgp.group_id = $2
          AND ap.link_type = 'via_group'`,
      [id, groupId]
    );
    res.status(204).send();
  } catch (err) {
    console.error('[Agents] delete group via_group links error', err);
    res.status(500).json({ error: 'Failed to remove group assignments' });
  }
});

// PUT /:id - update agent
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name = null,
      description = null,
      agent_type = null,
      endpoint_url = null,
      is_active = null,
      vendor = null,
      department = null,
      risk_score = null,
      status = null,
    } = req.body || {};

    const result = await pool.query(
      `UPDATE agents SET
         name = COALESCE($1, name),
         description = COALESCE($2, description),
         agent_type = COALESCE($3, agent_type),
         endpoint_url = COALESCE($4, endpoint_url),
         is_active = COALESCE($5, is_active),
         vendor = COALESCE($6, vendor),
         department = COALESCE($7, department),
         risk_score = COALESCE($8, risk_score),
         status = COALESCE($9, status),
         updated_at = CURRENT_TIMESTAMP
       WHERE agent_id = $10
       RETURNING *`,
      [
        name,
        description,
        agent_type,
        endpoint_url,
        typeof is_active === 'boolean' ? is_active : null,
        vendor,
        department,
        risk_score === null || risk_score === undefined ? null : Number(risk_score),
        status,
        id,
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    console.error('[Agents] update error', err);
    res.status(500).json({ error: 'Failed to update agent' });
  }
});

// DELETE /:id - delete agent
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const del = await pool.query('DELETE FROM agents WHERE agent_id = $1 RETURNING agent_id', [id]);
    if (del.rows.length === 0) {
      return res.status(404).json({ error: 'Agent not found' });
    }
    res.status(204).send();
  } catch (err) {
    console.error('[Agents] delete error', err);
    res.status(500).json({ error: 'Failed to delete agent' });
  }
});

export default router;
