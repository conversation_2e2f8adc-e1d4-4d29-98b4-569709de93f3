import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AzureOpenAI } from 'openai';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

const router = express.Router();

// --- Azure OpenAI Configuration ---
const CHAT_ENABLED = process.env.AZURE_OPENAI_KEY && process.env.AZURE_OPENAI_ENDPOINT;
let client = null;
if (CHAT_ENABLED) {
  const deployment = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
  client = new AzureOpenAI({
    endpoint: process.env.AZURE_OPENAI_ENDPOINT,
    apiKey: process.env.AZURE_OPENAI_KEY,
    apiVersion: '2024-04-01-preview',
    deployment,
  });
} else {
  console.warn('[Chat] Azure OpenAI credentials missing – /api/v1/chat disabled');
}

// Native MCP Configuration
const MCP_SERVER_ENABLED = process.env.MCP_SERVER_ENABLED === 'true';
const MCP_SERVER_HOST = process.env.MCP_SERVER_HOST || 'mcp-server';  // Use env var or fallback to container name


// In-memory storage for sessions and messages
const sessions = {};

// Global MCP client - maintain one connection to the running MCP server
let mcpClient = null;
let mcpTools = [];

// Initialize MCP connection to the running container
async function initializeMCP() {
    if (!MCP_SERVER_ENABLED) {
        console.log('📴 MCP server disabled, skipping initialization');
        return false;
    }

    try {
        console.log('🚀 Initializing native MCP connection to container...');
        console.log(`🔗 Connecting to MCP server at: ${MCP_SERVER_HOST}`);
        
        
        // Connect to the running MCP server container via HTTP/SSE
        // The MCP server is already running, we just need to connect to it over the network
        const mcpServerUrl = `http://${MCP_SERVER_HOST}:8002/sse/`;
        
        const transport = new SSEClientTransport(new URL(mcpServerUrl));
        mcpClient = new Client({
            name: 'vitea-pilot-client',
            version: '1.0.0'
        }, {
            capabilities: {}
        });
        
        // Connect to the server
        await mcpClient.connect(transport);
        console.log('✅ MCP client connected to running container!');
        
        // Get available tools
        const toolsResponse = await mcpClient.listTools();
        mcpTools = toolsResponse.tools || [];
        
        console.log(`🛠️  Found ${mcpTools.length} MCP tools:`, mcpTools.map(t => t.name));
        return true;
        
    } catch (error) {
        console.error('❌ Failed to initialize MCP connection:', error);
        console.log('⚠️  Continuing without MCP support');
        mcpClient = null;
        mcpTools = [];
        return false;
    }
}

// Convert MCP tools to Azure OpenAI format
function getMCPToolsForAzure() {
    if (!mcpTools.length) {
        console.log('📭 No MCP tools available');
        return [];
    }

    const azureTools = mcpTools.map(tool => ({
        type: "function",
        function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.inputSchema || {
                type: "object",
                properties: {},
                required: []
            }
        }
    }));

    console.log(`🎯 Converted ${mcpTools.length} MCP tools for Azure OpenAI`);
    return azureTools;
}

// Call MCP tool via native client
async function callMCPTool(toolName, args) {
    if (!mcpClient) {
        throw new Error('MCP client not initialized');
    }

    console.log(`🔧 Calling native MCP tool '${toolName}' with args:`, args);
    
    try {
        const result = await mcpClient.callTool({
            name: toolName,
            arguments: args
        });
        console.log(`✅ Native MCP tool '${toolName}' completed successfully`);
        
        // Extract text content from MCP response
        if (result.content && result.content.length > 0) {
            const content = result.content[0];
            return typeof content === 'string' ? content : content.text || JSON.stringify(content);
        }
        
        return JSON.stringify(result);
    } catch (error) {
        console.error(`❌ Error calling native MCP tool '${toolName}':`, error);
        throw error;
    }
}

// Initialize MCP on startup
initializeMCP().then(success => {
    if (success) {
        console.log('🎉 Native MCP initialization completed successfully');
    } else {
        console.log('⚠️  MCP initialization failed, continuing without MCP support');
    }
}).catch(error => {
    console.error('💥 MCP initialization crashed:', error);
});

// MCP Flow steps (mocked for now)
const MCP_STEPS = [
    { step: 1, name: 'User submits query', status: 'completed', timestamp: new Date().toISOString() },
    { step: 2, name: 'Get active policies', status: 'completed', timestamp: new Date().toISOString() },
    { step: 3, name: 'Policy rules retrieved', status: 'completed', timestamp: new Date().toISOString() },
    { step: 4, name: 'Pre-process request', status: 'completed', timestamp: new Date().toISOString() },
    { step: 5, name: 'Sanitized request to chatbot', status: 'completed', timestamp: new Date().toISOString() },
    { step: 6, name: 'Route through gateway', status: 'completed', timestamp: new Date().toISOString() },
    { step: 7, name: 'MCP invokes APIs', status: 'completed', timestamp: new Date().toISOString() },
    { step: 8, name: 'Database queries', status: 'completed', timestamp: new Date().toISOString() },
    { step: 9, name: 'Raw data returned', status: 'completed', timestamp: new Date().toISOString() },
    { step: 10, name: 'Policy filtering', status: 'completed', timestamp: new Date().toISOString() },
    { step: 11, name: 'Filtered response', status: 'completed', timestamp: new Date().toISOString() },
    { step: 12, name: 'UI rendering', status: 'completed', timestamp: new Date().toISOString() }
];


// POST /api/v1/chat/session - Create a new chat session
router.post('/session', (req, res) => {
    if (!process.env.AZURE_OPENAI_ENDPOINT || !process.env.AZURE_OPENAI_KEY || !deployment) {
        console.error("Azure OpenAI environment variables are not set. Cannot create session.");
        console.log("AZURE_OPENAI_ENDPOINT:", process.env.AZURE_OPENAI_ENDPOINT);
        console.log("AZURE_OPENAI_KEY:", process.env.AZURE_OPENAI_KEY);
        console.log("deployment:", deployment);
        return res.status(500).json({
            error: 'Server configuration error prevents session creation.'
        });
    }

    try {
        const sessionId = uuidv4();
        sessions[sessionId] = {
            messages: [],
            createdAt: new Date(),
            metadata: req.body.metadata || {}
        };
        console.log(`New session created: ${sessionId}`);
        res.status(201).json({
            session_id: sessionId,
            message: 'Session created successfully.',
            metadata: sessions[sessionId].metadata,
            mcp_enabled: MCP_SERVER_ENABLED,
            mcp_connected: mcpClient !== null,
            mcp_tools_available: mcpTools.length
        });
    } catch (error) {
        console.error('Error creating session:', error);
        res.status(500).json({ error: 'Failed to create session', details: error.message });
    }
});

// POST /api/v1/chat/:sessionId/message - Handle a new message
router.post('/:sessionId/message', async (req, res) => {
    console.log('🚀 Message route hit');
    const { sessionId } = req.params;
    const { message } = req.body;
    
    console.log('📝 Parsed params:', { sessionId, message: message?.substring(0, 50) });

    if (!sessions[sessionId]) {
        console.log('❌ Session not found:', sessionId);
        return res.status(404).json({ error: 'Session not found' });
    }

    if (!message) {
        console.log('❌ No message content');
        return res.status(400).json({ error: 'Message content is required' });
    }

    if (!process.env.AZURE_OPENAI_ENDPOINT || !process.env.AZURE_OPENAI_KEY || !deployment) {
        console.error("Azure OpenAI environment variables are not set.");
        return res.status(500).json({ error: 'Server configuration error.' });
    }

    console.log('✅ Starting message processing');
    try {
        console.log(`Processing message in session ${sessionId}: "${message}"`);
        console.log(`MCP Server Host: ${MCP_SERVER_HOST}`);
        
        // Add user message to session, maintaining format for OpenAI
        const userMessage = { role: 'user', content: message };
        sessions[sessionId].messages.push(userMessage);
        
        // Construct the chat history for the model
        const chatHistory = [
            { role: "system", content: "You are a helpful AI assistant for the Vitea healthcare application. Your responses must be compliant with healthcare regulations and policies." },
            ...sessions[sessionId].messages
        ];

        console.log('📡 Getting native MCP tools');
        const tools = getMCPToolsForAzure();
        console.log(`🎯 Using ${tools.length} native MCP tools`);

        console.log('🤖 About to call Azure OpenAI');
        let completion;
        try {
            completion = await client.chat.completions.create({
                messages: chatHistory,
                model: deployment,
                tools: tools,
                tool_choice: "auto",
                max_tokens: 800,
                temperature: 1,
                top_p: 1,
                frequency_penalty: 0,
                presence_penalty: 0,
            });
            console.log('✅ Azure OpenAI response received');
        } catch (openaiError) {
            console.error('❌ Azure OpenAI error:', openaiError);
            throw new Error(`Azure OpenAI failed: ${openaiError.message}`);
        }
        
        const responseMessage = completion.choices[0].message;

        // If the model wants to call a tool
        if (responseMessage.tool_calls) {
            sessions[sessionId].messages.push(responseMessage); // Save assistant's response

            const toolMessages = [];
            for (const toolCall of responseMessage.tool_calls) {
                const functionName = toolCall.function.name;
                const functionArgs = JSON.parse(toolCall.function.arguments);
                let mcpResult;

                console.log(`Model calling function: ${functionName} with args:`, functionArgs);

                try {
                    console.log(`🔧 Calling native MCP tool: ${functionName}`);
                    
                    // Use native MCP client to call the tool
                    mcpResult = await callMCPTool(functionName, functionArgs);
                } catch (toolError) {
                    console.error(`❌ Error calling native MCP tool ${functionName}:`, toolError);
                    mcpResult = { 
                        error: `Failed to call native MCP tool ${functionName}`, 
                        details: toolError.message,
                        mcp_native: true
                    };
                }

                toolMessages.push({
                    tool_call_id: toolCall.id,
                    role: "tool",
                    content: JSON.stringify(mcpResult),
                });
            }

            sessions[sessionId].messages.push(...toolMessages); // Save tool results

            let secondCompletion;
            try {
                secondCompletion = await client.chat.completions.create({
                    model: deployment,
                    messages: [
                        { role: "system", content: "You are a helpful AI assistant for the Vitea healthcare application. Your responses must be compliant with healthcare regulations and policies." },
                        ...sessions[sessionId].messages
                    ],
                });
            } catch (secondOpenaiError) {
                console.error('❌ Second Azure OpenAI call error:', secondOpenaiError);
                throw new Error(`Second Azure OpenAI call failed: ${secondOpenaiError.message}`);
            }

            const finalBotResponse = secondCompletion.choices[0].message.content;
            sessions[sessionId].messages.push({ role: 'assistant', content: finalBotResponse });

            res.json({
                message: finalBotResponse,
                metadata: {
                    session_id: sessionId,
                    timestamp: new Date().toISOString(),
                    model: deployment,
                }
            });
        } else {
            // Original behavior if no tool is called
            const botResponse = responseMessage.content;
            sessions[sessionId].messages.push({ role: 'assistant', content: botResponse });

            res.json({
                message: botResponse,
                metadata: {
                    session_id: sessionId,
                    timestamp: new Date().toISOString(),
                    model: deployment,
                }
            });
        }
    } catch (error) {
        console.error(`Error in session ${sessionId}:`, error);
        res.status(500).json({ 
            error: 'Failed to process message', 
            details: error.message,
            stack: error.stack 
        });
    }
});

// GET /api/v1/chat/:sessionId/flow - Get the MCP flow for a session
router.get('/:sessionId/flow', (req, res) => {
    const { sessionId } = req.params;

    if (!sessions[sessionId]) {
        return res.status(404).json({ error: 'Session not found' });
    }

    // In a real application, this would be the actual flow for the last message.
    // For now, we return a mocked, completed flow.
    res.json(MCP_STEPS);
});

if (!CHAT_ENABLED) {
  router.all('*', (req, res) => res.status(501).json({ error: 'Chat feature disabled in this environment' }));
}

export default router;

// Add MCP status endpoint
router.get('/mcp/status', (req, res) => {
    res.json({
        enabled: MCP_SERVER_ENABLED,
        connected: mcpClient !== null,
        tools_available: mcpTools.length,
        tools: mcpTools.map(t => ({ name: t.name, description: t.description })),
        connection_type: 'native_sse',
        server_host: MCP_SERVER_HOST
    });
});
``
