import express from 'express';
import { pool } from '../db.js';
import { requireOAuthClient } from '../middleware/oauthClient.js';
import { createMetricsRegistry, observeHistogram, incrementCounter } from '../utils/metrics.js';
import { createSnapshot, getSnapshot } from '../services/snapshotRegistry.js';

const router = express.Router();

const metrics = createMetricsRegistry();

// GET /api/v1/integrations/assignments-snapshot
router.get('/assignments-snapshot', requireOAuthClient, async (req, res) => {
  try {
    const start = Date.now();
    const pageSize = Math.min(parseInt(req.query.page_size || '500', 10), 2000);
    const cursor = req.query.cursor || null;
    let snapshotId = req.query.snapshot_id || null;

    let after = null;
    if (cursor) {
      try {
        const decoded = Buffer.from(cursor, 'base64').toString('utf8');
        after = JSON.parse(decoded);
      } catch (_) { /* ignore */ }
    }

    // Create or validate snapshot
    if (!snapshotId) {
      const s = createSnapshot();
      snapshotId = s.id;
    } else if (!getSnapshot(snapshotId)) {
      return res.status(400).json({ error: 'Invalid or expired snapshot_id' });
    }

    const whereClause = after
      ? `WHERE (arp.agent_id, arp.role_id, arp.group_id, arp.policy_id) > ($1::uuid, $2::uuid, $3::uuid, $4::uuid)`
      : '';
    const params = after ? [after.agent_id, after.role_id, after.group_id, after.policy_id] : [];

    const query = `
      SELECT
        arp.agent_id,
        arp.role_id,
        arp.group_id,
        arp.policy_id,
        p.guardrail_id,
        p.name,
        p.description,
        p.category,
        p.policy_type,
        p.definition,
        p.version,
        p.is_active,
        p.severity
      FROM agent_role_policies arp
      JOIN policies p ON p.policy_id = arp.policy_id 
          AND p.deleted_at IS NULL
      ${whereClause}
      ORDER BY arp.agent_id, arp.role_id, arp.group_id, arp.policy_id
      LIMIT ${pageSize + 1}
    `;

    const { rows } = await pool.query(query, params);
    const items = rows.slice(0, pageSize).map((r) => ({
      agent_id: r.agent_id,
      role_id: r.role_id,
      group_id: r.group_id,
      policy_id: r.policy_id,
      policy: {
        guardrail_id: r.guardrail_id || null,
        name: r.name,
        description: r.description,
        category: r.category,
        policy_type: r.policy_type,
        definition: r.definition,
        version: r.version,
        is_active: r.is_active,
        severity: r.severity
      }
    }));

    let nextCursor = null;
    if (rows.length > pageSize) {
      const last = items[items.length - 1];
      nextCursor = Buffer.from(JSON.stringify({
        agent_id: last.agent_id,
        role_id: last.role_id,
        group_id: last.group_id,
        policy_id: last.policy_id
      }), 'utf8').toString('base64');
    }

    const response = {
      snapshot_id: snapshotId,
      generated_at: new Date().toISOString(),
      items,
      next_cursor: nextCursor,
      correlation_id: req.correlationId
    };
    observeHistogram('snapshot_response_seconds', (Date.now() - start) / 1000, { route: 'assignments-snapshot' }, metrics);
    incrementCounter('snapshot_requests_total', { route: 'assignments-snapshot' }, 1, metrics);
    res.json(response);
  } catch (err) {
    console.error('[Integrations] snapshot error', err);
    res.status(500).json({ error: 'Failed to build snapshot' });
  }
});

export default router;

