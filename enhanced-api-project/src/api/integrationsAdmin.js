import express from 'express';
import { pool } from '../db.js';

const router = express.Router();

// Admin-only: replay a DLQ item back to outbox
router.post('/dlq/:id/replay', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({ error: 'DLQ replay disabled in production via API' });
    }
    const { id } = req.params;
    const { rows } = await pool.query('SELECT * FROM integration_dlq WHERE id = $1', [id]);
    if (!rows.length) return res.status(404).json({ error: 'DLQ item not found' });
    const item = rows[0];
    await pool.query(
      `INSERT INTO integration_outbox (id, tenant_id, destination, event_type, event_version, payload_json, status, attempts, next_attempt_at, last_error, created_at, updated_at)
       VALUES ($1,$2,$3,$4,$5,$6,'pending',0, NOW(), NULL, NOW(), NOW())
       ON CONFLICT (id) DO UPDATE SET status='pending', attempts=0, next_attempt_at=NOW(), last_error=NULL, updated_at=NOW()`,
      [item.id, item.tenant_id, item.destination, item.event_type, item.event_version, item.payload_json]
    );
    res.json({ replayed: true });
  } catch (err) {
    console.error('[IntegrationsAdmin] replay error', err);
    res.status(500).json({ error: 'Failed to replay DLQ item' });
  }
});

export default router;

