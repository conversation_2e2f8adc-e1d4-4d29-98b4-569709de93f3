import express from 'express';
import Ajv from 'ajv';
import schemaService from '../services/schemaService.js';
import schemaValidator from '../utils/schemaValidator.js';
import templateGenerationService from '../services/templateGenerationService.js';
import schemaTemplatesRouter from './schemaTemplates.js';

const router = express.Router();
const ajv = new Ajv({ strict: false });

/**
 * GET /api/v1/schemas - Get all schemas
 * Returns all active schemas in the format expected by the UI
 */
router.get('/', async (req, res) => {
    try {
        const schemas = await schemaService.getAllSchemas();
        res.json(schemas);
    } catch (error) {
        console.error('Error fetching schemas:', error);
        res.status(500).json({ error: 'Failed to fetch schemas' });
    }
});

/**
 * GET /api/v1/schemas/list - Get list of schema metadata
 * Returns metadata about all schemas including names, descriptions, and guardrail IDs
 */
router.get('/list', async (req, res) => {
    try {
        const schemaList = await schemaService.getSchemaNames();
        res.json({ schemas: schemaList });
    } catch (error) {
        console.error('Error fetching schema list:', error);
        res.status(500).json({ error: 'Failed to fetch schema list' });
    }
});

/**
 * GET /api/v1/schemas/:name - Get specific schema
 * Returns a specific schema by name with its metadata
 */
router.get('/:name', async (req, res) => {
    try {
        const schemaData = await schemaService.getSchemaWithGuardrail(req.params.name);
        if (!schemaData) {
            return res.status(404).json({ error: 'Schema not found' });
        }
        res.json(schemaData);
    } catch (error) {
        console.error(`Error fetching schema ${req.params.name}:`, error);
        res.status(500).json({ error: 'Failed to fetch schema' });
    }
});

/**
 * PUT /api/v1/schemas/:name - Update or create schema
 * Updates an existing schema or creates a new one
 */
router.put('/:name', async (req, res) => {
    try {
        const { schema_content, description, guardrail_id } = req.body;
        
        if (!schema_content) {
            return res.status(400).json({ error: 'schema_content is required' });
        }
        
        // Validate schema format
        try {
            ajv.compile(schema_content);
        } catch (error) {
            return res.status(400).json({ 
                error: 'Invalid JSON schema format',
                details: error.message 
            });
        }
        
        const result = await schemaService.upsertSchema(
            req.params.name, 
            schema_content, 
            description,
            guardrail_id
        );
        
        // Clear cache for updated schema
        schemaValidator.clearCache(req.params.name);
        
        // Handle template generation/update for the schema
        await templateGenerationService.handleSchemaUpdate(req.params.name, schema_content);
        
        res.json({ 
            message: 'Schema updated successfully',
            schema_name: req.params.name,
            id: result.id,
            updated_at: result.updated_at,
            template_updated: true
        });
    } catch (error) {
        console.error(`Error updating schema ${req.params.name}:`, error);
        res.status(500).json({ error: 'Failed to update schema' });
    }
});

/**
 * DELETE /api/v1/schemas/:name - Deactivate schema
 * Soft deletes a schema by setting is_active to false
 */
router.delete('/:name', async (req, res) => {
    try {
        await schemaService.deactivateSchema(req.params.name);
        
        // Clear cache for deactivated schema
        schemaValidator.clearCache(req.params.name);
        
        res.json({ 
            message: 'Schema deactivated successfully',
            schema_name: req.params.name
        });
    } catch (error) {
        console.error(`Error deactivating schema ${req.params.name}:`, error);
        if (error.message.includes('not found')) {
            res.status(404).json({ error: error.message });
        } else {
            res.status(500).json({ error: 'Failed to deactivate schema' });
        }
    }
});

/**
 * GET /api/v1/schemas/guardrail/:guardrailId - Get schemas by guardrail
 * Returns all schemas associated with a specific guardrail ID
 */
router.get('/guardrail/:guardrailId', async (req, res) => {
    try {
        const schemas = await schemaService.getSchemasByGuardrailId(req.params.guardrailId);
        res.json({ 
            guardrail_id: req.params.guardrailId,
            schemas: schemas 
        });
    } catch (error) {
        console.error(`Error fetching schemas for guardrail ${req.params.guardrailId}:`, error);
        res.status(500).json({ error: 'Failed to fetch schemas by guardrail' });
    }
});

/**
 * POST /api/v1/schemas/validate - Validate data against a schema
 * Test endpoint to validate data against a specific schema
 */
router.post('/validate', async (req, res) => {
    try {
        const { schema_name, data } = req.body;
        
        if (!schema_name || !data) {
            return res.status(400).json({ 
                error: 'Both schema_name and data are required' 
            });
        }
        
        const validation = await schemaValidator.validatePolicyDefinition(data, schema_name);
        
        res.json({
            schema_name,
            valid: validation.isValid,
            errors: validation.errors,
            warnings: validation.warnings
        });
    } catch (error) {
        console.error('Error validating data:', error);
        res.status(500).json({ error: 'Failed to validate data' });
    }
});

// Mount template-related routes under /schemas
router.use('/', schemaTemplatesRouter);

export default router;