import express from 'express';
import crypto from 'crypto';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import 'dotenv/config';

import { initializeDb, pool } from './db.js';
import { createMetricsRegistry, metricsHandler } from './utils/metrics.js';
import { startDispatcher } from './services/webhookDispatcher.js';
import chatRoutes from './api/chat.js';
import policyRoutes from './api/policies.js';
import metricsRoutes from './api/metrics.js';
import enumsRoutes from './api/enums.js';
import schemaTemplateRoutes from './api/schemaTemplates.js';

console.log('[Startup] Enhanced API server booting...');

const app = express();

// Trust proxy for Application Gateway
app.set('trust proxy', true);

// Security middleware
app.use(helmet());

// Enable CORS for all routes
app.use(cors({
  origin: '*', // Allow all origins for development. For production, restrict to your frontend URL.
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-User-Id']
}));

// Rate limiting with custom key generator for Application Gateway
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // limit each IP to 500 requests per windowMs
  message: 'Too many requests from this IP, please try again after 15 minutes',
  keyGenerator: (req) => {
    // Use X-Forwarded-For if available, otherwise fall back to remote address
    return req.headers['x-forwarded-for']?.split(',')[0]?.trim() || 
           req.socket.remoteAddress ||
           req.ip ||
           'unknown';
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});
app.use('/api/', limiter);

// Body parsing
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Logging
app.use(morgan('combined'));
// Metrics endpoint
const metrics = createMetricsRegistry();
app.get('/metrics', metricsHandler(metrics));
app.get('/_metrics', metricsHandler(metrics));
app.get('/api/v1/metrics-exposed', metricsHandler(metrics));

// Correlation ID middleware
app.use((req, res, next) => {
  const cid = req.headers['x-correlation-id'] || crypto.randomUUID();
  req.correlationId = cid;
  res.setHeader('X-Correlation-Id', cid);
  next();
});

// ===== REGISTER API ROUTES =====
// We register routes after initialization to ensure they are only active when the app is ready.

app.use('/api/v1/chat', chatRoutes);
app.use('/api/v1/policies', policyRoutes);
app.use('/api/v1/schemas', (await import('./api/schemas.js')).default);
app.use('/api/v1/policy-groups', (await import('./api/policyGroups.js')).default);
app.use('/api/v1/roles', (await import('./api/roles.js')).default);
// Agents CRUD & list
app.use('/api/v1/agents', (await import('./api/agents.js')).default);
// Agent role-scoped policy assignments
app.use('/api/v1', (await import('./api/agentRolePolicies.js')).default);
// Agent policy consolidation
app.use('/api/v1/agent-policies', (await import('./api/agentPolicies.js')).default);
app.use('/api/v1/bulk-policy-assign', (await import('./api/bulkAssign.js')).default);
app.use('/api/v1/metrics', metricsRoutes);
app.use('/api/v1/enums', enumsRoutes);
// Integrations (snapshot API)
app.use('/api/v1/integrations', (await import('./api/integrations.js')).default);
// Integrations Admin (DLQ replay) - protect behind env in real use
app.use('/api/v1/integrations-admin', (await import('./api/integrationsAdmin.js')).default);


// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbTest = await pool.query('SELECT 1');
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      services: {
        database: dbTest.rows ? 'connected' : 'disconnected'
      }
    });
  } catch (err) {
    console.error('Health check failed:', err);
    res.status(500).json({ error: 'Health check failed', details: err.message });
  }
});

// Original test endpoint
app.get('/api/v1/test', (req, res) => {
  res.json({
    message: 'Enhanced Vitea API is running',
    version: '2.0.0'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});


// ===== START SERVER =====
initializeDb().then(() => {
  const PORT = process.env.PORT || 8000;
  app.listen(PORT, () => {
    console.log(`✅ Enhanced Vitea API server running on port ${PORT}`);
    console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`   Health check: http://localhost:${PORT}/health`);
    if (process.env.INTEGRATIONS_ENABLED) {
      console.log('[Integrations] Starting webhook dispatcher...');
      startDispatcher(1000);
    }
  });
});
