// Lightweight metrics helper with optional Prometheus support.
// If prom-client is available, exposes a real /metrics endpoint.
// Otherwise, maintains simple in-memory counters and renders Prometheus format.

let promClient = null;
try {
  // eslint-disable-next-line global-require
  promClient = require('prom-client');
} catch (_) {
  promClient = null;
}

const inMemoryCounters = new Map();
const inMemoryHistograms = new Map();

function keyFor(name, labels) {
  if (!labels) return name;
  const parts = Object.keys(labels)
    .sort()
    .map((k) => `${k}=${labels[k]}`);
  return `${name}{${parts.join(',')}}`;
}

export function createMetricsRegistry() {
  if (promClient) {
    const register = new promClient.Registry();
    promClient.collectDefaultMetrics({ register });
    return { register, promClient };
  }
  return { register: null, promClient: null };
}

export function incrementCounter(name, labels = {}, value = 1, registry = null) {
  if (promClient && registry?.register) {
    const metricKey = `${name}|${JSON.stringify(Object.keys(labels).sort())}`;
    // Cache metrics by name+label set
    if (!incrementCounter._cache) incrementCounter._cache = new Map();
    if (!incrementCounter._cache.has(metricKey)) {
      const c = new promClient.Counter({ name, help: name, labelNames: Object.keys(labels), registers: [registry.register] });
      incrementCounter._cache.set(metricKey, c);
    }
    incrementCounter._cache.get(metricKey).inc(labels, value);
    return;
  }
  const k = keyFor(name, labels);
  inMemoryCounters.set(k, (inMemoryCounters.get(k) || 0) + value);
}

export function observeHistogram(name, value, labels = {}, registry = null) {
  if (promClient && registry?.register) {
    const metricKey = `${name}|${JSON.stringify(Object.keys(labels).sort())}`;
    if (!observeHistogram._cache) observeHistogram._cache = new Map();
    if (!observeHistogram._cache.has(metricKey)) {
      const h = new promClient.Histogram({
        name,
        help: name,
        labelNames: Object.keys(labels),
        buckets: [0.01, 0.05, 0.1, 0.25, 0.5, 1, 2, 5, 10],
        registers: [registry.register],
      });
      observeHistogram._cache.set(metricKey, h);
    }
    observeHistogram._cache.get(metricKey).observe(labels, value);
    return;
  }
  const k = keyFor(name, labels);
  const arr = inMemoryHistograms.get(k) || [];
  arr.push(value);
  inMemoryHistograms.set(k, arr);
}

export function metricsHandler(registry) {
  if (promClient && registry?.register) {
    return async (req, res) => {
      res.set('Content-Type', registry.register.contentType);
      res.send(await registry.register.metrics());
    };
  }
  return (_req, res) => {
    res.set('Content-Type', 'text/plain; version=0.0.4');
    const lines = [];
    for (const [k, v] of inMemoryCounters.entries()) {
      // Convert key with labels into Prometheus format
      const match = k.match(/^(.*?)\{(.*)\}$/);
      if (match) {
        const name = match[1];
        const labels = match[2];
        lines.push(`${name}{${labels}} ${v}`);
      } else {
        lines.push(`${k} ${v}`);
      }
    }
    // For histograms, expose simple summary as count and sum
    for (const [k, arr] of inMemoryHistograms.entries()) {
      const sum = arr.reduce((a, b) => a + b, 0);
      const count = arr.length;
      const match = k.match(/^(.*?)\{(.*)\}$/);
      if (match) {
        const name = match[1];
        const labels = match[2];
        lines.push(`${name}_count{${labels}} ${count}`);
        lines.push(`${name}_sum{${labels}} ${sum}`);
      } else {
        lines.push(`${k}_count ${count}`);
        lines.push(`${k}_sum ${sum}`);
      }
    }
    res.send(lines.join('\n'));
  };
}

