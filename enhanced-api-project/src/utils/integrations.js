import crypto from 'crypto';
import { pool } from '../db.js';

// HMAC signature generator (base64 of SHA256)
export function signPayloadHmacBase64(secret, rawBody) {
  return crypto.createHmac('sha256', secret).update(rawBody).digest('base64');
}

// Compute next backoff (seconds) with exponential growth and full jitter
// Attempts start at 0; caps after 10 attempts
export function computeNextBackoffSeconds(attempts) {
  const capped = Math.min(attempts, 10);
  const schedule = [1, 3, 9, 27, 120, 300, 900, 1200, 1800, 3600, 7200];
  const base = schedule[capped] ?? 7200;
  return Math.floor(Math.random() * base);
}

// Select pending outbox rows
export async function fetchPendingOutbox(limit = 50) {
  const { rows } = await pool.query(
    `SELECT * FROM integration_outbox
     WHERE status = 'pending' AND next_attempt_at <= NOW()
     ORDER BY created_at ASC
     LIMIT $1`,
    [limit]
  );
  return rows;
}

export async function markDelivered(id) {
  await pool.query(`UPDATE integration_outbox SET status='delivered', updated_at=CURRENT_TIMESTAMP WHERE id=$1`, [id]);
}

export async function markFailedAndSchedule(id, attempts, lastError) {
  const nextDelay = computeNextBackoffSeconds(attempts);
  await pool.query(
    `UPDATE integration_outbox
       SET attempts = attempts + 1,
           status = CASE WHEN attempts + 1 >= 10 THEN 'dead' ELSE 'pending' END,
           next_attempt_at = NOW() + make_interval(secs => $2),
           last_error = $3,
           updated_at = CURRENT_TIMESTAMP
     WHERE id = $1`,
    [id, nextDelay, lastError?.toString().slice(0, 2000) || null]
  );
  // Move to DLQ if dead
  await pool.query(
    `INSERT INTO integration_dlq (id, tenant_id, destination, event_type, event_version, payload_json, attempts, last_error, created_at, updated_at)
     SELECT id, tenant_id, destination, event_type, event_version, payload_json, attempts + 0, $2, created_at, updated_at
       FROM integration_outbox WHERE id = $1 AND status = 'dead'
     ON CONFLICT DO NOTHING`,
    [id, lastError?.toString().slice(0, 2000) || null]
  );
}

export function buildWebhookHeaders(event) {
  const timestamp = new Date().toISOString();
  const eventId = event.payload_json?.event_id || event.id;
  const correlationId = event.payload_json?.correlation_id || eventId;
  return {
    'Content-Type': 'application/json',
    'X-Event-Id': eventId,
    'X-Event-Type': event.event_type,
    'X-Event-Version': String(event.event_version || 1),
    'X-Timestamp': timestamp,
    'X-Correlation-Id': correlationId,
  };
}

