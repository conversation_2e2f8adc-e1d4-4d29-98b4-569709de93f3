import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import schemaService from '../services/schemaService.js';
import templateGenerationService from '../services/templateGenerationService.js';

class SchemaValidator {
    constructor() {
        this.ajv = new Ajv({ 
            allErrors: true, 
            strict: false,
            verbose: true,
            useDefaults: true,
            coerceTypes: true
        });
        addFormats(this.ajv);
        this.compiledSchemas = new Map(); // Simple cache
    }
    
    /**
     * Load schema from database (replaces file loading)
     */
    async getCompiledSchema(schemaName) {
        // Check cache first
        if (this.compiledSchemas.has(schemaName)) {
            return this.compiledSchemas.get(schemaName);
        }
        
        // Load from database
        const schema = await schemaService.getSchema(schemaName);
        if (!schema) {
            console.warn(`No schema found for policy type: ${schemaName}. Skipping validation.`);
            return null;
        }
        
        // Compile and cache
        try {
            const compiled = this.ajv.compile(schema);
            this.compiledSchemas.set(schemaName, compiled);
            return compiled;
        } catch (error) {
            console.error(`Error compiling schema ${schemaName}:`, error);
            throw new Error(`Invalid schema: ${schemaName}`);
        }
    }
    
    /**
     * Get schema for a specific policy type
     * @param {string} policyType - The policy type
     * @returns {Object|null} - The schema object or null if not found
     */
    async getSchemaForPolicyType(policyType) {
        if (!policyType) {
            return null;
        }
        return await schemaService.getSchema(policyType);
    }
    
    /**
     * Validate a policy definition against its schema
     * @param {Object} policyDefinition - The policy definition to validate
     * @param {string} policyType - The policy type
     * @returns {Object} - Validation result with isValid, errors, and warnings
     */
    async validatePolicyDefinition(policyDefinition, policyType) {
        const schema = await this.getSchemaForPolicyType(policyType);
        if (!schema) {
            // If no schema is found for this policy type, allow it but log a warning
            console.warn(`No schema found for policy type: ${policyType}. Skipping validation.`);
            return {
                isValid: true,
                errors: [],
                warnings: [`No schema defined for policy type: ${policyType}`]
            };
        }

        try {
            const validator = await this.getCompiledSchema(policyType);
            if (!validator) {
                return {
                    isValid: true,
                    errors: [],
                    warnings: [`No schema defined for policy type: ${policyType}`]
                };
            }
            
            const isValid = validator(policyDefinition);
            
            return {
                isValid,
                errors: isValid ? [] : validator.errors?.map(err => `${err.instancePath} ${err.message}`) || [],
                warnings: []
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [`Schema validation error: ${error.message}`],
                warnings: []
            };
        }
    }
    
    /**
     * Generate default values for a policy type based on its schema
     * Now uses TemplateGenerationService for database-driven templates
     * @param {string} policyType - The policy type
     * @returns {Promise<Object>} - Default policy definition
     */
    async generateDefaultPolicy(policyType) {
        try {
            // Use template service to get template (database > auto-generated > hardcoded)
            const template = await templateGenerationService.getTemplate(policyType);
            if (template) {
                return template;
            }
        } catch (error) {
            console.warn(`Error getting template from service for ${policyType}, falling back to hardcoded:`, error);
        }
        
        // Fallback to hardcoded templates (temporary during migration)
        const templates = {
            medical_privacy: {
                type: "medical_privacy",
                severity: "medium",
                allowed_roles: ["doctor"],
                hipaa_compliance: true,
                protected_fields: ["diagnosis"],
                audit_requirements: {
                    log_access: true,
                    retention_period: 7,
                    encryption_required: true,
                    access_timeout: 30
                },
                data_handling: {
                    anonymization: false,
                    pseudonymization: true,
                    data_minimization: true
                }
            },
            data_privacy: {
                type: "data_privacy",
                severity: "medium",
                allowed_roles: ["admin"],
                data_classification: "confidential",
                protected_fields: ["personal_info"],
                consent_requirements: {
                    explicit_consent: true,
                    consent_expiry: 12,
                    withdrawal_allowed: true
                },
                data_retention: {
                    retention_period: 24,
                    auto_deletion: true,
                    archive_after: 12
                }
            },
            access_control: {
                type: "access_control",
                severity: "medium",
                allowed_roles: ["admin"],
                time_restrictions: {
                    start_time: "09:00",
                    end_time: "17:00",
                    timezone: "UTC",
                    allowed_days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
                },
                session_management: {
                    max_session_duration: 60,
                    inactivity_timeout: 15,
                    concurrent_sessions: 3
                }
            },
            compliance: {
                type: "compliance",
                severity: "medium",
                allowed_roles: ["compliance_officer"],
                regulatory_framework: "gdpr",
                compliance_requirements: ["data_encryption"],
                audit_frequency: "quarterly",
                reporting_requirements: {
                    incident_reporting: true,
                    reporting_timeframe: 24,
                    regulatory_notifications: true
                }
            }
        };

        return templates[policyType] || { type: policyType };
    }
    
    /**
     * Get available policy types from database
     * @returns {Array} - Array of available policy types
     */
    async getAvailablePolicyTypes() {
        const schemaNames = await schemaService.getSchemaNames();
        return schemaNames.map(s => s.schema_name);
    }
    
    /**
     * Format validation errors for API response
     * @param {Array} errors - Array of validation error strings
     * @returns {Array} - Formatted error messages
     */
    formatValidationErrors(errors) {
        return errors.map(error => {
            // Remove the leading slash and convert to readable format
            const cleanError = error.replace(/^\//, '').replace(/\//g, ' → ');
            return cleanError;
        });
    }
    
    /**
     * Clear cache when schemas are updated
     */
    clearCache(schemaName = null) {
        if (schemaName) {
            this.compiledSchemas.delete(schemaName);
        } else {
            this.compiledSchemas.clear();
        }
        console.log(`Schema cache cleared${schemaName ? ` for ${schemaName}` : ''}`);
    }
}

// Create singleton instance
const schemaValidator = new SchemaValidator();

// Export individual functions for backward compatibility
export const getSchemaForPolicyType = (policyType) => schemaValidator.getSchemaForPolicyType(policyType);
export const validatePolicyDefinition = (policyDefinition, policyType) => schemaValidator.validatePolicyDefinition(policyDefinition, policyType);
export const generateDefaultPolicy = async (policyType) => schemaValidator.generateDefaultPolicy(policyType);
export const getAvailablePolicyTypes = () => schemaValidator.getAvailablePolicyTypes();
export const formatValidationErrors = (errors) => schemaValidator.formatValidationErrors(errors);

/**
 * Middleware for validating policy definitions in API requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const validatePolicyMiddleware = async (req, res, next) => {
    const { definition, policy_type } = req.body;
    
    if (!definition || !policy_type) {
        return next(); // Let other validation handle missing fields
    }

    // Validate ONLY the definition field against the schema
    const validation = await schemaValidator.validatePolicyDefinition(definition, policy_type);
    
    if (!validation.isValid) {
        const formattedErrors = schemaValidator.formatValidationErrors(validation.errors);
        return res.status(400).json({
            error: 'Policy definition validation failed',
            details: formattedErrors,
            schema_errors: validation.errors
        });
    }

    next();
};

export default schemaValidator;