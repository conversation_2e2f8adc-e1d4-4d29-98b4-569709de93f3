import { pool } from '../db.js';

/**
 * <PERSON><PERSON>per to call log_hipaa_audit_event(). Keeps defaults for most params.
 * @param {object} opts
 *  - userId
 *  - action (string)
 *  - resourceType (string)
 *  - resourceId (uuid)
 *  - oldValues (object|null)
 *  - newValues (object|null)
 */
export async function recordAudit({ userId, action, resourceType, resourceId, oldValues = null, newValues = null }) {
  try {
    await pool.query(
      'SELECT log_hipaa_audit_event($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12)',
      [
        userId,
        action,
        resourceType,
        resourceId,
        oldValues ? JSON.stringify(oldValues) : null,
        newValues ? JSON.stringify(newValues) : null,
        null, // session_id
        null, // request_id
        'system', // user_role (placeholder)
        action.toLowerCase(), // operation
        'write', // access_type
        'sensitive' // data_sensitivity
      ]
    );
  } catch (err) {
    console.error('[<PERSON><PERSON>] failed to log event', action, resourceType, resourceId, err);
  }
}
