import { pool } from '../db.js';

class SchemaService {
    /**
     * Get all active schemas - replaces reading policy_schemas.json
     * Returns: Object with schema_name as keys and schema_content as values
     */
    async getAllSchemas() {
        try {
            const query = `
                SELECT schema_name, schema_content, description, guardrail_id 
                FROM policy_schemas 
                WHERE is_active = true 
                ORDER BY schema_name
            `;
            const result = await pool.query(query);
            
            // Convert to existing format for backward compatibility
            const schemas = {};
            result.rows.forEach(row => {
                schemas[row.schema_name] = row.schema_content;
            });
            
            console.log(`Loaded ${result.rows.length} schemas from database`);
            return schemas;
        } catch (error) {
            console.error('Error loading schemas from database:', error);
            throw new Error('Failed to load schemas from database');
        }
    }
    
    /**
     * Get specific schema for validation
     */
    async getSchema(schemaName) {
        try {
            const query = `
                SELECT schema_content, guardrail_id 
                FROM policy_schemas 
                WHERE schema_name = $1 AND is_active = true
            `;
            const result = await pool.query(query, [schemaName]);
            return result.rows[0]?.schema_content || null;
        } catch (error) {
            console.error(`Error getting schema ${schemaName}:`, error);
            return null;
        }
    }
    
    /**
     * Get schema with guardrail information
     */
    async getSchemaWithGuardrail(schemaName) {
        try {
            const query = `
                SELECT schema_name, schema_content, description, guardrail_id, updated_at
                FROM policy_schemas 
                WHERE schema_name = $1 AND is_active = true
            `;
            const result = await pool.query(query, [schemaName]);
            return result.rows[0] || null;
        } catch (error) {
            console.error(`Error getting schema with guardrail ${schemaName}:`, error);
            return null;
        }
    }
    
    /**
     * Update or create schema (simple upsert)
     */
    async upsertSchema(schemaName, schemaContent, description = null, guardrailId = null) {
        try {
            const query = `
                INSERT INTO policy_schemas (schema_name, schema_content, description, guardrail_id)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (schema_name) 
                DO UPDATE SET 
                    schema_content = $2, 
                    description = COALESCE($3, policy_schemas.description),
                    guardrail_id = COALESCE($4, policy_schemas.guardrail_id),
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id, created_at, updated_at
            `;
            const result = await pool.query(query, [schemaName, schemaContent, description, guardrailId]);
            console.log(`Schema ${schemaName} upserted successfully`);
            return result.rows[0];
        } catch (error) {
            console.error(`Error upserting schema ${schemaName}:`, error);
            throw new Error(`Failed to update schema ${schemaName}`);
        }
    }
    
    /**
     * List all schema names (for admin UI)
     */
    async getSchemaNames() {
        try {
            const query = `
                SELECT schema_name, description, guardrail_id, updated_at 
                FROM policy_schemas 
                WHERE is_active = true 
                ORDER BY schema_name
            `;
            const result = await pool.query(query);
            return result.rows;
        } catch (error) {
            console.error('Error getting schema names:', error);
            throw new Error('Failed to get schema names');
        }
    }
    
    /**
     * Get schemas by guardrail ID
     */
    async getSchemasByGuardrailId(guardrailId) {
        try {
            const query = `
                SELECT schema_name, schema_content, description
                FROM policy_schemas 
                WHERE guardrail_id = $1 AND is_active = true 
                ORDER BY schema_name
            `;
            const result = await pool.query(query, [guardrailId]);
            return result.rows;
        } catch (error) {
            console.error(`Error getting schemas for guardrail ${guardrailId}:`, error);
            throw new Error('Failed to get schemas by guardrail');
        }
    }
    
    /**
     * Deactivate schema (soft delete)
     */
    async deactivateSchema(schemaName) {
        try {
            const query = `
                UPDATE policy_schemas 
                SET is_active = false, updated_at = CURRENT_TIMESTAMP
                WHERE schema_name = $1
                RETURNING id
            `;
            const result = await pool.query(query, [schemaName]);
            if (result.rows.length === 0) {
                throw new Error(`Schema not found: ${schemaName}`);
            }
            console.log(`Schema ${schemaName} deactivated`);
            return true;
        } catch (error) {
            console.error(`Error deactivating schema ${schemaName}:`, error);
            throw error;
        }
    }
}

export default new SchemaService();