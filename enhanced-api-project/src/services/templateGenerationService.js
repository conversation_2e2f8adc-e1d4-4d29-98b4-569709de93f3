import { pool } from '../db.js';

/**
 * Service for auto-generating and managing policy templates from JSON schemas
 * Implements hybrid approach: auto-generated, manual override, external provided
 */
class TemplateGenerationService {
    constructor() {
        this.templateCache = new Map();
        this.CACHE_TTL = 1 * 30 * 1000; // 30 seconds
    }

    /**
     * Generate a default template from a JSON Schema
     * Extracts defaults, const values, enum first values, and type-specific defaults
     * @param {Object} jsonSchema - The JSON Schema to generate template from
     * @returns {Object} Generated template with default values
     */
    generateFromSchema(jsonSchema) {
        if (!jsonSchema || !jsonSchema.properties) {
            return {};
        }

        const template = {};
        
        // Process each property in the schema
        for (const [fieldName, fieldDef] of Object.entries(jsonSchema.properties)) {
            const value = this.extractDefaultValue(fieldDef, fieldName);
            if (value !== undefined) {
                template[fieldName] = value;
            }
        }

        // Add required fields that don't have defaults
        if (jsonSchema.required && Array.isArray(jsonSchema.required)) {
            for (const requiredField of jsonSchema.required) {
                if (!(requiredField in template) && jsonSchema.properties[requiredField]) {
                    const fieldDef = jsonSchema.properties[requiredField];
                    template[requiredField] = this.getTypeDefault(fieldDef.type);
                }
            }
        }

        return template;
    }

    /**
     * Extract default value from a field definition
     * Priority: default > const > enum[0] > type-specific default
     */
    extractDefaultValue(fieldDef, fieldName) {
        // 1. Explicit default value
        if (fieldDef.default !== undefined) {
            return fieldDef.default;
        }

        // 2. Const value (fixed value)
        if (fieldDef.const !== undefined) {
            return fieldDef.const;
        }

        // 3. First enum value
        if (fieldDef.enum && Array.isArray(fieldDef.enum) && fieldDef.enum.length > 0) {
            return fieldDef.enum[0];
        }

        // 4. Handle nested objects
        if (fieldDef.type === 'object' && fieldDef.properties) {
            return this.generateFromSchema(fieldDef);
        }

        // 5. Handle arrays with minItems
        if (fieldDef.type === 'array') {
            if (fieldDef.minItems && fieldDef.minItems > 0) {
                // If items have enum, create array with first enum value
                if (fieldDef.items && fieldDef.items.enum && fieldDef.items.enum.length > 0) {
                    return [fieldDef.items.enum[0]];
                }
                return []; // Empty array for required arrays
            }
            return undefined; // Don't include optional arrays
        }

        // 6. Special handling for specific field names (domain knowledge)
        if (fieldName === 'type' && fieldDef.type === 'string') {
            // Policy type should match schema name
            return undefined; // Will be set by caller
        }

        // 7. Type-specific defaults for required fields only
        // (We'll check if required later)
        return undefined;
    }

    /**
     * Get type-specific default value
     */
    getTypeDefault(type) {
        switch (type) {
            case 'string':
                return '';
            case 'number':
            case 'integer':
                return 0;
            case 'boolean':
                return false;
            case 'array':
                return [];
            case 'object':
                return {};
            default:
                return null;
        }
    }

    /**
     * Get or generate template for a schema
     * Implements fallback logic: database > auto-generate > hardcoded
     */
    async getTemplate(schemaName) {
        // Check cache first
        const cached = this.getCachedTemplate(schemaName);
        if (cached) {
            return cached;
        }

        try {
            // 1. Try to get from database (manual override or previously generated)
            const dbResult = await pool.query(
                `SELECT default_template, template_source, schema_content 
                 FROM policy_schemas 
                 WHERE schema_name = $1 AND is_active = true`,
                [schemaName]
            );

            if (dbResult.rows.length > 0) {
                const row = dbResult.rows[0];
                
                // If template exists in database, use it
                if (row.default_template) {
                    this.cacheTemplate(schemaName, row.default_template);
                    return row.default_template;
                }

                // If no template but schema exists, auto-generate
                if (row.schema_content) {
                    const generated = this.generateFromSchema(row.schema_content);
                    // Add the type field based on schema name
                    generated.type = schemaName;
                    
                    // Save generated template to database for future use
                    await this.saveGeneratedTemplate(schemaName, generated);
                    
                    this.cacheTemplate(schemaName, generated);
                    return generated;
                }
            }

            // 2. Fallback to hardcoded templates (temporary during migration)
            const hardcoded = this.getHardcodedTemplate(schemaName);
            if (hardcoded) {
                console.warn(`Using hardcoded template for ${schemaName} - schema not found in database`);
                return hardcoded;
            }

            // 3. No template available
            console.error(`No template available for schema: ${schemaName}`);
            return null;

        } catch (error) {
            console.error(`Error getting template for ${schemaName}:`, error);
            // Fallback to hardcoded on error
            return this.getHardcodedTemplate(schemaName);
        }
    }

    /**
     * Save auto-generated template to database
     */
    async saveGeneratedTemplate(schemaName, template) {
        try {
            await pool.query(
                `UPDATE policy_schemas 
                 SET default_template = $1, 
                     template_source = 'auto_generated',
                     updated_at = CURRENT_TIMESTAMP
                 WHERE schema_name = $2`,
                [JSON.stringify(template), schemaName]
            );
            console.log(`Saved auto-generated template for ${schemaName}`);
        } catch (error) {
            console.error(`Error saving generated template for ${schemaName}:`, error);
        }
    }

    /**
     * Update template manually (admin override)
     */
    async updateTemplate(schemaName, template, source = 'manual_override') {
        try {
            const result = await pool.query(
                `UPDATE policy_schemas 
                 SET default_template = $1, 
                     template_source = $2,
                     updated_at = CURRENT_TIMESTAMP
                 WHERE schema_name = $3
                 RETURNING schema_name`,
                [JSON.stringify(template), source, schemaName]
            );

            if (result.rows.length === 0) {
                throw new Error(`Schema ${schemaName} not found`);
            }

            // Clear cache
            this.clearCache(schemaName);
            
            return { success: true, message: `Template updated for ${schemaName}` };
        } catch (error) {
            console.error(`Error updating template for ${schemaName}:`, error);
            throw error;
        }
    }

    /**
     * Reset template to auto-generated
     */
    async resetTemplate(schemaName) {
        try {
            // Get schema content
            const schemaResult = await pool.query(
                `SELECT schema_content FROM policy_schemas 
                 WHERE schema_name = $1 AND is_active = true`,
                [schemaName]
            );

            if (schemaResult.rows.length === 0) {
                throw new Error(`Schema ${schemaName} not found`);
            }

            // Generate new template from schema
            const generated = this.generateFromSchema(schemaResult.rows[0].schema_content);
            generated.type = schemaName;

            // Update database
            await this.updateTemplate(schemaName, generated, 'auto_generated');

            return { success: true, message: `Template reset to auto-generated for ${schemaName}` };
        } catch (error) {
            console.error(`Error resetting template for ${schemaName}:`, error);
            throw error;
        }
    }

    /**
     * Regenerate templates for all schemas (admin utility)
     */
    async regenerateAllTemplates(overrideManual = false) {
        try {
            const query = overrideManual 
                ? `SELECT schema_name, schema_content FROM policy_schemas WHERE is_active = true`
                : `SELECT schema_name, schema_content FROM policy_schemas 
                   WHERE is_active = true 
                   AND (template_source != 'manual_override' OR template_source IS NULL)`;

            const schemas = await pool.query(query);
            
            let updated = 0;
            let skipped = 0;
            
            for (const row of schemas.rows) {
                if (row.schema_content) {
                    const generated = this.generateFromSchema(row.schema_content);
                    generated.type = row.schema_name;
                    
                    await this.saveGeneratedTemplate(row.schema_name, generated);
                    updated++;
                } else {
                    skipped++;
                }
            }

            return { 
                success: true, 
                message: `Regenerated ${updated} templates, skipped ${skipped}` 
            };
        } catch (error) {
            console.error('Error regenerating all templates:', error);
            throw error;
        }
    }

    /**
     * Handle schema update from external system
     */
    async handleSchemaUpdate(schemaName, schemaContent) {
        try {
            // Check current template source
            const current = await pool.query(
                `SELECT template_source FROM policy_schemas 
                 WHERE schema_name = $1`,
                [schemaName]
            );

            // Only regenerate if not manually overridden
            if (current.rows.length > 0 && current.rows[0].template_source === 'manual_override') {
                console.log(`Preserving manual template override for ${schemaName}`);
                return { preserved: true };
            }

            // Generate new template from updated schema
            const generated = this.generateFromSchema(schemaContent);
            generated.type = schemaName;

            // Save to database
            await this.saveGeneratedTemplate(schemaName, generated);

            // Clear cache
            this.clearCache(schemaName);

            return { regenerated: true };
        } catch (error) {
            console.error(`Error handling schema update for ${schemaName}:`, error);
            throw error;
        }
    }

    // Cache management methods
    getCachedTemplate(schemaName) {
        const cached = this.templateCache.get(schemaName);
        if (cached && (Date.now() - cached.timestamp < this.CACHE_TTL)) {
            return cached.template;
        }
        return null;
    }

    cacheTemplate(schemaName, template) {
        this.templateCache.set(schemaName, {
            template,
            timestamp: Date.now()
        });
    }

    clearCache(schemaName) {
        if (schemaName) {
            this.templateCache.delete(schemaName);
        } else {
            this.templateCache.clear();
        }
    }

    /**
     * Temporary hardcoded templates for backward compatibility
     * TO BE REMOVED after migration complete
     */
    getHardcodedTemplate(schemaName) {
        const templates = {
            medical_privacy: {
                type: "medical_privacy",
                severity: "medium",
                allowed_roles: ["doctor"],
                hipaa_compliance: true,
                protected_fields: ["diagnosis"],
                audit_requirements: {
                    log_access: true,
                    retention_period: 7,
                    encryption_required: true,
                    access_timeout: 30
                },
                data_handling: {
                    anonymization: false,
                    pseudonymization: true,
                    data_minimization: true
                }
            },
            data_privacy: {
                type: "data_privacy",
                severity: "medium",
                allowed_roles: ["admin"],
                data_classification: "confidential",
                protected_fields: ["personal_info"],
                consent_requirements: {
                    explicit_consent: true,
                    consent_expiry: 12,
                    withdrawal_allowed: true
                },
                data_retention: {
                    retention_period: 24,
                    auto_deletion: true,
                    archive_after: 12
                }
            },
            access_control: {
                type: "access_control",
                severity: "medium",
                allowed_roles: ["admin"],
                time_restrictions: {
                    start_time: "09:00",
                    end_time: "17:00",
                    timezone: "UTC",
                    allowed_days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
                },
                session_management: {
                    max_session_duration: 60,
                    inactivity_timeout: 15,
                    concurrent_sessions: 3
                }
            },
            compliance: {
                type: "compliance",
                severity: "medium",
                allowed_roles: ["compliance_officer"],
                regulatory_framework: "gdpr",
                compliance_requirements: ["data_encryption"],
                audit_frequency: "quarterly",
                reporting_requirements: {
                    incident_reporting: true,
                    reporting_timeframe: 24,
                    regulatory_notifications: true
                }
            }
        };

        return templates[schemaName] || null;
    }
}

// Export singleton instance
const templateGenerationService = new TemplateGenerationService();
export default templateGenerationService;