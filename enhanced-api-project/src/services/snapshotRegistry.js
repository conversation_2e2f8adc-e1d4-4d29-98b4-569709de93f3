import { randomUUID } from 'crypto';

const SNAPSHOT_TTL_MS = 15 * 60 * 1000; // 15 minutes
const registry = new Map(); // snapshot_id -> { asOf: Date, expiresAt: number }

export function createSnapshot() {
  const id = randomUUID();
  const asOf = new Date();
  const expiresAt = Date.now() + SNAPSHOT_TTL_MS;
  registry.set(id, { asOf, expiresAt });
  return { id, asOf };
}

export function getSnapshot(snapshotId) {
  const rec = registry.get(snapshotId);
  if (!rec) return null;
  if (Date.now() > rec.expiresAt) {
    registry.delete(snapshotId);
    return null;
  }
  return rec;
}

