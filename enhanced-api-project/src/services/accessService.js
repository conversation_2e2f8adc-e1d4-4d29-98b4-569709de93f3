import { pool } from '../db.js';

/**
 * Fetch Role records for a given user.
 * @param {string} userId UUID of the user
 * @returns {Promise<Array<{role_id: string, code: string}>>}
 */
export async function getUserRoles(userId) {
  const { rows } = await pool.query(
    `SELECT r.role_id, r.code
       FROM roles r
       JOIN user_roles ur ON ur.role_id = r.role_id
      WHERE ur.user_id = $1`,
    [userId]
  );
  return rows;
}

/**
 * Fetch agent IDs the user can access via their roles.
 * Returns an array of objects with agent_id and access_level.
 * @param {string[]} roleIds array of role UUIDs
 * @returns {Promise<Array<{agent_id: string, access_level: string}>>}
 */
export async function getAllowedAgents(roleIds) {
  if (!roleIds.length) return [];

  const { rows } = await pool.query(
    `SELECT DISTINCT agent_id, access_level
       FROM agent_access
      WHERE role_id = ANY($1::uuid[])`,
    [roleIds]
  );
  return rows;
}
