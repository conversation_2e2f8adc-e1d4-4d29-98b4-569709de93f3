import axios from 'axios';
import { fetchPendingOutbox, markDelivered, markFailedAndSchedule, signPayloadHmacBase64, buildWebhookHeaders } from '../utils/integrations.js';
import { createMetricsRegistry, incrementCounter, observeHistogram } from '../utils/metrics.js';

const WEBHOOK_URLS = (process.env.INTEGRATION_WEBHOOK_URLS || '').split(',').map(s => s.trim()).filter(Boolean);
const SECRET_ACTIVE = process.env.INTEGRATION_WEBHOOK_SECRET_ACTIVE || '';

const metrics = createMetricsRegistry();
export async function dispatchPendingOnce(limit = 50) {
  if (!process.env.INTEGRATIONS_ENABLED || WEBHOOK_URLS.length === 0) return;
  const rows = await fetchPendingOutbox(limit);
  for (const row of rows) {
    const payload = row.payload_json || {};
    const rawBody = JSON.stringify(payload);
    const headers = buildWebhookHeaders(row);
    const signature = SECRET_ACTIVE ? signPayloadHmacBase64(SECRET_ACTIVE, rawBody) : '';
    const signedHeaders = { ...headers, 'X-Signature': signature };

    try {
      const start = Date.now();
      // Send to all configured URLs; consider success if any returns 2xx
      const results = await Promise.allSettled(
        WEBHOOK_URLS.map((url) => axios.post(url, rawBody, { headers: signedHeaders, timeout: 5000 }))
      );
      const anySuccess = results.some((r) => r.status === 'fulfilled' && r.value.status >= 200 && r.value.status < 300);
      if (anySuccess) {
        await markDelivered(row.id);
        observeHistogram('dispatcher_latency_seconds', (Date.now() - start) / 1000, { outcome: 'delivered' }, metrics);
        incrementCounter('dispatcher_delivered_total', { event_type: row.event_type }, 1, metrics);
      } else {
        const errMsg = results.map((r) => r.status === 'rejected' ? (r.reason?.message || 'error') : String(r.value.status)).join(';');
        await markFailedAndSchedule(row.id, row.attempts, errMsg);
        observeHistogram('dispatcher_latency_seconds', 0, { outcome: 'failed' }, metrics);
        incrementCounter('dispatcher_failed_total', { event_type: row.event_type }, 1, metrics);
      }
    } catch (err) {
      await markFailedAndSchedule(row.id, row.attempts, err?.message || 'dispatch error');
      incrementCounter('dispatcher_failed_total', { event_type: row.event_type }, 1, metrics);
    }
  }
}

let intervalHandle = null;
export function startDispatcher(intervalMs = 1000) {
  if (intervalHandle) return;
  intervalHandle = setInterval(() => {
    dispatchPendingOnce().catch(() => {});
  }, intervalMs);
}

export function stopDispatcher() {
  if (intervalHandle) clearInterval(intervalHandle);
  intervalHandle = null;
}

