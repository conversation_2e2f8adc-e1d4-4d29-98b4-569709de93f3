// In a real app, this would be a robust JWT validation middleware.
// For demonstration, it's a simplified check for an 'admin' keyword in the Authorization header.

import { getUserRoles, getAllowedAgents } from '../services/accessService.js';

/**
 * Lightweight auth middleware
 * 1. Verifies Authorization header contains a recognised token ("admin" demo token)
 * 2. Loads user roles and allowed agents from DB and attaches to req.user
 * NOTE: Replace with full JWT validation in production.
 */
export const requireAdmin = async (req, res, next) => {
  try {
    // Skip auth in development mode
    if (process.env.NODE_ENV === 'development') {
      // Set default admin user for development - skip database calls
      req.user = {
        id: '00000000-0000-0000-0000-000000000000',
        roles: ['admin'],
        roleIds: [],
        allowedAgents: [],
        agentAccess: {}
      };
      
      return next();
    }
    
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.includes('admin')) {
      return res.status(401).json({ error: 'Admin access required' });
    }

    // Static admin user for demo purposes. In real use, decode JWT to get userId
    const userId = '00000000-0000-0000-0000-000000000000';

    // Fetch roles
    const roles = await getUserRoles(userId);
    const roleIds = roles.map((r) => r.role_id);

    // Fetch allowed agents via roles
    const allowedAgentsRaw = await getAllowedAgents(roleIds);
    const allowedAgents = allowedAgentsRaw.map((r) => r.agent_id);

    req.user = {
      id: userId,
      roles: roles.map((r) => r.code),
      roleIds,
      allowedAgents,
      // map of agent_id -> access_level for finer-grained checks if needed
      agentAccess: Object.fromEntries(allowedAgentsRaw.map((r) => [r.agent_id, r.access_level]))
    };

    next();
  } catch (err) {
    console.error('[Auth] Error resolving user roles/agents:', err);
    return res.status(500).json({ error: 'Authentication failure' });
  }
}; 