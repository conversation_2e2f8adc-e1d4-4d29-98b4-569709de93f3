// Minimal OAuth2 Client Credentials validator for development
// Accepts any Bear<PERSON> token matching INTEGRATIONS_TEST_TOKEN.
// Replace with real JWT validation or introspection in production.

export function requireOAuthClient(req, res, next) {
  try {
    const auth = req.headers.authorization || '';
    if (!auth.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    const token = auth.slice('Bearer '.length).trim();
    // Mode switch: if OAuth issuer configured, validate JWT; else use test token
    const issuer = process.env.INTEGRATIONS_OAUTH2_ISSUER;
    if (issuer) {
      // Placeholder for future JWT validation; currently accept any token in dev until configured
      // Implement jwks validation here when IdP is ready
      if (!token) return res.status(401).json({ error: 'Invalid token' });
      return next();
    }
    const expected = process.env.INTEGRATIONS_TEST_TOKEN || '';
    if (!expected || token !== expected) {
      return res.status(401).json({ error: 'Invalid token' });
    }
    return next();
  } catch (err) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
}

