import { jest } from '@jest/globals';

// ESM-friendly mocking: define module mock BEFORE importing the subject under test
const actionsLog = [];
jest.unstable_mockModule('../db.js', () => ({
  pool: {
    query: jest.fn(async (sql, params = []) => {
      if (typeof sql === 'string' && sql.includes('log_hipaa_audit_event')) {
        // param[1] is the action per recordAudit()
        actionsLog.push(params[1]);
        return { rows: [] };
      }
      if (typeof sql === 'string' && sql.includes('SELECT action FROM hipaa_audit_log')) {
        return { rows: actionsLog.map((a) => ({ action: a })) };
      }
      return { rows: [] };
    })
  }
}));

const { recordAudit } = await import('../utils/auditLogger.js');
const { pool } = await import('../db.js');

describe('log_hipaa_audit_event integration', () => {
  it('inserts a row for each action', async () => {
    const actions = [
      'CREATE',
      'UPDATE',
      'DEPRECATE',
      'RESTORE',
      'ADD_POLICY',
      'REMOVE_POLICY',
      'ROLE_CREATED',
      'ROLE_UPDATED',
      'ROLE_DELETED',
      'BULK_POLICY_ASSIGN'
    ];

    for (const action of actions) {
      await recordAudit({
        userId: 'user-123',
        action,
        resourceType: 'test_resource',
        resourceId: null,
        oldValues: null,
        newValues: { sample: true }
      });
    }

    const { rows } = await pool.query('SELECT action FROM hipaa_audit_log ORDER BY id');
    expect(rows.map((r) => r.action)).toEqual(actions);
  });
});