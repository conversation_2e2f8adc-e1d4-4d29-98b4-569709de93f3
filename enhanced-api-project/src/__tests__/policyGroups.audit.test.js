import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';

// ESM-first mocks must be declared before importing the module under test
const insertedRows = [{ group_id: '11111111-1111-1111-1111-111111111111', name: 'Test Group' }];
jest.unstable_mockModule('../utils/auditLogger.js', () => ({
  recordAudit: jest.fn().mockResolvedValue()
}));
jest.unstable_mockModule('../db.js', () => ({
  pool: {
    query: jest.fn(async (sql) => {
      if (typeof sql === 'string' && sql.includes('INSERT INTO policy_groups')) {
        return { rows: insertedRows };
      }
      return { rows: [] };
    })
  }
}));
jest.unstable_mockModule('../services/accessService.js', () => ({
  getUserRoles: jest.fn(async () => []),
  getAllowedAgents: jest.fn(async () => [])
}));

const { recordAudit } = await import('../utils/auditLogger.js');
const { default: router } = await import('../api/policyGroups.js');

const app = express();
app.use(express.json());
// inject dummy auth header to satisfy requireAdmin
app.use('/', (req, res, next) => {
  req.headers.authorization = 'Bearer admin';
  req.user = { user_id: 'user-123', allowedAgents: [] };
  next();
});
app.use('/', router);

describe('PolicyGroups audit logging', () => {
  it('calls recordAudit on create', async () => {
    await request(app)
      .post('/')
      .set('Authorization', 'Bearer admin')
      .send({ name: 'Test Group' })
      .expect(201);
    expect(recordAudit).toHaveBeenCalledWith(
      expect.objectContaining({ action: 'CREATE', resourceType: 'policy_group' })
    );
  });
});