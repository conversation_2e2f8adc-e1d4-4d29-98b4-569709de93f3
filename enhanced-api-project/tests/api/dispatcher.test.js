import { computeNextBackoffSeconds } from '../../src/utils/integrations.js';

describe('Dispatcher backoff', () => {
  test('increases with attempts and is bounded', () => {
    const a0 = computeNextBackoffSeconds(0);
    const a1 = computeNextBackoffSeconds(1);
    const a5 = computeNextBackoffSeconds(5);
    const a10 = computeNextBackoffSeconds(10);
    expect(a0).toBeGreaterThanOrEqual(0);
    expect(a10).toBeLessThanOrEqual(7200);
    // Non-deterministic, but ensure general monotonic upper bounds are higher
    expect(Math.max(a1, a5, a10)).toBeGreaterThanOrEqual(Math.min(a1, a5, a10));
  });
});

