import { newDb } from 'pg-mem';
import nock from 'nock';
import axios from 'axios';
import { createHmac, randomUUID } from 'crypto';

// Inline copy of minimal functions to avoid full app boot
function signPayloadHmacBase64(secret, rawBody) {
  return createHmac('sha256', secret).update(rawBody).digest('base64');
}

describe('Dispatcher integration (outbox -> webhook)', () => {
  test('marks delivered on 200 from receiver', async () => {
    const db = newDb({ autoCreateForeignKeyIndices: true });

    // Register uuid-ossp extension for pg-mem (uuid_generate_v4)
    db.registerExtension('uuid-ossp', (schema) => {
      schema.registerFunction({
        name: 'uuid_generate_v4',
        returns: 'uuid',
        implementation: () => randomUUID(),
      });
    });
    const { Pool } = db.adapters.createPg();
    const pool = new Pool();

    // Create tables used by dispatcher
    await pool.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      CREATE TABLE integration_outbox (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        destination TEXT NOT NULL DEFAULT 'webhook',
        event_type TEXT NOT NULL,
        event_version INTEGER NOT NULL DEFAULT 1,
        payload_json JSONB NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        attempts INTEGER NOT NULL DEFAULT 0,
        next_attempt_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
        last_error TEXT,
        created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    const event = { event_id: 'e1', event_version: 1, event_type: 'policy.updated', subject: { resource_type: 'policy', resource_id: 'p1' } };
    await pool.query(`INSERT INTO integration_outbox (event_type, payload_json) VALUES ($1, $2)`, [event.event_type, event]);

    // Mock webhook
    const url = 'https://webhook.example';
    const path = '/notify';
    const secret = 's';
    const rawBody = JSON.stringify(event);
    const sig = signPayloadHmacBase64(secret, rawBody);
    nock(url).post(path, rawBody).reply(200, {}, {
      'X-Signature': sig,
    });

    // Minimal fetchPending/mark functions inline using this pool
    const { rows } = await pool.query(`SELECT * FROM integration_outbox WHERE status='pending'`);
    expect(rows.length).toBe(1);

    // Simulate dispatch
    await axios.post(`${url}${path}`, rawBody, {
      headers: { 'X-Signature': sig, 'Content-Type': 'application/json' },
      timeout: 2000,
    });
    await pool.query(`UPDATE integration_outbox SET status='delivered' WHERE id=$1`, [rows[0].id]);

    const delivered = await pool.query(`SELECT status FROM integration_outbox WHERE id=$1`, [rows[0].id]);
    expect(delivered.rows[0].status).toBe('delivered');
  });
});

