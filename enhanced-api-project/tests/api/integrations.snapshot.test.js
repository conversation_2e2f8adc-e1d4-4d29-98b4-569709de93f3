import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';
import integrations from '../../src/api/integrations.js';

// Mock DB pool to avoid undefined pool.query in snapshot route
jest.unstable_mockModule('../../src/db.js', () => ({
  pool: { query: jest.fn(async () => ({ rows: [] })) }
}));

// Minimal app wrapper
const app = express();
app.use(express.json());
app.use('/api/v1/integrations', integrations);

describe('Integrations Snapshot API', () => {
  test('rejects without bearer token', async () => {
    const res = await request(app).get('/api/v1/integrations/assignments-snapshot');
    expect(res.status).toBe(401);
  });

  test('accepts with valid bearer token (no data)', async () => {
    process.env.INTEGRATIONS_TEST_TOKEN = 'test-token';
    const res = await request(app)
      .get('/api/v1/integrations/assignments-snapshot')
      .set('Authorization', 'Bearer test-token');
    // In this isolated test there is no DB, so it will 500. We just assert auth passed.
    expect([200, 500]).toContain(res.status);
  });
});

