import { newDb } from 'pg-mem';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { recordAudit } from '../src/utils/auditLogger.js';

// set up in-memory db and override pool used by modules
const db = newDb({ autoCreateForeignKeyIndices: true });
const { Pool } = db.adapters.createPg();
const pool = new Pool();

import * as dbModule from '../src/db.js';
dbModule.pool = pool;

// Helper to run all migrations
beforeAll(async () => {
  const migrationsDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '../../configs');
  const files = fs.readdirSync(migrationsDir).filter(f => /^2025\d{4}_\d+.*\.sql$/.test(f)).sort();
  for (const f of files) {
    await pool.query(fs.readFileSync(path.join(migrationsDir, f), 'utf8'));
  }
});

afterAll(async () => {
  await pool.end();
});

describe('auditLogger', () => {
  test('recordAudit inserts row into audit_log', async () => {
    const before = await pool.query('SELECT COUNT(*) FROM audit_log');
    await recordAudit({ userId: '00000000-0000-0000-0000-000000000000', action: 'TEST_ACTION', resourceType: 'test', resourceId: '11111111-1111-1111-1111-111111111111', newValues: { foo: 'bar' } });
    const after = await pool.query('SELECT COUNT(*) FROM audit_log');
    expect(Number(after.rows[0].count)).toBe(Number(before.rows[0].count) + 1);
    const { rows } = await pool.query("SELECT * FROM audit_log ORDER BY timestamp DESC LIMIT 1");
    expect(rows[0].action).toBe('TEST_ACTION');
  });
});
