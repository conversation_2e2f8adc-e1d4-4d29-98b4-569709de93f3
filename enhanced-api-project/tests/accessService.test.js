import { newDb } from 'pg-mem';
import { getUserRoles, getAllowedAgents } from '../src/services/accessService.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Init in-memory db & run migrations once for all tests
const db = newDb({ autoCreateForeignKeyIndices: true });
const { Pool } = db.adapters.createPg();
const pool = new Pool();
// patch pool export used by service to use in-memory db
import * as dbModule from '../src/db.js';
dbModule.pool = pool;

beforeAll(async () => {
  const migrationsDir = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '../../configs');
  const files = fs.readdirSync(migrationsDir).filter((f) => /^2025\d{4}_\d+.*\.sql$/.test(f)).sort();
  for (const f of files) {
    await pool.query(fs.readFileSync(path.join(migrationsDir, f), 'utf8'));
  }

  // seed demo data
  const userId = '11111111-1111-1111-1111-111111111111';
  const roleId = '22222222-2222-2222-2222-222222222222';
  const agentId = '33333333-3333-3333-3333-333333333333';

  await pool.query(`INSERT INTO users (user_id, azure_ad_id, email, role) VALUES ($1,'aad','<EMAIL>','admin')`, [userId]);
  await pool.query(`INSERT INTO roles (role_id, code, name) VALUES ($1,'ADMIN','Administrator')`, [roleId]);
  await pool.query(`INSERT INTO agents (agent_id, name) VALUES ($1,'TestAgent')`, [agentId]);
  await pool.query(`INSERT INTO user_roles (user_id, role_id) VALUES ($1,$2)`, [userId, roleId]);
  await pool.query(`INSERT INTO agent_access (agent_id, role_id, access_level) VALUES ($1,$2,'manage')`, [agentId, roleId]);
});

afterAll(async () => {
  await pool.end();
});

describe('accessService', () => {
  test('getUserRoles returns roles', async () => {
    const roles = await getUserRoles('11111111-1111-1111-1111-111111111111');
    expect(roles).toHaveLength(1);
    expect(roles[0].code).toBe('ADMIN');
  });
  test('getAllowedAgents returns agent ids', async () => {
    const allowed = await getAllowedAgents(['22222222-2222-2222-2222-222222222222']);
    expect(allowed).toHaveLength(1);
    expect(allowed[0].agent_id).toBe('33333333-3333-3333-3333-333333333333');
  });
});
