/*
 * Migration integration test
 * Runs all SQL migrations sequentially against an in-memory Postgres instance (pg-mem)
 * to verify they execute without error and expected tables exist.
 */
import { newDb } from 'pg-mem';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Relative path from test file to configs directory
const MIGRATIONS_DIR = path.resolve(__dirname, '../../configs');

function readMigrationFiles() {
  const files = fs
    .readdirSync(MIGRATIONS_DIR)
    .filter((f) => /^2025\d{4}_\d+.*\.sql$/.test(f))
    .sort(); // alphanumeric sort ensures timestamp+sequence order
  return files.map((f) => ({ name: f, sql: fs.readFileSync(path.join(MIGRATIONS_DIR, f), 'utf8') }));
}

describe('SQL migrations', () => {
  const db = newDb({ autoCreateForeignKeyIndices: true });
  const { Pool } = db.adapters.createPg();

  const pool = new Pool();

  afterAll(async () => {
    await pool.end();
  });

  const migrations = readMigrationFiles();

  for (const { name, sql } of migrations) {
    test(`runs migration ${name} without error`, async () => {
      await pool.query(sql);
    });
  }

  test('creates expected core tables', async () => {
    const res = await pool.query("SELECT table_name FROM information_schema.tables WHERE table_schema='public'");
    const tables = res.rows.map((r) => r.table_name);
    expect(tables).toEqual(expect.arrayContaining([
      'roles',
      'user_roles',
      'policy_groups',
      'policy_group_policies',
      'agent_access',
      'agent_policies'
    ]));
  });
});
