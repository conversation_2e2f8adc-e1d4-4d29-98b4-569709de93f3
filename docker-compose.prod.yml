version: '3.8'

services:
  # API Server - Production version using ACR image
  pilot-api:
    image: viteadevacr.azurecr.io/pilot-api:latest
    container_name: pilot-api
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${PORT:-8000}
      DB_HOST: ${DB_HOST:-pilot-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-vitea123}
      DB_NAME: ${DB_NAME:-vitea_db}
      DB_USER: ${DB_USER:-dbadmin}
      # Azure OpenAI configuration
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT}
      AZURE_OPENAI_KEY: ${AZURE_OPENAI_KEY}
      AZURE_OPENAI_DEPLOYMENT_NAME: ${AZURE_OPENAI_DEPLOYMENT_NAME}
      # MCP configuration
      MCP_SERVER_ENABLED: ${MCP_SERVER_ENABLED:-true}
      MCP_SERVER_HOST: ${MCP_SERVER_HOST:-mcp-server}
    ports:
      - "8001:8000"
    depends_on:
      pilot-postgres:
        condition: service_healthy
    volumes:
      - ./enhanced-api-project/configs:/app/configs:ro
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend - Production version using ACR image
  pilot-frontend:
    image: viteadevacr.azurecr.io/pilot-frontend:latest
    container_name: pilot-frontend
    environment:
      API_URL: http://pilot-api:8000
      # Note: React variables are baked into image at build time, not runtime
    ports:
      - "3001:80"
    depends_on:
      - pilot-api