.PHONY: up down build logs clean restart api-rebuild api-restart api-logs api-shell

# Default target
up:
	docker-compose up -d

# Bring containers down
down:
	docker-compose down

# Build containers
build:
	docker-compose build

# Follow API logs by default
logs:
	docker-compose logs -f pilot-api

# View all logs
logs-all:
	docker-compose logs -f

# Clean up (remove containers, networks, volumes)
clean:
	docker-compose down -v --remove-orphans

# Restart services
restart: down up

# Build and start
rebuild: build up

# Show running containers
status:
	docker-compose ps

# API-specific commands
# Rebuild and restart just the API service (picks up code and .env changes)
api-rebuild:
	@echo "Stopping and rebuilding API service..."
	docker-compose stop pilot-api
	docker-compose build pilot-api
	docker-compose up -d pilot-api
	@echo "API service rebuilt and restarted successfully!"

# Restart API service without rebuild (picks up .env changes only)
api-restart:
	@echo "Restarting API service..."
	docker-compose restart pilot-api
	@echo "API service restarted successfully!"

# View API logs
api-logs:
	docker-compose logs -f pilot-api

# Shell into API container
api-shell:
	docker-compose exec pilot-api sh

# Help
help:
	@echo "Available targets:"
	@echo "  up           - Start all services in detached mode"
	@echo "  down         - Stop all services"
	@echo "  build        - Build all containers"
	@echo "  logs         - Follow API logs (default)"
	@echo "  logs-all     - Follow logs for all services"
	@echo "  clean        - Stop and remove containers, networks, volumes"
	@echo "  restart      - Stop and start services"
	@echo "  rebuild      - Build and start services"
	@echo "  status       - Show running containers"
	@echo ""
	@echo "API-specific commands:"
	@echo "  api-rebuild  - Rebuild and restart API service (code + env changes)"
	@echo "  api-restart  - Restart API service (env changes only)"
	@echo "  api-logs     - Follow API logs"
	@echo "  api-shell    - Open shell in API container"
	@echo "  help         - Show this help message"
