### Webhook Specification (Outbound events from us → external system)

Endpoint(s)
- Configured by you per environment (preview/prod). Example (preview):
  - `POST https://<partner-host>/webhooks/v1/policy-events`
  - `POST https://<partner-host>/webhooks/v1/assignment-events`

HTTP
- Method: `POST`
- Content-Type: `application/json`
- Retries: at-least-once delivery with exponential backoff and jitter. After max attempts, events are moved to DLQ and can be replayed.

Headers
- `X-Event-Id`: UUIDv4 unique per event
- `X-Event-Type`: One of:
  - `agent.created` | `agent.updated` | `agent.deleted`
  - `role.created` | `role.updated` | `role.deleted`
  - `policy.created` | `policy.updated` | `policy.deleted`
  - `assignment.linked` | `assignment.unlinked`
- `X-Event-Version`: Integer (starts at `1`)
- `X-Timestamp`: RFC3339 UTC timestamp of event emission
- `X-Correlation-Id`: Correlates across API calls and logs
- `X-Signature`: Base64 of `HMAC_SHA256(secret, raw_body)`

Signature verification
1) Read raw request body bytes (exactly as sent)
2) Compute `HMAC_SHA256(secret, raw_body)`
3) Base64-encode the digest
4) Compare with `X-Signature` header (constant-time comparison recommended)

Payload envelope (no PHI)
{
  "event_id": "9c2c1c9e-5f1b-4b70-8b5a-8f9a5c0a2d3f",
  "event_version": 1,
  "event_type": "assignment.linked",
  "occurred_at": "2025-08-12T03:40:40.309Z",
  "correlation_id": "b2273d30-9c2a-4a39-8d6d-9d8f3e5e4f7f",
  "subject": {
    "resource_type": "agent_role_policy",
    "resource_id": {
      "agent_id": "597df3ba-ae0d-4e38-ba40-aab0f84beb88",
      "role_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
      "group_id": "210374f9-7697-48ff-bf11-582126a5be7b",
      "policy_id": "d6ac7fbd-37d9-4b81-824e-7ee86dc5a412"
    }
  },
  "data": {
    "change_type": "created"
  }
}

Notes
- No PHI is included. Only metadata/identifiers and policy references are present.
- The event signals the external system to call our snapshot API for the authoritative state.

