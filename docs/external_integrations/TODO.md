### TODO — External Integrations v1

Phase A — Schema & Config
- [ ] Create `integration_outbox` table and indexes
- [ ] Create `integration_dlq` table and indexes
- [ ] Add env config: `INTEGRATION_WEBHOOK_URLS`, `INTEGRATION_WEBHOOK_SECRET_ACTIVE`, `INTEGRATION_WEBHOOK_SECRET_NEXT`, `INTEGRATIONS_ENABLED`

Phase B — Utilities
- [ ] HMAC signer/validator utility with secret rotation
- [ ] Backoff with jitter utility
- [ ] Cursor codec (composite key <-> opaque string)

Phase C — Event Production
- [ ] Hook agents CRUD to enqueue outbox events
- [ ] Hook roles CRUD to enqueue outbox events
- [ ] Hook policies CRUD to enqueue outbox events
- [ ] Hook assignment link/unlink to enqueue outbox events

Phase D — Dispatcher
- [ ] Background worker to deliver webhooks with retries
- [ ] DLQ handling and admin replay endpoint
- [ ] Metrics for dispatcher (latency, attempts, failures)

Phase E — Auth & API
- [ ] Implement OAuth2 Client Credentials validation middleware
- [ ] Implement `GET /api/v1/integrations/assignments-snapshot` (full snapshot, cursor-based)
- [ ] Enforce no PHI in payloads; add schema validations

Phase F — Observability & Audit
- [ ] Structured audit logs for CRUD, outbox enqueue, deliveries
- [ ] Metrics and tracing instrumentation

Phase G — Tests
- [ ] Unit tests for utilities and repositories
- [ ] Integration tests for CRUD→outbox→dispatcher→DLQ
- [ ] Snapshot API tests (schema, pagination, perf)
- [ ] Security tests (OAuth2, HMAC, rate limits)

Phase H — Docs & Samples
- [ ] Example webhook receiver with HMAC verification
- [ ] Example snapshot consumer with cursor loop

Phase I — Rollout
- [ ] Staging dry run with external partner stub
- [ ] Production readiness checklist (secrets, limits, alerts)

