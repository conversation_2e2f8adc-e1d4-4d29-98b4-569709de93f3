# External Integration Testing Guide

## Overview

This guide explains how to implement and test external integrations with the Vitea system. The system uses a webhook + snapshot API pattern where:

1. **Webhooks**: Your system sends events to external systems when changes occur
2. **Snapshot API**: External systems pull full state via paginated API
3. **Security**: HMAC-SHA256 signed webhooks with OAuth2/Bearer token authentication

## API Call Patterns for External Systems

### 1. Receiving Webhooks from Vitea

External systems should implement a webhook receiver that:

#### Headers Received:
```
X-Event-Id: UUIDv4 unique per event
X-Event-Type: agent.created|updated|deleted, role.*, policy.*, assignment.linked|unlinked
X-Event-Version: Integer (starts at 1)
X-Timestamp: RFC3339 UTC timestamp
X-Correlation-Id: For tracing/debugging
X-Signature: Base64 of HMAC_SHA256(secret, raw_body)
Content-Type: application/json
```

#### Example Webhook Payload:
```json
{
  "event_id": "9c2c1c9e-5f1b-4b70-8b5a-8f9a5c0a2d3f",
  "event_version": 1,
  "event_type": "assignment.linked",
  "occurred_at": "2025-08-12T03:40:40.309Z",
  "correlation_id": "b2273d30-9c2a-4a39-8d6d-9d8f3e5e4f7f",
  "subject": {
    "resource_type": "agent_role_policy",
    "resource_id": {
      "agent_id": "597df3ba-ae0d-4e38-ba40-aab0f84beb88",
      "role_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
      "group_id": "210374f9-7697-48ff-bf11-582126a5be7b",
      "policy_id": "d6ac7fbd-37d9-4b81-824e-7ee86dc5a412"
    }
  },
  "data": {
    "change_type": "created"
  }
}
```

#### Webhook Receiver Implementation:
```javascript
// Based on existing webhook-receiver.js
function verifySignature(rawBody, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(rawBody)
    .digest('base64');
  return expectedSignature === signature;
}

app.post('/webhooks/policy-events', (req, res) => {
  try {
    const rawBody = JSON.stringify(req.body);
    const signature = req.headers['x-signature'];
    const eventId = req.headers['x-event-id'];
    
    // Verify HMAC signature
    if (!verifySignature(rawBody, signature, process.env.WEBHOOK_SECRET)) {
      return res.status(401).json({ error: 'Invalid signature' });
    }
    
    // Check for duplicate events (idempotency)
    if (isEventProcessed(eventId)) {
      return res.status(200).json({ status: 'already_processed' });
    }
    
    // Process the webhook event
    await processWebhookEvent(req.body);
    markEventProcessed(eventId);
    
    res.status(200).json({ status: 'processed' });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Processing failed' });
  }
});
```

### 2. Pulling State via Snapshot API

After receiving a webhook, external systems should pull the full current state:

```javascript
async function pullAssignmentSnapshot() {
  let cursor = null;
  const allAssignments = [];
  
  do {
    const params = new URLSearchParams({
      page_size: '500',
      ...(cursor && { cursor })
    });
    
    const response = await fetch(
      `https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1/integrations/assignments-snapshot?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.INTEGRATIONS_TEST_TOKEN}`
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`Snapshot API error: ${response.status}`);
    }
    
    const data = await response.json();
    allAssignments.push(...data.items);
    cursor = data.next_cursor;
    
  } while (cursor);
  
  return allAssignments;
}
```

### 3. Making Updates to Vitea System

For testing external systems making updates to Vitea:

#### Update Agent Role Policies:
```javascript
async function updateAgentRolePolicies(agentId, updates) {
  // 1. Update agent active status
  if (updates.agent_active !== undefined) {
    await fetch(`${API_BASE}/agents/${agentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin-token'
      },
      body: JSON.stringify({
        is_active: updates.agent_active
      })
    });
  }
  
  // 2. Bulk assign policies
  if (updates.policy_ids && updates.policy_ids.length > 0) {
    await fetch(`${API_BASE}/agents/${agentId}/role-policies/bulk`, {
      method: 'POST', 
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin-token'
      },
      body: JSON.stringify({
        roleId: updates.roleId,
        groupId: updates.groupId,
        policyIds: updates.policy_ids
      })
    });
  }
}
```

#### Update Policy Status:
```javascript
async function updatePolicy(policyId, updates) {
  // First get current policy details
  const currentPolicy = await fetch(`${API_BASE}/policies/${policyId}`, {
    headers: { 'Authorization': 'Bearer admin-token' }
  }).then(r => r.json());
  
  // Update with new values
  await fetch(`${API_BASE}/policies/${policyId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json', 
      'Authorization': 'Bearer admin-token'
    },
    body: JSON.stringify({
      ...currentPolicy,
      is_active: updates.is_active
    })
  });
}
```

## Testing Framework Implementation

### 1. End-to-End Test Flow

```javascript
class ExternalIntegrationTester {
  constructor(config) {
    this.config = config;
    this.receivedWebhooks = new Map(); // Store received events
  }
  
  async runIntegrationTest() {
    // 1. Start webhook receiver
    await this.startWebhookReceiver();
    
    // 2. Make a change in Vitea
    const testAgentId = await this.createTestAgent();
    
    // 3. Wait for webhook
    const webhook = await this.waitForWebhook('agent.created', testAgentId);
    
    // 4. Pull snapshot and verify
    const snapshot = await this.pullSnapshot();
    const agentInSnapshot = snapshot.find(item => item.agent_id === testAgentId);
    
    // 5. Verify consistency
    assert(agentInSnapshot, 'Agent should be in snapshot');
    assert(webhook.subject.resource_id.agent_id === testAgentId, 'IDs should match');
    
    // 6. Test updates
    await this.updateAgentRolePolicies(testAgentId, {
      agent_active: true,
      policy_ids: ['test-policy-id']
    });
    
    // 7. Wait for assignment webhook
    await this.waitForWebhook('assignment.linked', testAgentId);
    
    // 8. Verify final state
    const finalSnapshot = await this.pullSnapshot();
    // ... verify expectations
  }
  
  async waitForWebhook(eventType, resourceId, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Webhook ${eventType} not received within ${timeout}ms`));
      }, timeout);
      
      const checkWebhook = () => {
        for (const [eventId, webhook] of this.receivedWebhooks) {
          if (webhook.event_type === eventType && 
              this.matchesResourceId(webhook, resourceId)) {
            clearTimeout(timeoutId);
            resolve(webhook);
            return;
          }
        }
        setTimeout(checkWebhook, 100);
      };
      
      checkWebhook();
    });
  }
}
```

### 2. Test Configuration

```javascript
// config/integration-test.json
{
  "vitea_api_base": "http://localhost:8001/api/v1",
  "webhook_receiver_port": 9000,
  "webhook_secret": "test-secret",
  "auth_token": "admin-token",
  "test_timeout": 30000,
  "cleanup_test_data": true
}
```

### 3. Running Tests

```bash
# Start your Vitea services
./start-all-services.sh

# In another terminal, run integration tests
node tests/external-integration-test.js
```

## Security Considerations

1. **HMAC Verification**: Always verify webhook signatures
2. **Token Security**: Use secure bearer tokens or OAuth2 client credentials
3. **Idempotency**: Handle duplicate webhook deliveries using event_id
4. **Rate Limiting**: Implement proper rate limiting on webhook endpoints
5. **HTTPS**: Use HTTPS for all webhook endpoints in production

## Monitoring and Observability

1. **Correlation IDs**: Use X-Correlation-Id for tracing requests
2. **Metrics**: Track webhook delivery success/failure rates
3. **Logging**: Log all webhook events and API calls with structured logging
4. **Alerting**: Set up alerts for failed webhook deliveries or API errors

## Troubleshooting

- **Webhook not received**: Check firewall, HTTPS cert, and webhook URL configuration
- **Signature mismatch**: Verify webhook secret and raw body computation
- **Snapshot pagination**: Ensure proper cursor handling for large datasets
- **Token expiry**: Implement token refresh for OAuth2 flows