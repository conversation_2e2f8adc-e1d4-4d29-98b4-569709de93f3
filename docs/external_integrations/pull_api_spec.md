### Assignments Snapshot API (Inbound pull by external system)

Base URL (preview)
- `https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1`

Endpoint
- `GET /integrations/assignments-snapshot`

Auth
- Preview: `Authorization: Bearer <INTEGRATIONS_TEST_TOKEN>`
- Production: OAuth2 Client Credentials (Bearer); scopes TBD (e.g., `integrations.read`)

Query parameters
- `page_size` (optional, default 500, max 2000)
- `cursor` (optional, opaque string from previous page)
- `snapshot_id` (optional; supply to continue a previously started snapshot)

Response (200)
{
  "snapshot_id": "20cf6ab8-6322-4ec3-9dab-2e006384ce83",
  "generated_at": "2025-08-12T03:32:36.262Z",
  "items": [
    {
      "agent_id": "AGENT_UUID",
      "role_id": "ROLE_UUID",
      "group_id": "GROUP_UUID",
      "policy_id": "POLICY_UUID",
      "policy": {
        "guardrail_id": "GR-12345",
        "name": "PHI Redaction",
        "description": "Redact PHI in outputs",
        "category": "medical_privacy",
        "policy_type": "opa",
        "definition": { /* rules only, no PHI */ },
        "version": 7,
        "is_active": true,
        "severity": "high"
      }
    }
  ],
  "next_cursor": "OPAQUE_OR_NULL",
  "correlation_id": "b2273d30-9c2a-4a39-8d6d-9d8f3e5e4f7f"
}

Semantics
- Returns a full, denormalized snapshot of role-scoped assignments with policy details.
- Use `cursor` to iterate pages until `next_cursor` is null.
- Use a stable `snapshot_id` to continue a snapshot across calls; expires after ~15 minutes.
- No PHI appears in the payload.

Errors
- 401 Unauthorized: missing/invalid token
- 400 Bad Request: invalid/expired `snapshot_id`
- 500 Internal Server Error: transient error; retry with backoff

Example
`curl -s -H "Authorization: Bearer <INTEGRATIONS_TEST_TOKEN>" "https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1/integrations/assignments-snapshot?page_size=500"`

