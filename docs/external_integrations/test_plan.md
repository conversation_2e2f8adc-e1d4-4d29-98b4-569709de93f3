### Test Plan — External Integrations v1

Objectives
- Ensure reliable webhook emission on CRUD with retries, idempotency, and DLQ
- Validate snapshot API correctness, pagination, performance, and security
- Confirm no PHI leakage; observability and audit coverage

Test Matrix

1) Unit Tests
- Outbox repository: insert, status transitions, backoff schedule calculation (with jitter bounds)
- HMAC utility: signature generation/verification with secret rotation (active/next)
- Cursor codec: key → cursor and back; ordering stability
- OAuth2 middleware: token validation, scope enforcement

2) Integration Tests
- CRUD flows (agents, roles, policies, assignments)
  - Enqueue outbox row with correct envelope
  - Dispatcher posts to mock webhook; 2xx → delivered, retry on 5xx/timeout
  - Idempotency: duplicate deliveries are ignored by receiver using event_id
- DLQ
  - Force 10 consecutive failures → row moved to DLQ; replay moves back to pending and eventually delivers
- Snapshot API
  - Returns full set split across pages with stable snapshot_id; last page has null next_cursor
  - Large dataset (e.g., 50k items) within latency SLO (p95 under target)
  - Schema validation for items.policy fields

3) Security Tests
- OAuth2 required for snapshot endpoint; reject missing/invalid tokens, insufficient scope
- HMAC signature present and valid on all webhook POSTs; rotation works
- Rate limiting and 429 behavior

4) Performance & Reliability
- Load test dispatcher under bursts of events; verify <5s end-to-end for p95
- Throttle/mock external endpoint to trigger retries; verify exponential backoff distribution and DLQ behavior
- Snapshot API pagination under concurrent consumers; verify isolation of snapshots and consistent results

5) E2E (Staging)
- Deploy sample webhook receiver that verifies HMAC and records event_ids
- Run scripted CRUD operations; verify received events, then pull snapshot and reconcile counts

Tooling
- Use Jest for units; supertest for HTTP; a lightweight mock OAuth2 server; k6 or Locust for load; OWASP ZAP for basic API security checks

Acceptance Criteria
- All unit/integration tests passing
- E2E staging scenario demonstrates correct end-to-end behavior
- No PHI detected in payloads (static checks on payload schemas + sample traces)

