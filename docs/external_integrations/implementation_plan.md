### Implementation Plan — External Integrations v1

Milestones
1) Schema & config baselines
2) Outbox table + model + repository
3) Event producers on CRUD (agents, roles, policies, links)
4) Webhook dispatcher worker (retries, jitter, DLQ, HMAC)
5) Inbound OAuth2 (client credentials) middleware
6) Snapshot API (`GET /api/v1/integrations/assignments-snapshot`) with cursor
7) Observability (audit logs, metrics, tracing)
8) Docs and example client

Details

1) Schema & Config
- Database
  - Create `integration_outbox` table
    - id (uuid pk), tenant_id (uuid, nullable), destination (text: webhook|bus), event_type (text), event_version (int), payload_json (jsonb), status (text: pending|delivered|failed|dead), attempts (int), next_attempt_at (timestamptz), last_error (text), created_at/updated_at (timestamptz)
  - Create `integration_dlq` table with identical schema + `dead_at`
- Configuration
  - `INTEGRATION_WEBHOOK_URLS` (comma-separated) and `INTEGRATION_WEBHOOK_SECRET_ACTIVE`, `INTEGRATION_WEBHOOK_SECRET_NEXT`
  - OAuth2 provider or static token service for client credentials (v1 can stub a minimal provider in backend if needed)

2) Outbox Components
- Data access layer: insert row within same transaction as CRUD
- Status transitions: pending → delivered|failed|dead

3) Event Producers
- Hook into server-side handlers for:
  - agents: create/update/delete
  - roles: create/update/delete
  - policies: create/update/delete
  - assignments: link/unlink (agent_role_policies writes)
- Serialize envelope per design_decisions.md and enqueue outbox rows

4) Webhook Dispatcher
- Background job/worker loop:
  - Select pending rows with `next_attempt_at <= now()`
  - POST to each configured webhook URL with HMAC signature
  - On 2xx: mark delivered
  - On non-2xx/network error: attempts++, compute backoff with jitter, set next_attempt_at, store last_error
  - If attempts >= 10: move to DLQ
- Backoff function: base^n with full jitter (e.g., 1s,3s,9s,27s,120s,300s,900s)
- DLQ replay endpoint (admin-protected)

5) OAuth2 Client Credentials
- For v1, implement a minimal token endpoint or integrate existing IdP
- Middleware validates Bearer tokens and required scope `assignments.read`

6) Snapshot API
- Controller/action: `GET /api/v1/integrations/assignments-snapshot`
- Generate or reuse a `snapshot_id` when no cursor provided
  - Capture as-of timestamp and store server-side snapshot state keyed by snapshot_id (TTL 15m)
- Data assembly per page:
  - Query `agent_role_policies` joined with `policies`
  - Select fields: `agent_id, role_id, group_id, policy_id` and policy details (`guardrail_id, name, description, category, policy_type, definition, version, is_active, severity`)
  - Order by deterministic key (e.g., (agent_id, role_id, group_id, policy_id))
  - Construct opaque cursor from the last key in page (base64-encoded)
- Response: items array + next_cursor

7) Observability
- Audit: record CRUD + outbox enqueue + webhook delivery outcomes (event_id, actor, resource)
- Metrics: counters and histograms for delivery attempts, latencies, failures, DLQ depth; API latency and page sizes
- Tracing: add correlation_id and propagate into webhook headers and response payloads

8) Docs & Example Client
- Publish docs in this directory
- Provide a sample webhook receiver verifying HMAC and printing event ids
- Provide a sample client fetching snapshot with cursor loop

Migration & Rollout
- Deploy schema
- Gate features behind `INTEGRATIONS_ENABLED` toggle
- Start dispatcher with safe concurrency and rate limits
- Run smoke tests in staging (webhook receiver + snapshot fetch)

