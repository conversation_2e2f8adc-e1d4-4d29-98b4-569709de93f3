### External Integration Guide (End‑to‑End)

This guide shows how your external system integrates with our platform to receive change notifications (webhooks) and to pull the authoritative agent–role–group–policy assignments with policy details.

References
- Webhooks: see `webhook_spec.md`
- Snapshot API: see `pull_api_spec.md`
- Partner onboarding: see `partner_onboarding.md`

Environments (Preview)
- Base UI/API host: `https://vitea-pilot-demo.eastus.cloudapp.azure.com`
- API base: `https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1`

Prerequisites
- We exchange:
  - Your webhook receiver URL(s) (HTTPS)
  - Our shared secret for HMAC signing
  - Preview Bearer token for snapshot API (temp) or OAuth2 CC details (prod)
- You support at-least-once delivery and deduplicate by `X-Event-Id`.
- You can make outbound requests to our API and validate TLS.

Flow Overview
1) A relevant change occurs (agent/role/policy CRUD or assignment link/unlink).
2) We POST a signed webhook event to your receiver (HMAC-SHA256 of raw body).
3) You verify signature, deduplicate by `X-Event-Id`, and respond 2xx quickly.
4) You then call our snapshot API to pull the full, consistent set of assignments+policies.
5) You apply/update your enforcement layer to reflect current state.

Webhook (Receiver) — Quick Start
Endpoint requirements
- HTTPS POST receiver (e.g., `POST https://your.api/webhooks/v1/policy-events`)
- Accept `application/json`
- Return 2xx within ~5 seconds (after verifying signature and enqueueing internally)

Headers you will receive (key ones)
- `X-Event-Id`: global unique ID (use for idempotency)
- `X-Event-Type`: e.g., `policy.updated`, `assignment.linked`
- `X-Event-Version`: integer, starts at `1`
- `X-Timestamp`: RFC3339 UTC string
- `X-Correlation-Id`: for tracing; include in support requests
- `X-Signature`: base64(HMAC_SHA256(shared_secret, raw_body))

Example webhook payload (assignment linked)
{
  "event_id": "f7b9f7f1-2a5e-4f7c-8ea2-2e0d3db6c1e1",
  "event_version": 1,
  "event_type": "assignment.linked",
  "occurred_at": "2025-08-12T03:40:40.309Z",
  "correlation_id": "4d2aaf33-24f3-4a6b-8c61-3b8d171f51b4",
  "subject": {
    "resource_type": "agent_role_policy",
    "resource_id": {
      "agent_id": "597df3ba-ae0d-4e38-ba40-aab0f84beb88",
      "role_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
      "group_id": "210374f9-7697-48ff-bf11-582126a5be7b",
      "policy_id": "d6ac7fbd-37d9-4b81-824e-7ee86dc5a412"
    }
  },
  "data": {
    "change_type": "created"
  }
}

Signature verification
- Compute base64(HMAC_SHA256(secret, raw_body_bytes)) and compare to `X-Signature`.

Node.js example
```javascript
import crypto from 'crypto';

function verifySignature(secret, raw, headerSig) {
  const expected = crypto.createHmac('sha256', secret).update(raw).digest('base64');
  return crypto.timingSafeEqual(Buffer.from(expected), Buffer.from(headerSig || ''));
}
```

Python example
```python
import hmac, hashlib, base64

def verify_signature(secret: bytes, raw: bytes, header_sig: str) -> bool:
    digest = hmac.new(secret, raw, hashlib.sha256).digest()
    expected = base64.b64encode(digest).decode('utf-8')
    return hmac.compare_digest(expected, header_sig or '')
```

Responding to the webhook
- Always return 2xx if the signature is valid and you’ve safely enqueued/accepted the event.
- If invalid signature, return 401/403.
- Do not perform long work in the request thread; process asynchronously.

Pulling the authoritative snapshot
Endpoint
- `GET https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1/integrations/assignments-snapshot`

Auth (Preview)
- Header: `Authorization: Bearer <INTEGRATIONS_TEST_TOKEN>`

Parameters
- `page_size` (default 500, max 2000)
- `cursor` (opaque; use from previous response)
- `snapshot_id` (use to continue a snapshot)

Example request
`curl -s -H "Authorization: Bearer <INTEGRATIONS_TEST_TOKEN>" "https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1/integrations/assignments-snapshot?page_size=500"`

Example response (truncated)
{
  "snapshot_id": "20cf6ab8-6322-4ec3-9dab-2e006384ce83",
  "generated_at": "2025-08-12T03:32:36.262Z",
  "items": [
    {
      "agent_id": "597df3ba-ae0d-4e38-ba40-aab0f84beb88",
      "role_id": "89c0d4ee-b4f0-4494-a8fc-3fe6e79de729",
      "group_id": "210374f9-7697-48ff-bf11-582126a5be7b",
      "policy_id": "f536ec8c-bf37-4c7c-ba4e-827bc5ce934f",
      "policy": {
        "guardrail_id": null,
        "name": "DOB Policy",
        "description": "Ensure user provides a valid DOB",
        "category": "access_control",
        "policy_type": "opa",
        "definition": { "type": "access_control", "enabled": true, "severity": "medium" },
        "version": 1,
        "is_active": true,
        "severity": "medium"
      }
    }
  ],
  "next_cursor": "OPAQUE_OR_NULL",
  "correlation_id": "4d2aaf33-24f3-4a6b-8c61-3b8d171f51b4"
}

Applying updates on your side
1) Receive webhook, verify signature, dedupe by `X-Event-Id`, enqueue.
2) Pull snapshot; loop until `next_cursor` is null.
3) Build/refresh a map: `(agent_id, role_id, group_id, policy_id) -> policy_object`.
4) Activate/disable or update rules in your gateway/enforcement layer to match the snapshot.
5) Record the `snapshot_id` and timestamp for observability and reconciliation.

Concrete business example (US healthcare)
- Scenario: Hospital “ED Triage Assistant” requires PHI redaction and toxic content filtering for RN role.
- Admin updates PHI Redaction policy from severity `medium` → `high` and links it to RN via a group.
- Webhook: You receive `policy.updated` followed by `assignment.linked`.
  - Verify signatures, dedupe events, return 2xx.
- Pull: Call the snapshot API. You receive rows for the ED Triage agent + RN role with the PHI Redaction policy in `policy`.
- Enforcement: Update your masking/redaction configuration to reflect severity `high` and the new JSON `definition`.
- Confirm: Log `correlation_id` along with applied version for audit.

Error handling & retries
- Webhooks: At-least-once delivery; you must dedupe by `X-Event-Id`.
- Snapshot: If 500, retry with exponential backoff. If 401, refresh token.
- If you cannot process immediately, persist events and resume later using the snapshot as source of truth.

Security
- HTTPS only, TLS 1.2+.
- HMAC-signed webhooks using a shared secret that we rotate periodically.
- No PHI in events or snapshot payloads.
- OAuth2 in production; preview uses a test token.

Observability
- Use `X-Correlation-Id` to tie together webhooks, your internal logs, and snapshot pulls.
- Our `/metrics` endpoint (Prometheus format) is available for scraping in production.

Go‑live checklist
- [ ] Webhook receiver deployed (HTTPS), verifies signatures, logs `X-Event-Id` and `X-Correlation-Id`.
- [ ] Snapshot client implemented with pagination and retry.
- [ ] Enforcement layer updated atomically from snapshot data.
- [ ] Tokens/secrets stored securely and rotated.
- [ ] Rate limits and alerts configured.

