### External Partner Onboarding (Preview)

What we provide
- Base API URL (preview): `https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1`
- Webhook shared secret(s): active (and next for rotation)
- Webhook event types and payload spec (see `webhook_spec.md`)
- Snapshot API details (see `pull_api_spec.md`)
- Temporary Bearer token for preview (`INTEGRATIONS_TEST_TOKEN`)
- Expected rate limits and retry guidance

What we need from partner
- Webhook receiver URL(s) (HTTPS) per environment
- Confirmation of HMAC-SHA256 signature verification implementation
- IP ranges if you require allowlisting on our side
- Authentication preference for production (OAuth2 client credentials) and required scopes
- Expected SLA for acknowledging webhooks (target: 2xx within 5s)

Operational expectations
- At-least-once delivery; deduplicate by `X-Event-Id`
- On webhook receipt, call snapshot API to reconcile state (full snapshot with cursor)
- No PHI is sent in webhooks or the snapshot API payload
- Use `X-Correlation-Id` for support requests to correlate logs and traces

Security
- HTTPS only, TLS 1.2+
- HMAC-signed webhooks (rotate secrets periodically)
- OAuth2 for production (test token only in preview)

Testing checklist
- Receive a `policy.created` test webhook and verify signature
- Pull first page of snapshot; follow `next_cursor` until null
- Perform a test assignment link and verify `assignment.linked` webhook

