### External Integrations (Preview)

This directory documents how our system notifies an external orchestrator about resource changes (webhooks) and how the external system pulls authoritative assignments (snapshot API).

Docs:
- `webhook_spec.md` — outbound event contract (headers, signing, payloads, retries)
- `pull_api_spec.md` — inbound snapshot API for assignments + policies
- `partner_onboarding.md` — what we provide to partners and what we expect in return

Environments:
- Preview host: `https://vitea-pilot-demo.eastus.cloudapp.azure.com`
- API base (preview): `https://vitea-pilot-demo.eastus.cloudapp.azure.com/api/v1`

### External Integrations Overview

This directory documents how our platform integrates with external systems for policy governance synchronization.

We use signed webhooks to notify an external orchestrator whenever critical resources change, and we expose a denormalized snapshot API that the external system can call to retrieve agent–role–group–policy assignments along with associated policy details.

Key decisions (v1):
- Signed webhooks for outbound notifications (foundation laid for a future message bus)
- Full snapshot fetch API with cursor-based pagination
- Near real-time propagation (<5s end-to-end)
- OAuth2 Client Credentials (Bearer) for inbound API auth
- At-least-once delivery with idempotency
- Exponential backoff with jitter and DLQ
- Versioned APIs/events (/v1, event.version)
- Single denormalized snapshot endpoint (assignments + policy details)
- No PHI in any event or snapshot payload
- Structured audit logs, metrics, and tracing

Contents:
- design_decisions.md — architecture and interface specifications
- implementation_plan.md — step-by-step implementation guide
- test_plan.md — unit, integration, e2e, performance, and security tests
- TODO.md — actionable checklist to drive delivery

See also:
- `design_decisions.md`
- `implementation_plan.md`
- `test_plan.md`
- `TODO.md`

