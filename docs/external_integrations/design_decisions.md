### External Integrations — Design Decisions (v1)

Scope
- Notify an external system when agents, roles, policies, or their links change
- Provide a pull API that returns a full, denormalized snapshot of agent–role–group–policy assignments with policy details

Non-goals (v1)
- No message bus delivery yet (foundation only via Outbox pattern)
- No delta/incremental API (full snapshot only)

1) Outbound Signaling — Webhooks
- Transport: HTTPS POST webhooks
- Authentication: HMAC-SHA256 signature (shared secret per endpoint)
- Headers
  - `X-Event-Id`: UUID v4
  - `X-Event-Type`: e.g., `policy.updated`, `assignment.linked`
  - `X-Event-Version`: integer, starts at 1
  - `X-Timestamp`: RFC3339 UTC
  - `X-Signature`: base64(HMAC_SHA256(secret, raw_body))
- Body (envelope)
  {
    "event_id": "UUID",
    "event_version": 1,
    "event_type": "assignment.linked",
    "occurred_at": "2025-08-11T18:32:45.123Z",
    "subject": {
      "resource_type": "agent_policy_assignment",
      "resource_id": "COMPOSITE_OR_INTERNAL_ID",
      "tenant_id": "TENANT_UUID"
    },
    "correlation_id": "TRACE_OR_REQUEST_ID",
    "data": {
      "change_type": "created",
      "changed_fields": ["role_id","group_id","policy_id"],
      "snapshot_hint": { "type": "full", "snapshot_id": "SNAPSHOT_UUID" }
    }
  }
- Delivery semantics
  - At-least-once
  - Idempotency via `event_id`
  - Retries: exponential backoff with jitter (1s, 3s, 9s, 27s, 2m, 5m, 15m); max attempts = 10
  - DLQ: events moved to DLQ after max attempts with error history; replay supported
- PHI: No PHI in events; only metadata and identifiers

2) Reliability Foundation — Outbox Pattern
- Outbox table written within same transaction as CRUD operations
- Dispatcher service scans and delivers webhooks; transitions statuses and schedules retries
- Future bus publisher can reuse outbox rows to publish to Kafka/SNS/EventBridge

3) Triggers
- Agents: created, updated, deleted
- Roles: created, updated, deleted
- Policies: created, updated, deleted
- Assignments (links): linked, unlinked (agent–role–group–policy)

4) Inbound Snapshot API
- Route: `GET /api/v1/integrations/assignments-snapshot`
- Auth: OAuth2 Client Credentials (Bearer); scope `assignments.read`
- Request params
  - `cursor` (optional, opaque)
  - `page_size` (optional; default 500; max 2000)
- Response
  {
    "snapshot_id": "UUID",
    "generated_at": "2025-08-11T18:33:02.456Z",
    "items": [
      {
        "agent_id": "UUID",
        "role_id": "UUID",
        "group_id": "UUID",
        "policy_id": "UUID",
        "policy": {
          "guardrail_id": "string",
          "name": "string",
          "description": "string",
          "category": "content_safety|medical_privacy|...",
          "policy_type": "opa",
          "definition": { /* rules only, no PHI */ },
          "version": 1,
          "is_active": true,
          "severity": "low|medium|high|critical"
        }
      }
    ],
    "next_cursor": "OPAQUE_OR_NULL"
  }
- Snapshot semantics
  - Full snapshot, paginated by cursor until `next_cursor` is null
  - Stable `snapshot_id` across pages; consistent-as-of timestamp
  - No PHI in payloads

5) Security
- TLS required
- OAuth2 Client Credentials for inbound API
- HMAC-signed webhooks with secret rotation support (active/next secrets)
- Rate limits and allowlists configurable per environment

6) Observability & Audit
- Structured audit logs: CRUD operations capture actor, resource, event_id, correlation_id
- Metrics: webhook latency/success/failure, retry counts, DLQ depth, snapshot generation latency, API p95/p99
- Tracing: correlation_id propagation and distributed tracing headers

7) Versioning & Evolution
- REST path versioning `/api/v1/...`
- `event_version` per event; additive changes preferred
- Deprecation policy documented in release notes

8) Defaults (accepted)
- page_size default 500, max 2000
- Outbox and DLQ retention 30 days
- Snapshot TTL 15 minutes

