# Policy Schema Refactor Documentation

## Overview

This directory contains documentation for migrating the **dual policy schema files** to a **simple, centralized database solution**. The approach prioritizes **simplicity, maintainability, and quick implementation** over complex technical architecture.

## Current Problem

The system has two conflicting policy schema files:
- `enhanced-api-project/configs/policy_schemas.json`
- `admin-ui-project/src/policy_schemas.json`

This creates inconsistencies and maintenance burden.

## Solution: Simple Database-Driven Approach

Instead of building a complex schema registry, we implement a **minimal viable product (MVP)** using:
- ✅ Simple PostgreSQL table (`policy_schemas`)
- ✅ Direct database queries (no caching layer)
- ✅ Existing validation patterns with AJV
- ✅ Minimal code changes to existing API and UI

## Documentation Structure

### 1. [MVP Requirements](./MVP_REQUIREMENTS.md)
**Start here** - Defines the minimal approach and success criteria:
- Core problem statement
- Simple database table solution
- 6-day implementation timeline
- What's NOT included in MVP

### 2. [Simple Database Design](./SIMPLE_DATABASE_DESIGN.md)
Technical specifications for the database-first approach:
- Single table schema design
- Sample data structure
- API integration patterns
- Simple migration strategy

### 3. [Implementation Guide](./IMPLEMENTATION_GUIDE.md)
Step-by-step instructions for implementation:
- Database setup scripts
- Schema service creation
- API endpoint updates
- Admin UI modifications
- Data migration procedures

### 4. [No-Cache Approach](./NO_CACHE_APPROACH.md)
Justification for keeping it simple:
- Why direct database access is sufficient
- Performance analysis (database vs cache)
- When to consider adding caching
- Benefits of simplicity over optimization

### 5. [Testing Strategy](./TESTING_STRATEGY.md)
Pragmatic testing approach:
- Essential test coverage
- Unit, integration, and API tests
- Manual testing checklist
- Test database setup

## Key Design Principles

### 1. **Simplicity Over Optimization**
- Direct database queries instead of complex caching
- Single table instead of normalized schema
- Reuse existing patterns instead of new frameworks

### 2. **Minimal Risk Implementation**
- Small, incremental changes
- Maintain all existing functionality
- Easy rollback procedures
- Manual verification at each step

### 3. **Business Value Focus**
- Solve the core problem (dual files)
- Enable future enhancements
- Reduce maintenance burden
- Improve development velocity

## Implementation Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| **Phase 1** | 1 day | Database setup, table creation |
| **Phase 2** | 2 days | API updates, schema service |
| **Phase 3** | 2 days | UI updates, API integration |
| **Phase 4** | 1 day | Testing, cleanup, documentation |
| **Total** | **6 days** | **Complete MVP implementation** |

## Success Metrics

- ✅ Single source of truth for policy schemas
- ✅ Both JSON files removed from codebase
- ✅ All existing functionality preserved
- ✅ Schema validation continues working
- ✅ Zero breaking changes to policies
- ✅ Admin UI creates/edits policies normally

## What This Approach Avoids

- ❌ Complex schema registry architecture
- ❌ Advanced versioning and migration tools
- ❌ Caching and performance optimization
- ❌ Multi-layer abstraction patterns
- ❌ Advanced conflict resolution
- ❌ Automated rollback mechanisms

## Future Enhancement Path

This simple foundation enables future improvements when needed:

| Current MVP | Future Enhancement |
|-------------|-------------------|
| Single database table | Multi-table normalized design |
| Direct database access | Caching layer (Redis/memory) |
| Manual conflict resolution | Automated schema merging |
| Simple replacement versioning | Semantic versioning system |
| Basic error handling | Advanced error recovery |

## Getting Started

1. **Read** [MVP Requirements](./MVP_REQUIREMENTS.md) for the big picture
2. **Review** [Simple Database Design](./SIMPLE_DATABASE_DESIGN.md) for technical details
3. **Follow** [Implementation Guide](./IMPLEMENTATION_GUIDE.md) step by step
4. **Test** using [Testing Strategy](./TESTING_STRATEGY.md) guidelines
5. **Reference** [No-Cache Approach](./NO_CACHE_APPROACH.md) for performance questions

## Questions and Decisions

### Why Database Instead of File-Based Registry?
- Central source of truth
- Atomic updates across services
- Better concurrency handling
- Easier backup and recovery

### Why No Caching for MVP?
- PostgreSQL performance is sufficient (5-10ms queries)
- Reduces complexity and potential bugs
- Easier to debug and maintain
- Can add caching later based on actual metrics

### Why Simple Table Design?
- Faster implementation and testing
- Easier to understand and modify
- Sufficient for current requirements
- Foundation for future enhancements

## Support and Maintenance

### For Implementation Questions:
- Follow the step-by-step [Implementation Guide](./IMPLEMENTATION_GUIDE.md)
- Check [Testing Strategy](./TESTING_STRATEGY.md) for verification procedures
- Review troubleshooting sections in each document

### For Performance Concerns:
- See [No-Cache Approach](./NO_CACHE_APPROACH.md) for performance analysis
- Monitor the suggested metrics before adding complexity
- Consider gradual optimization based on real usage data

### For Future Enhancements:
- Start with the simple MVP foundation
- Add complexity only when metrics justify it
- Maintain backward compatibility during upgrades
- Document any changes to this approach

This documentation provides everything needed to implement a reliable, maintainable solution to the dual schema file problem while keeping technical complexity to a minimum.