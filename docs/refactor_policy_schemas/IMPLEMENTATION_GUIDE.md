# Implementation Guide - Simple Policy Schema Migration

## Overview

This guide provides step-by-step instructions for implementing the simple database-driven policy schema solution. The approach emphasizes **minimal code changes** and **maximum reuse** of existing patterns.

## Prerequisites

- PostgreSQL database access
- Node.js development environment
- Access to both schema files:
  - `enhanced-api-project/configs/policy_schemas.json`
  - `admin-ui-project/src/policy_schemas.json`

## Implementation Steps

### Step 1: Database Setup

#### 1.1 Create Schema Table

Add to the existing database schema (append to `scripts/00-create-complete-postgres-schema-consolidated.sql`):

```sql
-- ==============================================================================
-- POLICY SCHEMA REGISTRY (Simple MVP Implementation)
-- ==============================================================================

-- Main table for storing policy schemas
CREATE TABLE IF NOT EXISTS policy_schemas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    schema_content JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index for fast schema lookup
CREATE INDEX IF NOT EXISTS idx_policy_schemas_active 
ON policy_schemas(schema_name) WHERE is_active = true;

-- Updated timestamp trigger (reuse existing function)
CREATE TRIGGER update_policy_schemas_updated_at
    BEFORE UPDATE ON policy_schemas
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial schemas
INSERT INTO policy_schemas (schema_name, schema_content, description) VALUES
('BasicHealthPolicy', '{
    "type": "object",
    "properties": {
        "policyId": {"type": "string"},
        "policyName": {"type": "string"},
        "effectiveDate": {"type": "string", "format": "date"}
    },
    "required": ["policyId", "policyName", "effectiveDate"]
}'::jsonb, 'Basic health policy schema')
ON CONFLICT (schema_name) DO NOTHING;
```

#### 1.2 Apply Database Changes

```bash
# Apply to local database
docker exec -it pilot-postgres psql -U dbadmin -d vitea_db -c "
-- Add the SQL from step 1.1 here
"

# Or add to migration file and restart containers
echo "-- Policy Schema Registry" >> scripts/00-create-complete-postgres-schema-consolidated.sql
# Add the SQL content above
docker-compose down && docker-compose up -d
```

### Step 2: Create Schema Service

#### 2.1 Create Schema Service File

Create `enhanced-api-project/src/services/schemaService.js`:

```javascript
import db from '../db.js';

class SchemaService {
    /**
     * Get all active schemas - replaces reading policy_schemas.json
     * Returns: Object with schema_name as keys and schema_content as values
     */
    async getAllSchemas() {
        try {
            const query = `
                SELECT schema_name, schema_content, description 
                FROM policy_schemas 
                WHERE is_active = true 
                ORDER BY schema_name
            `;
            const result = await db.query(query);
            
            // Convert to existing format for backward compatibility
            const schemas = {};
            result.rows.forEach(row => {
                schemas[row.schema_name] = row.schema_content;
            });
            
            console.log(`Loaded ${result.rows.length} schemas from database`);
            return schemas;
        } catch (error) {
            console.error('Error loading schemas from database:', error);
            throw new Error('Failed to load schemas from database');
        }
    }
    
    /**
     * Get specific schema for validation
     */
    async getSchema(schemaName) {
        try {
            const query = `
                SELECT schema_content 
                FROM policy_schemas 
                WHERE schema_name = $1 AND is_active = true
            `;
            const result = await db.query(query, [schemaName]);
            return result.rows[0]?.schema_content || null;
        } catch (error) {
            console.error(`Error getting schema ${schemaName}:`, error);
            return null;
        }
    }
    
    /**
     * Update or create schema (simple upsert)
     */
    async upsertSchema(schemaName, schemaContent, description = null) {
        try {
            const query = `
                INSERT INTO policy_schemas (schema_name, schema_content, description)
                VALUES ($1, $2, $3)
                ON CONFLICT (schema_name) 
                DO UPDATE SET 
                    schema_content = $2, 
                    description = COALESCE($3, policy_schemas.description),
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id, created_at, updated_at
            `;
            const result = await db.query(query, [schemaName, schemaContent, description]);
            console.log(`Schema ${schemaName} upserted successfully`);
            return result.rows[0];
        } catch (error) {
            console.error(`Error upserting schema ${schemaName}:`, error);
            throw new Error(`Failed to update schema ${schemaName}`);
        }
    }
    
    /**
     * List all schema names (for admin UI)
     */
    async getSchemaNames() {
        try {
            const query = `
                SELECT schema_name, description, updated_at 
                FROM policy_schemas 
                WHERE is_active = true 
                ORDER BY schema_name
            `;
            const result = await db.query(query);
            return result.rows;
        } catch (error) {
            console.error('Error getting schema names:', error);
            throw new Error('Failed to get schema names');
        }
    }
}

export default new SchemaService();
```

### Step 3: Update Schema Validator

#### 3.1 Update Existing Schema Validator

Modify `enhanced-api-project/src/utils/schemaValidator.js`:

```javascript
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import schemaService from '../services/schemaService.js';

class SchemaValidator {
    constructor() {
        this.ajv = new Ajv({ allErrors: true, strict: false });
        addFormats(this.ajv);
        this.compiledSchemas = new Map(); // Simple cache
    }
    
    /**
     * Load schema from database (replaces file loading)
     */
    async getCompiledSchema(schemaName) {
        // Check cache first
        if (this.compiledSchemas.has(schemaName)) {
            return this.compiledSchemas.get(schemaName);
        }
        
        // Load from database
        const schema = await schemaService.getSchema(schemaName);
        if (!schema) {
            throw new Error(`Schema not found: ${schemaName}`);
        }
        
        // Compile and cache
        try {
            const compiled = this.ajv.compile(schema);
            this.compiledSchemas.set(schemaName, compiled);
            return compiled;
        } catch (error) {
            console.error(`Error compiling schema ${schemaName}:`, error);
            throw new Error(`Invalid schema: ${schemaName}`);
        }
    }
    
    /**
     * Validate data against schema (existing interface maintained)
     */
    async validate(data, schemaName) {
        try {
            const validator = await this.getCompiledSchema(schemaName);
            const valid = validator(data);
            
            return {
                valid,
                errors: valid ? [] : this.formatErrors(validator.errors)
            };
        } catch (error) {
            return {
                valid: false,
                errors: [{ message: error.message }]
            };
        }
    }
    
    /**
     * Format AJV errors for user consumption
     */
    formatErrors(errors) {
        return errors.map(error => ({
            field: error.instancePath || error.schemaPath,
            message: error.message,
            value: error.data
        }));
    }
    
    /**
     * Clear cache when schemas are updated
     */
    clearCache(schemaName = null) {
        if (schemaName) {
            this.compiledSchemas.delete(schemaName);
        } else {
            this.compiledSchemas.clear();
        }
        console.log(`Schema cache cleared${schemaName ? ` for ${schemaName}` : ''}`);
    }
}

export default new SchemaValidator();
```

### Step 4: Update API Endpoints

#### 4.1 Add Schema Management Endpoint

Create `enhanced-api-project/src/api/schemas.js`:

```javascript
import express from 'express';
import schemaService from '../services/schemaService.js';
import schemaValidator from '../utils/schemaValidator.js';

const router = express.Router();

/**
 * GET /api/v1/schemas - Get all schemas
 */
router.get('/', async (req, res) => {
    try {
        const schemas = await schemaService.getAllSchemas();
        res.json(schemas);
    } catch (error) {
        console.error('Error fetching schemas:', error);
        res.status(500).json({ error: 'Failed to fetch schemas' });
    }
});

/**
 * GET /api/v1/schemas/:name - Get specific schema
 */
router.get('/:name', async (req, res) => {
    try {
        const schema = await schemaService.getSchema(req.params.name);
        if (!schema) {
            return res.status(404).json({ error: 'Schema not found' });
        }
        res.json({ schema_name: req.params.name, schema_content: schema });
    } catch (error) {
        console.error(`Error fetching schema ${req.params.name}:`, error);
        res.status(500).json({ error: 'Failed to fetch schema' });
    }
});

/**
 * PUT /api/v1/schemas/:name - Update schema
 */
router.put('/:name', async (req, res) => {
    try {
        const { schema_content, description } = req.body;
        
        if (!schema_content) {
            return res.status(400).json({ error: 'schema_content is required' });
        }
        
        // Validate schema format
        try {
            new Ajv().compile(schema_content);
        } catch (error) {
            return res.status(400).json({ error: 'Invalid JSON schema format' });
        }
        
        const result = await schemaService.upsertSchema(
            req.params.name, 
            schema_content, 
            description
        );
        
        // Clear cache for updated schema
        schemaValidator.clearCache(req.params.name);
        
        res.json({ 
            message: 'Schema updated successfully',
            schema_name: req.params.name,
            updated_at: result.updated_at
        });
    } catch (error) {
        console.error(`Error updating schema ${req.params.name}:`, error);
        res.status(500).json({ error: 'Failed to update schema' });
    }
});

export default router;
```

#### 4.2 Register Schema Routes

Update `enhanced-api-project/src/app.js`:

```javascript
// Add import
import schemasRouter from './api/schemas.js';

// Add route (before other routes)
app.use('/api/v1/schemas', schemasRouter);
```

### Step 5: Data Migration

#### 5.1 Create Migration Script

Create `enhanced-api-project/scripts/migrate-schemas.js`:

```javascript
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import db from '../src/db.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function migrateSchemas() {
    console.log('🚀 Starting policy schema migration...');
    
    try {
        // Read schema files
        const apiSchemaPath = path.join(__dirname, '../configs/policy_schemas.json');
        const uiSchemaPath = path.join(__dirname, '../../admin-ui-project/src/policy_schemas.json');
        
        let apiSchemas = {};
        let uiSchemas = {};
        
        if (fs.existsSync(apiSchemaPath)) {
            apiSchemas = JSON.parse(fs.readFileSync(apiSchemaPath, 'utf8'));
            console.log(`✅ Loaded ${Object.keys(apiSchemas).length} schemas from API`);
        }
        
        if (fs.existsSync(uiSchemaPath)) {
            uiSchemas = JSON.parse(fs.readFileSync(uiSchemaPath, 'utf8'));
            console.log(`✅ Loaded ${Object.keys(uiSchemas).length} schemas from UI`);
        }
        
        // Merge schemas (API takes precedence)
        const allSchemas = { ...uiSchemas, ...apiSchemas };
        console.log(`📊 Total unique schemas to migrate: ${Object.keys(allSchemas).length}`);
        
        // Migrate each schema
        let migrated = 0;
        for (const [name, content] of Object.entries(allSchemas)) {
            try {
                await db.query(`
                    INSERT INTO policy_schemas (schema_name, schema_content, description)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (schema_name) DO UPDATE SET
                        schema_content = $2,
                        description = $3,
                        updated_at = CURRENT_TIMESTAMP
                `, [name, content, `Migrated schema for ${name} policy type`]);
                
                migrated++;
                console.log(`✅ Migrated: ${name}`);
            } catch (error) {
                console.error(`❌ Failed to migrate ${name}:`, error.message);
            }
        }
        
        console.log(`🎉 Migration completed! ${migrated}/${Object.keys(allSchemas).length} schemas migrated`);
        
        // Verify migration
        const verifyResult = await db.query('SELECT COUNT(*) as count FROM policy_schemas WHERE is_active = true');
        console.log(`📋 Database now contains ${verifyResult.rows[0].count} active schemas`);
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        throw error;
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    migrateSchemas()
        .then(() => {
            console.log('✅ Migration completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        });
}

export default migrateSchemas;
```

#### 5.2 Run Migration

```bash
cd enhanced-api-project
node scripts/migrate-schemas.js
```

### Step 6: Update Admin UI

#### 6.1 Update Schema Fetching

Modify `admin-ui-project/src/utils/schemaUtils.js` (or create if doesn't exist):

```javascript
import axios from 'axios';

const API_BASE = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8001';

// Cache schemas in memory (simple approach)
let schemaCache = null;
let cacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch all schemas from API (replaces local file loading)
 */
export async function loadSchemas() {
    // Return cached schemas if still valid
    if (schemaCache && (Date.now() - cacheTime) < CACHE_DURATION) {
        return schemaCache;
    }
    
    try {
        console.log('Fetching schemas from API...');
        const response = await axios.get(`${API_BASE}/api/v1/schemas`);
        
        schemaCache = response.data;
        cacheTime = Date.now();
        
        console.log(`Loaded ${Object.keys(schemaCache).length} schemas from API`);
        return schemaCache;
    } catch (error) {
        console.error('Error loading schemas from API:', error);
        
        // Fallback to empty schemas to prevent app crash
        return {};
    }
}

/**
 * Get specific schema by name
 */
export async function getSchema(schemaName) {
    const schemas = await loadSchemas();
    return schemas[schemaName] || null;
}

/**
 * Clear schema cache (call after updates)
 */
export function clearSchemaCache() {
    schemaCache = null;
    cacheTime = 0;
    console.log('Schema cache cleared');
}
```

#### 6.2 Update Components Using Schemas

Update any components that import the local schema file:

```javascript
// Before: import schemas from '../policy_schemas.json';
// After: 
import { loadSchemas, getSchema } from '../utils/schemaUtils.js';

// In component:
const [schemas, setSchemas] = useState({});

useEffect(() => {
    loadSchemas().then(setSchemas);
}, []);
```

### Step 7: Testing

#### 7.1 Test Database Access

```bash
# Test schema retrieval
curl http://localhost:8001/api/v1/schemas

# Test specific schema
curl http://localhost:8001/api/v1/schemas/BasicHealthPolicy
```

#### 7.2 Test Policy Creation

```bash
# Test policy creation still works with database schemas
curl -X POST http://localhost:8001/api/v1/policies \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Policy",
    "schema_type": "BasicHealthPolicy",
    "data": {
      "policyId": "POL-123456",
      "policyName": "Test Policy",
      "effectiveDate": "2024-01-01"
    }
  }'
```

### Step 8: Cleanup

#### 8.1 Remove Old Schema Files

After verifying everything works:

```bash
# Backup original files
mv enhanced-api-project/configs/policy_schemas.json enhanced-api-project/configs/policy_schemas.json.backup
mv admin-ui-project/src/policy_schemas.json admin-ui-project/src/policy_schemas.json.backup

# Or delete if confident
rm enhanced-api-project/configs/policy_schemas.json
rm admin-ui-project/src/policy_schemas.json
```

#### 8.2 Update Documentation

Add note to relevant files:
```markdown
<!-- In relevant README files -->
## Schema Management
Policy schemas are now managed in the database via the `policy_schemas` table.
Access schemas via the API endpoint: GET /api/v1/schemas
```

## Troubleshooting

### Common Issues

1. **Database connection errors**: Check PostgreSQL is running
2. **Schema not found**: Verify migration completed successfully  
3. **Validation errors**: Check schema format in database
4. **Cache issues**: Clear schema cache in validator

### Verification Commands

```bash
# Check database schemas
docker exec -it pilot-postgres psql -U dbadmin -d vitea_db -c "SELECT schema_name, description FROM policy_schemas;"

# Test API endpoint
curl http://localhost:8001/api/v1/schemas

# Check application logs
docker logs pilot-api
```

This implementation provides a simple, working solution that eliminates the dual schema file problem while maintaining all existing functionality.