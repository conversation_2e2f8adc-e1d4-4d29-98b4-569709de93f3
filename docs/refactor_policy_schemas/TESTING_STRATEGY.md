# Testing Strategy for Simple Policy Schema Migration

## Overview

This document outlines a **pragmatic testing approach** for the simple database-driven policy schema solution, focusing on **essential tests** that ensure reliability without over-engineering.

## Testing Philosophy: **Essential Coverage, Maximum Efficiency**

- ✅ **Test core functionality** - schema CRUD, validation, API endpoints
- ✅ **Test critical paths** - policy creation, schema loading, validation
- ✅ **Test error conditions** - missing schemas, invalid data, database failures
- ✅ **Simple test setup** - minimal mocking, real database for integration tests
- ❌ **No complex test infrastructure** - avoid over-engineered test frameworks
- ❌ **No performance load testing** - MVP focuses on functionality first

## Test Structure

### Unit Tests (Fast, Isolated)
Test individual components without external dependencies

### Integration Tests (Real Database)
Test complete workflows with actual database

### API Tests (End-to-End)
Test full API endpoints with real requests

## Test Implementation

### 1. Unit Tests - Schema Service

```javascript
// enhanced-api-project/src/services/__tests__/schemaService.test.js
import { jest } from '@jest/globals';
import schemaService from '../schemaService.js';

// Mock the database
jest.mock('../db.js', () => ({
    query: jest.fn()
}));

import db from '../db.js';

describe('SchemaService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    
    describe('getSchema', () => {
        it('should return schema when found', async () => {
            const mockSchema = { type: 'object', properties: {} };
            db.query.mockResolvedValue({ 
                rows: [{ schema_content: mockSchema }] 
            });
            
            const result = await schemaService.getSchema('TestSchema');
            
            expect(result).toEqual(mockSchema);
            expect(db.query).toHaveBeenCalledWith(
                expect.stringContaining('SELECT schema_content'),
                ['TestSchema']
            );
        });
        
        it('should return null when schema not found', async () => {
            db.query.mockResolvedValue({ rows: [] });
            
            const result = await schemaService.getSchema('NonExistentSchema');
            
            expect(result).toBeNull();
        });
        
        it('should handle database errors gracefully', async () => {
            db.query.mockRejectedValue(new Error('Database connection failed'));
            
            const result = await schemaService.getSchema('TestSchema');
            
            expect(result).toBeNull();
        });
    });
    
    describe('getAllSchemas', () => {
        it('should return all active schemas as object', async () => {
            db.query.mockResolvedValue({ 
                rows: [
                    { schema_name: 'Schema1', schema_content: { type: 'object' } },
                    { schema_name: 'Schema2', schema_content: { type: 'string' } }
                ]
            });
            
            const result = await schemaService.getAllSchemas();
            
            expect(result).toEqual({
                Schema1: { type: 'object' },
                Schema2: { type: 'string' }
            });
        });
        
        it('should handle empty result', async () => {
            db.query.mockResolvedValue({ rows: [] });
            
            const result = await schemaService.getAllSchemas();
            
            expect(result).toEqual({});
        });
    });
    
    describe('upsertSchema', () => {
        it('should insert new schema successfully', async () => {
            const mockResult = { 
                rows: [{ id: 'uuid-123', created_at: new Date(), updated_at: new Date() }] 
            };
            db.query.mockResolvedValue(mockResult);
            
            const schema = { type: 'object', properties: { name: { type: 'string' } } };
            const result = await schemaService.upsertSchema('NewSchema', schema, 'Test schema');
            
            expect(result).toEqual(mockResult.rows[0]);
            expect(db.query).toHaveBeenCalledWith(
                expect.stringContaining('INSERT INTO policy_schemas'),
                ['NewSchema', schema, 'Test schema']
            );
        });
    });
});
```

### 2. Unit Tests - Schema Validator

```javascript
// enhanced-api-project/src/utils/__tests__/schemaValidator.test.js
import { jest } from '@jest/globals';
import schemaValidator from '../schemaValidator.js';

// Mock schema service
jest.mock('../services/schemaService.js', () => ({
    getSchema: jest.fn()
}));

import schemaService from '../services/schemaService.js';

describe('SchemaValidator', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        schemaValidator.clearCache(); // Clear cache between tests
    });
    
    describe('validate', () => {
        it('should validate correct data successfully', async () => {
            const schema = {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    age: { type: 'number', minimum: 0 }
                },
                required: ['name']
            };
            schemaService.getSchema.mockResolvedValue(schema);
            
            const validData = { name: 'John', age: 25 };
            const result = await schemaValidator.validate(validData, 'PersonSchema');
            
            expect(result.valid).toBe(true);
            expect(result.errors).toEqual([]);
        });
        
        it('should return validation errors for invalid data', async () => {
            const schema = {
                type: 'object',
                properties: { name: { type: 'string' } },
                required: ['name']
            };
            schemaService.getSchema.mockResolvedValue(schema);
            
            const invalidData = { age: 25 }; // missing required 'name'
            const result = await schemaValidator.validate(invalidData, 'PersonSchema');
            
            expect(result.valid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toMatchObject({
                message: expect.stringContaining('required')
            });
        });
        
        it('should handle schema not found', async () => {
            schemaService.getSchema.mockResolvedValue(null);
            
            const result = await schemaValidator.validate({}, 'NonExistentSchema');
            
            expect(result.valid).toBe(false);
            expect(result.errors[0].message).toContain('Schema not found');
        });
        
        it('should cache compiled schemas', async () => {
            const schema = { type: 'object', properties: { name: { type: 'string' } } };
            schemaService.getSchema.mockResolvedValue(schema);
            
            // First call
            await schemaValidator.validate({ name: 'test' }, 'TestSchema');
            // Second call - should use cached version
            await schemaValidator.validate({ name: 'test2' }, 'TestSchema');
            
            expect(schemaService.getSchema).toHaveBeenCalledTimes(1);
        });
    });
    
    describe('clearCache', () => {
        it('should clear specific schema from cache', async () => {
            const schema = { type: 'string' };
            schemaService.getSchema.mockResolvedValue(schema);
            
            // Load schema into cache
            await schemaValidator.validate('test', 'StringSchema');
            
            // Clear cache for this schema
            schemaValidator.clearCache('StringSchema');
            
            // Next call should reload from service
            await schemaValidator.validate('test2', 'StringSchema');
            
            expect(schemaService.getSchema).toHaveBeenCalledTimes(2);
        });
    });
});
```

### 3. Integration Tests - Real Database

```javascript
// enhanced-api-project/src/__tests__/integration/schemaIntegration.test.js
import { Pool } from 'pg';
import schemaService from '../services/schemaService.js';
import schemaValidator from '../utils/schemaValidator.js';

// Use test database
const testDb = new Pool({
    connectionString: process.env.TEST_DATABASE_URL || 'postgresql://dbadmin:vitea123@localhost:5432/vitea_test_db'
});

describe('Schema Integration Tests', () => {
    beforeAll(async () => {
        // Setup test database schema
        await testDb.query(`
            CREATE TABLE IF NOT EXISTS policy_schemas (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                schema_name VARCHAR(255) NOT NULL UNIQUE,
                schema_content JSONB NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        `);
    });
    
    beforeEach(async () => {
        // Clean up test data
        await testDb.query('DELETE FROM policy_schemas');
    });
    
    afterAll(async () => {
        await testDb.end();
    });
    
    describe('Schema CRUD Operations', () => {
        it('should store and retrieve schema correctly', async () => {
            const schema = {
                type: 'object',
                properties: {
                    policyId: { type: 'string' },
                    amount: { type: 'number', minimum: 0 }
                },
                required: ['policyId']
            };
            
            // Store schema
            await schemaService.upsertSchema('TestPolicy', schema, 'Test policy schema');
            
            // Retrieve schema
            const retrieved = await schemaService.getSchema('TestPolicy');
            
            expect(retrieved).toEqual(schema);
        });
        
        it('should update existing schema', async () => {
            const originalSchema = { type: 'string' };
            const updatedSchema = { type: 'object', properties: { name: { type: 'string' } } };
            
            // Insert original
            await schemaService.upsertSchema('UpdateTest', originalSchema);
            
            // Update
            await schemaService.upsertSchema('UpdateTest', updatedSchema);
            
            // Verify update
            const result = await schemaService.getSchema('UpdateTest');
            expect(result).toEqual(updatedSchema);
        });
    });
    
    describe('End-to-End Validation', () => {
        it('should validate data against database schema', async () => {
            // Setup schema in database
            const schema = {
                type: 'object',
                properties: {
                    name: { type: 'string', minLength: 1 },
                    email: { type: 'string', format: 'email' }
                },
                required: ['name', 'email']
            };
            await schemaService.upsertSchema('UserSchema', schema);
            
            // Test valid data
            const validData = { name: 'John Doe', email: '<EMAIL>' };
            const validResult = await schemaValidator.validate(validData, 'UserSchema');
            expect(validResult.valid).toBe(true);
            
            // Test invalid data
            const invalidData = { name: '', email: 'invalid-email' };
            const invalidResult = await schemaValidator.validate(invalidData, 'UserSchema');
            expect(invalidResult.valid).toBe(false);
            expect(invalidResult.errors.length).toBeGreaterThan(0);
        });
    });
});
```

### 4. API Tests - Schema Endpoints

```javascript
// enhanced-api-project/src/__tests__/api/schemas.test.js
import request from 'supertest';
import app from '../../app.js';
import db from '../../db.js';

describe('Schema API Endpoints', () => {
    beforeEach(async () => {
        // Clean test data
        await db.query('DELETE FROM policy_schemas WHERE schema_name LIKE \'Test%\'');
    });
    
    describe('GET /api/v1/schemas', () => {
        it('should return all active schemas', async () => {
            // Insert test schema
            const testSchema = { type: 'object', properties: { test: { type: 'string' } } };
            await db.query(
                'INSERT INTO policy_schemas (schema_name, schema_content) VALUES ($1, $2)',
                ['TestSchema', testSchema]
            );
            
            const response = await request(app)
                .get('/api/v1/schemas')
                .expect(200);
            
            expect(response.body).toHaveProperty('TestSchema');
            expect(response.body.TestSchema).toEqual(testSchema);
        });
        
        it('should return empty object when no schemas exist', async () => {
            const response = await request(app)
                .get('/api/v1/schemas')
                .expect(200);
            
            expect(response.body).toEqual({});
        });
    });
    
    describe('GET /api/v1/schemas/:name', () => {
        it('should return specific schema', async () => {
            const testSchema = { type: 'string', pattern: '^[A-Z]+$' };
            await db.query(
                'INSERT INTO policy_schemas (schema_name, schema_content) VALUES ($1, $2)',
                ['StringSchema', testSchema]
            );
            
            const response = await request(app)
                .get('/api/v1/schemas/StringSchema')
                .expect(200);
            
            expect(response.body).toEqual({
                schema_name: 'StringSchema',
                schema_content: testSchema
            });
        });
        
        it('should return 404 for non-existent schema', async () => {
            await request(app)
                .get('/api/v1/schemas/NonExistent')
                .expect(404);
        });
    });
    
    describe('PUT /api/v1/schemas/:name', () => {
        it('should create new schema', async () => {
            const newSchema = {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    value: { type: 'number' }
                },
                required: ['id']
            };
            
            const response = await request(app)
                .put('/api/v1/schemas/NewSchema')
                .send({
                    schema_content: newSchema,
                    description: 'A new test schema'
                })
                .expect(200);
            
            expect(response.body.message).toContain('updated successfully');
            
            // Verify schema was stored
            const stored = await db.query(
                'SELECT schema_content FROM policy_schemas WHERE schema_name = $1',
                ['NewSchema']
            );
            expect(stored.rows[0].schema_content).toEqual(newSchema);
        });
        
        it('should reject invalid JSON schema', async () => {
            const invalidSchema = {
                type: 'invalid-type' // Not a valid JSON Schema type
            };
            
            await request(app)
                .put('/api/v1/schemas/InvalidSchema')
                .send({ schema_content: invalidSchema })
                .expect(400);
        });
        
        it('should require schema_content', async () => {
            await request(app)
                .put('/api/v1/schemas/MissingContent')
                .send({ description: 'Missing content' })
                .expect(400);
        });
    });
});
```

### 5. Migration Tests

```javascript
// enhanced-api-project/scripts/__tests__/migrate-schemas.test.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import db from '../db.js';
import migrateSchemas from '../migrate-schemas.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

describe('Schema Migration', () => {
    const testSchemaPath = path.join(__dirname, 'test-schemas.json');
    
    beforeAll(() => {
        // Create test schema file
        const testSchemas = {
            TestSchema1: { type: 'string' },
            TestSchema2: { type: 'object', properties: { name: { type: 'string' } } }
        };
        fs.writeFileSync(testSchemaPath, JSON.stringify(testSchemas, null, 2));
    });
    
    afterAll(() => {
        // Cleanup test file
        if (fs.existsSync(testSchemaPath)) {
            fs.unlinkSync(testSchemaPath);
        }
    });
    
    beforeEach(async () => {
        // Clean migration test data
        await db.query('DELETE FROM policy_schemas WHERE description LIKE \'%Migrated%\'');
    });
    
    it('should migrate schemas from JSON file to database', async () => {
        // Mock file paths for testing
        const originalMethod = migrateSchemas.constructor.prototype.readFileSync;
        
        // Run migration (would need to modify migrate-schemas.js to accept file paths)
        // This test would verify the migration logic
        expect(true).toBe(true); // Placeholder - implement based on migration structure
    });
});
```

## Test Execution

### Running Tests

```bash
# Unit tests only (fast)
cd enhanced-api-project
npm test -- --testPathPattern=__tests__.*\.test\.js

# Integration tests (requires database)
npm test -- --testPathPattern=integration

# API tests (requires running server)
npm test -- --testPathPattern=api

# All tests
npm test
```

### Test Database Setup

```bash
# Create test database
docker exec -it pilot-postgres createdb -U dbadmin vitea_test_db

# Run schema on test database
docker exec -i pilot-postgres psql -U dbadmin -d vitea_test_db < scripts/00-create-complete-postgres-schema-consolidated.sql
```

### CI/CD Integration

```yaml
# .github/workflows/test.yml (if using GitHub Actions)
name: Test Policy Schema Migration

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: vitea123
          POSTGRES_USER: dbadmin
          POSTGRES_DB: vitea_test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd enhanced-api-project
          npm ci
          
      - name: Run tests
        run: |
          cd enhanced-api-project
          npm test
        env:
          TEST_DATABASE_URL: postgresql://dbadmin:vitea123@localhost:5432/vitea_test_db
```

## Test Coverage Goals

### Minimum Acceptable Coverage
- **Schema Service**: >90% line coverage
- **Schema Validator**: >90% line coverage  
- **API Endpoints**: >85% line coverage
- **Critical Paths**: 100% coverage (schema loading, validation, policy creation)

### Coverage Analysis

```bash
# Generate coverage report
cd enhanced-api-project
npm test -- --coverage

# View coverage report
open coverage/lcov-report/index.html
```

## Manual Testing Checklist

### Before Deployment
- [ ] All automated tests pass
- [ ] Manual smoke test: create policy with database schema
- [ ] Manual smoke test: edit policy with validation
- [ ] Manual smoke test: schema CRUD operations
- [ ] Database migration runs successfully
- [ ] Original schema files can be safely removed
- [ ] API endpoints respond correctly
- [ ] Admin UI loads schemas from API

### Error Scenarios
- [ ] Database connection failure handled gracefully
- [ ] Invalid schema format rejected appropriately
- [ ] Missing schema returns proper error messages
- [ ] Malformed JSON in database handled correctly

This testing strategy ensures the simple schema migration is reliable and bug-free while avoiding unnecessary complexity in the test setup.