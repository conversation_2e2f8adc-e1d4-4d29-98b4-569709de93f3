# Policy Schemas API Documentation

**Version:** 1.0.0  
**Base URL:** `https://your-domain.com/api/v1/schemas`  
**Last Updated:** 2025-08-28

## Overview

The Policy Schemas API provides comprehensive CRUD operations for managing policy validation schemas in the Vitea.ai Policy Management System. This API allows external systems to create, read, update, and delete policy schemas that are used for validating policy definitions throughout the system.

## Authentication

All API endpoints require Bearer token authentication.

```http
Authorization: Bearer <your-access-token>
```

## Base Response Format

All API responses follow a consistent structure:

### Success Response
```json
{
  "data": { /* response data */ },
  "message": "Success message",
  "timestamp": "2025-08-28T12:00:00Z"
}
```

### Error Response
```json
{
  "error": "Error description",
  "details": "Additional error details (optional)",
  "timestamp": "2025-08-28T12:00:00Z"
}
```

## Data Model

### Policy Schema Object

```json
{
  "id": "************************************",
  "schema_name": "healthcare_privacy",
  "schema_content": {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
      "patient_data": {
        "type": "object",
        "properties": {
          "encryption_required": {
            "type": "boolean"
          },
          "access_controls": {
            "type": "array",
            "items": {
              "type": "string"
            }
          }
        },
        "required": ["encryption_required", "access_controls"]
      }
    },
    "required": ["patient_data"]
  },
  "description": "Schema for healthcare privacy policies",
  "guardrail_id": "************************************",
  "is_active": true,
  "created_at": "2025-08-28T10:00:00Z",
  "updated_at": "2025-08-28T11:30:00Z"
}
```

### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | UUID | Auto-generated | Unique identifier for the schema |
| `schema_name` | String(255) | Yes | Unique name for the schema (used as identifier) |
| `schema_content` | Object | Yes | Valid JSON Schema definition |
| `description` | String | No | Human-readable description of the schema |
| `guardrail_id` | UUID | No | Associated guardrail identifier |
| `is_active` | Boolean | Auto | Whether the schema is active (soft delete flag) |
| `created_at` | DateTime | Auto | Schema creation timestamp |
| `updated_at` | DateTime | Auto | Last modification timestamp |

## API Endpoints

### 1. Get All Schemas

Retrieve all active schemas in a format ready for policy validation.

**Endpoint:** `GET /api/v1/schemas`

**Response:**
```json
{
  "healthcare_privacy": {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": { /* schema definition */ }
  },
  "data_security": {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": { /* schema definition */ }
  }
}
```

**Status Codes:**
- `200 OK` - Schemas retrieved successfully
- `500 Internal Server Error` - Database error

**Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/schemas" \
  -H "Authorization: Bearer your-token"
```

### 2. Get Schema List

Retrieve metadata for all active schemas (admin/listing purposes).

**Endpoint:** `GET /api/v1/schemas/list`

**Response:**
```json
{
  "schemas": [
    {
      "schema_name": "healthcare_privacy",
      "description": "Schema for healthcare privacy policies",
      "guardrail_id": "************************************",
      "updated_at": "2025-08-28T11:30:00Z"
    },
    {
      "schema_name": "data_security",
      "description": "Schema for data security policies",
      "guardrail_id": null,
      "updated_at": "2025-08-28T10:15:00Z"
    }
  ]
}
```

**Status Codes:**
- `200 OK` - Schema list retrieved successfully
- `500 Internal Server Error` - Database error

### 3. Get Specific Schema

Retrieve a specific schema by name with full metadata.

**Endpoint:** `GET /api/v1/schemas/{schema_name}`

**Path Parameters:**
- `schema_name` (string, required) - Name of the schema to retrieve

**Response:**
```json
{
  "schema_name": "healthcare_privacy",
  "schema_content": {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": { /* full schema definition */ }
  },
  "description": "Schema for healthcare privacy policies",
  "guardrail_id": "************************************",
  "updated_at": "2025-08-28T11:30:00Z"
}
```

**Status Codes:**
- `200 OK` - Schema found and returned
- `404 Not Found` - Schema not found
- `500 Internal Server Error` - Database error

**Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/schemas/healthcare_privacy" \
  -H "Authorization: Bearer your-token"
```

### 4. Create or Update Schema

Create a new schema or update an existing one (upsert operation).

**Endpoint:** `PUT /api/v1/schemas/{schema_name}`

**Path Parameters:**
- `schema_name` (string, required) - Name of the schema to create/update

**Request Body:**
```json
{
  "schema_content": {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
      "patient_data": {
        "type": "object",
        "properties": {
          "encryption_required": {
            "type": "boolean"
          }
        },
        "required": ["encryption_required"]
      }
    },
    "required": ["patient_data"]
  },
  "description": "Updated schema for healthcare privacy policies",
  "guardrail_id": "************************************"
}
```

**Request Fields:**
- `schema_content` (object, required) - Valid JSON Schema definition
- `description` (string, optional) - Schema description
- `guardrail_id` (UUID, optional) - Associated guardrail ID

**Response:**
```json
{
  "message": "Schema updated successfully",
  "schema_name": "healthcare_privacy",
  "id": "************************************",
  "updated_at": "2025-08-28T12:00:00Z"
}
```

**Status Codes:**
- `200 OK` - Schema updated successfully
- `201 Created` - New schema created successfully
- `400 Bad Request` - Invalid request body or malformed JSON schema
- `500 Internal Server Error` - Database error

**Example:**
```bash
curl -X PUT "https://your-domain.com/api/v1/schemas/healthcare_privacy" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "schema_content": {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "properties": {
        "encryption": {
          "type": "boolean"
        }
      }
    },
    "description": "Healthcare privacy schema"
  }'
```

### 5. Deactivate Schema

Soft delete a schema by setting it as inactive. The schema will no longer appear in active listings but is preserved for audit purposes.

**Endpoint:** `DELETE /api/v1/schemas/{schema_name}`

**Path Parameters:**
- `schema_name` (string, required) - Name of the schema to deactivate

**Response:**
```json
{
  "message": "Schema deactivated successfully",
  "schema_name": "healthcare_privacy"
}
```

**Status Codes:**
- `200 OK` - Schema deactivated successfully
- `404 Not Found` - Schema not found
- `500 Internal Server Error` - Database error

**Example:**
```bash
curl -X DELETE "https://your-domain.com/api/v1/schemas/healthcare_privacy" \
  -H "Authorization: Bearer your-token"
```

### 6. Get Schemas by Guardrail

Retrieve all schemas associated with a specific guardrail ID.

**Endpoint:** `GET /api/v1/schemas/guardrail/{guardrail_id}`

**Path Parameters:**
- `guardrail_id` (UUID, required) - Guardrail identifier

**Response:**
```json
{
  "guardrail_id": "************************************",
  "schemas": [
    {
      "schema_name": "healthcare_privacy",
      "schema_content": { /* schema definition */ },
      "description": "Schema for healthcare privacy policies"
    },
    {
      "schema_name": "healthcare_security", 
      "schema_content": { /* schema definition */ },
      "description": "Schema for healthcare security policies"
    }
  ]
}
```

**Status Codes:**
- `200 OK` - Schemas retrieved successfully (may be empty array)
- `500 Internal Server Error` - Database error

**Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/schemas/guardrail/************************************" \
  -H "Authorization: Bearer your-token"
```

### 7. Validate Data Against Schema

Test endpoint to validate data against a specific schema.

**Endpoint:** `POST /api/v1/schemas/validate`

**Request Body:**
```json
{
  "schema_name": "healthcare_privacy",
  "data": {
    "patient_data": {
      "encryption_required": true,
      "access_controls": ["admin", "nurse"]
    }
  }
}
```

**Request Fields:**
- `schema_name` (string, required) - Name of schema to validate against
- `data` (object, required) - Data to validate

**Response:**
```json
{
  "schema_name": "healthcare_privacy",
  "valid": true,
  "errors": [],
  "warnings": []
}
```

**Response for Invalid Data:**
```json
{
  "schema_name": "healthcare_privacy",
  "valid": false,
  "errors": [
    {
      "instancePath": "/patient_data",
      "schemaPath": "#/properties/patient_data/required",
      "keyword": "required",
      "params": {"missingProperty": "encryption_required"},
      "message": "must have required property 'encryption_required'"
    }
  ],
  "warnings": []
}
```

**Status Codes:**
- `200 OK` - Validation completed (check `valid` field for result)
- `400 Bad Request` - Missing required fields
- `500 Internal Server Error` - Database or validation error

**Example:**
```bash
curl -X POST "https://your-domain.com/api/v1/schemas/validate" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "schema_name": "healthcare_privacy",
    "data": {
      "patient_data": {
        "encryption_required": true
      }
    }
  }'
```

## Error Codes

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| 400 | `INVALID_SCHEMA_FORMAT` | The provided schema_content is not valid JSON Schema |
| 400 | `MISSING_REQUIRED_FIELD` | Required field is missing from request body |
| 401 | `UNAUTHORIZED` | Invalid or missing authentication token |
| 404 | `SCHEMA_NOT_FOUND` | The specified schema does not exist |
| 409 | `SCHEMA_NAME_CONFLICT` | Schema name already exists (for strict create operations) |
| 500 | `DATABASE_ERROR` | Internal database error |
| 500 | `VALIDATION_ERROR` | Error during schema validation process |

## Rate Limiting

API requests are limited to:
- **100 requests per minute** for read operations (GET)
- **20 requests per minute** for write operations (PUT, POST, DELETE)

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Best Practices

### Schema Naming
- Use descriptive, lowercase names with underscores
- Examples: `healthcare_privacy`, `data_security`, `financial_compliance`
- Avoid special characters except underscore
- Maximum length: 255 characters

### Schema Content
- Always include `$schema` field for JSON Schema version
- Use semantic versioning for major schema changes
- Include descriptive `title` and `description` fields
- Validate schemas before submitting using external JSON Schema validators

### Error Handling
- Always check HTTP status codes before processing responses
- Handle rate limiting with exponential backoff
- Implement retry logic for 5xx errors
- Cache schemas locally when possible to reduce API calls

### Performance
- Use the `/api/v1/schemas` endpoint to fetch all schemas at once for bulk operations
- Cache schema content locally and use `updated_at` timestamps to check for changes
- Use the `/api/v1/schemas/list` endpoint for admin interfaces to avoid loading large schema content

## Example Integration

### Python Example
```python
import requests
import json

class PolicySchemaClient:
    def __init__(self, base_url, auth_token):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def get_all_schemas(self):
        """Get all active schemas"""
        response = requests.get(
            f'{self.base_url}/api/v1/schemas',
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def create_schema(self, name, schema_content, description=None, guardrail_id=None):
        """Create or update a schema"""
        data = {'schema_content': schema_content}
        if description:
            data['description'] = description
        if guardrail_id:
            data['guardrail_id'] = guardrail_id
            
        response = requests.put(
            f'{self.base_url}/api/v1/schemas/{name}',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
    
    def validate_data(self, schema_name, data):
        """Validate data against a schema"""
        response = requests.post(
            f'{self.base_url}/api/v1/schemas/validate',
            headers=self.headers,
            json={'schema_name': schema_name, 'data': data}
        )
        response.raise_for_status()
        return response.json()

# Usage
client = PolicySchemaClient('https://your-domain.com', 'your-token')

# Get all schemas
schemas = client.get_all_schemas()

# Create a new schema
schema_content = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "type": "object",
    "properties": {
        "encryption": {"type": "boolean"}
    }
}
result = client.create_schema('test_schema', schema_content, 'Test schema')

# Validate data
validation_result = client.validate_data('test_schema', {'encryption': True})
```

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

class PolicySchemaClient {
    constructor(baseUrl, authToken) {
        this.client = axios.create({
            baseURL: baseUrl.replace(/\/$/, ''),
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    }

    async getAllSchemas() {
        const response = await this.client.get('/api/v1/schemas');
        return response.data;
    }

    async createSchema(name, schemaContent, description = null, guardrailId = null) {
        const data = { schema_content: schemaContent };
        if (description) data.description = description;
        if (guardrailId) data.guardrail_id = guardrailId;

        const response = await this.client.put(`/api/v1/schemas/${name}`, data);
        return response.data;
    }

    async validateData(schemaName, data) {
        const response = await this.client.post('/api/v1/schemas/validate', {
            schema_name: schemaName,
            data: data
        });
        return response.data;
    }
}

// Usage
const client = new PolicySchemaClient('https://your-domain.com', 'your-token');

// Example usage with async/await
(async () => {
    try {
        const schemas = await client.getAllSchemas();
        console.log('All schemas:', schemas);
    } catch (error) {
        console.error('API Error:', error.response?.data || error.message);
    }
})();
```

## Support

For API support and questions:
- **Documentation:** https://docs.vitea.ai/policy-schemas-api
- **Support Email:** <EMAIL>
- **Status Page:** https://status.vitea.ai

## Changelog

### Version 1.0.0 (2025-08-28)
- Initial release of Policy Schemas API
- Full CRUD operations for schema management
- Schema validation endpoint
- Guardrail association support
- Comprehensive error handling and documentation