# Policy Schema Refactor - MVP Requirements

## Overview

This document outlines the **minimum viable product (MVP)** requirements for consolidating the dual policy schema files into a simple, centralized database-driven solution. The focus is on **simplicity and functionality** rather than complex technical architecture.

## Current Problem

The system currently has **two policy schema files** causing inconsistencies:
- `enhanced-api-project/configs/policy_schemas.json`
- `admin-ui-project/src/policy_schemas.json`

## MVP Solution: Simple Database Tables

### Core Principle: **KEEP IT SIMPLE**

- ✅ **Simple database tables** - no complex registry architecture
- ✅ **Basic CRUD operations** - no advanced versioning or caching
- ✅ **Direct database queries** - no additional API layers
- ✅ **Minimal code changes** - reuse existing validation patterns
- ❌ **No caching mechanisms** - direct database access is sufficient
- ❌ **No complex versioning** - single active schema per policy type
- ❌ **No advanced migration tooling** - simple data copy operations

## MVP Database Schema

### Table: `policy_schemas`

```sql
CREATE TABLE policy_schemas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    schema_content JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Simple index for fast retrieval
CREATE INDEX idx_policy_schemas_active ON policy_schemas(schema_name) WHERE is_active = true;
```

## MVP Implementation Plan

### Phase 1: Database Setup (1 day)
1. Create `policy_schemas` table
2. Insert existing schemas from both JSON files
3. Resolve any conflicts by manual review

### Phase 2: API Updates (2 days)
1. Update API to read schemas from database
2. Replace file-based schema loading with database queries
3. Maintain existing validation logic using AJV

### Phase 3: UI Updates (2 days)  
1. Update admin UI to fetch schemas from API
2. Remove local policy_schemas.json file
3. Test schema-driven form validation

### Phase 4: Testing & Cleanup (1 day)
1. Test all policy operations
2. Remove old JSON files
3. Update documentation

**Total MVP Timeline: 6 days**

## MVP Success Criteria

- ✅ Single source of truth for policy schemas in database
- ✅ Both JSON files removed from codebase
- ✅ All existing functionality works unchanged
- ✅ Schema validation continues to work with AJV
- ✅ Admin UI can create/edit policies with proper validation
- ✅ Zero breaking changes to existing policies

## What's NOT in MVP

- **Schema versioning** - Use simple replacement strategy
- **Complex migration tools** - Manual data migration acceptable
- **Caching layer** - Database performance is sufficient for MVP
- **Schema registry API** - Direct database access through existing API
- **Advanced conflict resolution** - Manual review and merge
- **Performance optimization** - Basic indexes are sufficient
- **Automated rollback** - Manual rollback procedures acceptable

## MVP vs. Future Enhancements

| Feature | MVP | Future Enhancement |
|---------|-----|------------------|
| Schema Storage | Database table | Advanced registry |
| Versioning | Replace existing | Semantic versioning |
| Performance | Basic indexes | Caching + optimization |
| Migration | Manual copy | Automated tools |
| Validation | Existing AJV | Enhanced validation |
| Rollback | Manual process | Automated rollback |

## Key Design Decisions

1. **Database-first approach** - Store schemas directly in PostgreSQL
2. **Reuse existing patterns** - Keep current AJV validation workflow  
3. **Minimal API changes** - Add database queries, remove file reads
4. **No new dependencies** - Use existing PostgreSQL connection
5. **Simple conflict resolution** - Manual review during migration
6. **Direct access pattern** - No additional abstraction layers

This MVP approach ensures we solve the core problem (dual schema files) with minimal risk and complexity, while providing a foundation for future enhancements.