# Simple Database Design for Policy Schema Consolidation

## Design Philosophy: **Simplicity First**

This design prioritizes **simplicity, maintainability, and quick implementation** over advanced features. The goal is to solve the dual schema file problem with minimal technical complexity.

## Database Schema

### Primary Table: `policy_schemas`

```sql
CREATE TABLE policy_schemas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    schema_content JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Simple index for performance
CREATE INDEX idx_policy_schemas_active ON policy_schemas(schema_name) WHERE is_active = true;

-- Automatic updated_at trigger (reuse existing function)
CREATE TRIGGER update_policy_schemas_updated_at
    BEFORE UPDATE ON policy_schemas
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

### Sample Data Structure

```sql
INSERT INTO policy_schemas (schema_name, schema_content, description) VALUES
(
    'BasicHealthPolicy',
    '{
        "type": "object",
        "properties": {
            "policyId": {"type": "string", "pattern": "^POL-[0-9]{6}$"},
            "policyName": {"type": "string", "minLength": 1},
            "coverageType": {"type": "string", "enum": ["basic", "premium", "family"]},
            "effectiveDate": {"type": "string", "format": "date"},
            "premium": {"type": "number", "minimum": 0}
        },
        "required": ["policyId", "policyName", "coverageType", "effectiveDate", "premium"]
    }'::jsonb,
    'Schema for basic health insurance policies'
),
(
    'LifeInsurancePolicy',
    '{
        "type": "object",
        "properties": {
            "policyId": {"type": "string", "pattern": "^LIFE-[0-9]{6}$"},
            "beneficiaryName": {"type": "string", "minLength": 1},
            "coverageAmount": {"type": "number", "minimum": 1000},
            "term": {"type": "integer", "minimum": 1, "maximum": 30}
        },
        "required": ["policyId", "beneficiaryName", "coverageAmount", "term"]
    }'::jsonb,
    'Schema for life insurance policies'
);
```

## Access Patterns

### 1. Retrieve All Active Schemas

```sql
-- Simple query for API endpoint
SELECT schema_name, schema_content, description 
FROM policy_schemas 
WHERE is_active = true 
ORDER BY schema_name;
```

### 2. Get Specific Schema

```sql
-- Get schema for validation
SELECT schema_content 
FROM policy_schemas 
WHERE schema_name = $1 AND is_active = true;
```

### 3. Update Schema

```sql
-- Simple update (replaces existing)
UPDATE policy_schemas 
SET schema_content = $2, updated_at = CURRENT_TIMESTAMP 
WHERE schema_name = $1 AND is_active = true;
```

### 4. Add New Schema

```sql
-- Insert new schema
INSERT INTO policy_schemas (schema_name, schema_content, description)
VALUES ($1, $2, $3);
```

## API Integration (Enhanced API Project)

### Simple Database Service

```javascript
// src/services/schemaService.js
import db from '../db.js';

class SchemaService {
    // Get all active schemas - replaces file reading
    async getAllSchemas() {
        const query = `
            SELECT schema_name, schema_content, description 
            FROM policy_schemas 
            WHERE is_active = true 
            ORDER BY schema_name
        `;
        const result = await db.query(query);
        
        // Convert to existing format for backward compatibility
        const schemas = {};
        result.rows.forEach(row => {
            schemas[row.schema_name] = row.schema_content;
        });
        return schemas;
    }
    
    // Get specific schema for validation
    async getSchema(schemaName) {
        const query = `
            SELECT schema_content 
            FROM policy_schemas 
            WHERE schema_name = $1 AND is_active = true
        `;
        const result = await db.query(query, [schemaName]);
        return result.rows[0]?.schema_content || null;
    }
    
    // Update schema (simple replacement)
    async updateSchema(schemaName, schemaContent, description) {
        const query = `
            INSERT INTO policy_schemas (schema_name, schema_content, description)
            VALUES ($1, $2, $3)
            ON CONFLICT (schema_name) 
            DO UPDATE SET 
                schema_content = $2, 
                description = $3,
                updated_at = CURRENT_TIMESTAMP
        `;
        await db.query(query, [schemaName, schemaContent, description]);
    }
}

export default new SchemaService();
```

## Validation Integration

### Reuse Existing AJV Pattern

```javascript
// src/utils/schemaValidator.js - Updated to use database
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import schemaService from '../services/schemaService.js';

class SchemaValidator {
    constructor() {
        this.ajv = new Ajv({ allErrors: true });
        addFormats(this.ajv);
        this.schemas = new Map(); // Simple in-memory cache
    }
    
    // Load schema from database (replaces file loading)
    async loadSchema(schemaName) {
        if (this.schemas.has(schemaName)) {
            return this.schemas.get(schemaName);
        }
        
        const schema = await schemaService.getSchema(schemaName);
        if (schema) {
            const validator = this.ajv.compile(schema);
            this.schemas.set(schemaName, validator);
            return validator;
        }
        return null;
    }
    
    // Validate data against schema (existing interface)
    async validate(data, schemaName) {
        const validator = await this.loadSchema(schemaName);
        if (!validator) {
            throw new Error(`Schema not found: ${schemaName}`);
        }
        
        const valid = validator(data);
        return {
            valid,
            errors: valid ? [] : validator.errors
        };
    }
    
    // Clear cache when schemas are updated
    clearCache(schemaName = null) {
        if (schemaName) {
            this.schemas.delete(schemaName);
        } else {
            this.schemas.clear();
        }
    }
}

export default new SchemaValidator();
```

## Migration Strategy

### Simple Data Migration Script

```javascript
// scripts/migrate-schemas.js
import fs from 'fs';
import db from '../enhanced-api-project/src/db.js';

async function migrateSchemas() {
    console.log('Starting schema migration...');
    
    // Read existing schema files
    const apiSchemas = JSON.parse(fs.readFileSync('enhanced-api-project/configs/policy_schemas.json'));
    const uiSchemas = JSON.parse(fs.readFileSync('admin-ui-project/src/policy_schemas.json'));
    
    // Combine schemas (API takes precedence for conflicts)
    const combinedSchemas = { ...uiSchemas, ...apiSchemas };
    
    // Insert into database
    for (const [name, content] of Object.entries(combinedSchemas)) {
        console.log(`Migrating schema: ${name}`);
        
        await db.query(`
            INSERT INTO policy_schemas (schema_name, schema_content, description)
            VALUES ($1, $2, $3)
        `, [name, content, `Migrated schema for ${name}`]);
    }
    
    console.log('Schema migration completed!');
    
    // Create backup of original files
    fs.copyFileSync('enhanced-api-project/configs/policy_schemas.json', 
                   'enhanced-api-project/configs/policy_schemas.json.backup');
    fs.copyFileSync('admin-ui-project/src/policy_schemas.json',
                   'admin-ui-project/src/policy_schemas.json.backup');
    
    console.log('Original files backed up with .backup extension');
}

migrateSchemas().catch(console.error);
```

## Performance Characteristics

### Expected Performance (MVP)
- **Schema retrieval**: ~5-10ms (simple indexed query)
- **Validation**: Same as current (AJV performance unchanged)  
- **Schema updates**: ~10-20ms (simple UPDATE query)
- **Memory usage**: Minimal cache (only recently used schemas)

### When to Consider Optimization
- If schema retrieval > 50ms consistently
- If more than 100 different schema types
- If schema updates happen > 1000 times/day
- If memory usage for cached schemas > 100MB

## Testing Strategy

### Unit Tests
```javascript
// Test schema service
describe('SchemaService', () => {
    it('should retrieve schema from database', async () => {
        const schema = await schemaService.getSchema('BasicHealthPolicy');
        expect(schema).toBeDefined();
        expect(schema.type).toBe('object');
    });
    
    it('should validate data against database schema', async () => {
        const result = await validator.validate(validPolicyData, 'BasicHealthPolicy');
        expect(result.valid).toBe(true);
    });
});
```

### Integration Tests
```javascript
// Test API endpoints work with database schemas
describe('Policy API with Database Schemas', () => {
    it('should create policy with database schema validation', async () => {
        const response = await request(app)
            .post('/api/v1/policies')
            .send(newPolicyData);
        expect(response.status).toBe(201);
    });
});
```

## Deployment Checklist

- [ ] Create `policy_schemas` table in database
- [ ] Run migration script to populate table  
- [ ] Update API code to use database schemas
- [ ] Update UI code to fetch schemas from API
- [ ] Test all policy creation/editing functionality
- [ ] Remove original JSON files
- [ ] Update documentation

This simple design solves the core problem with minimal complexity while providing a solid foundation for future enhancements.