#!/bin/bash

# End-to-End Test Runner Script
# Usage: ./tests/run-e2e-tests.sh [options]

set -e  # Exit on any error

# Default configuration
API_BASE_URL="http://localhost:8001"
AUTH_TOKEN="admin-token"
TEST_TIMEOUT="30000"
CLEANUP_ON_FAILURE="true"
VERBOSE="false"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
log() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# Function to show usage
usage() {
    echo "End-to-End Test Runner for Vitea.ai Policy Management System"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --url URL           API base URL (default: http://localhost:8001)"
    echo "  -t, --token TOKEN       Authentication token (default: admin-token)"
    echo "  -T, --timeout MS        Test timeout in milliseconds (default: 30000)"
    echo "  -c, --no-cleanup        Disable cleanup on failure"
    echo "  -v, --verbose           Enable verbose logging"
    echo "  -h, --help              Show this help message"
    echo "  --check-deps            Check and install dependencies"
    echo "  --dry-run               Show what would be executed without running"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL           Override API base URL"
    echo "  AUTH_TOKEN             Override authentication token"
    echo "  TEST_TIMEOUT           Override test timeout"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with defaults"
    echo "  $0 --url https://api.staging.vitea.ai --token \$STAGING_TOKEN"
    echo "  $0 --verbose --no-cleanup"
    echo "  $0 --check-deps"
}

# Function to check prerequisites
check_prerequisites() {
    log $BLUE "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log $RED "❌ Node.js is not installed. Please install Node.js 14+ and try again."
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 14 ]]; then
        log $YELLOW "⚠️  Node.js version $node_version detected. Recommended: 14+"
    else
        log $GREEN "✅ Node.js version: $(node --version)"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log $RED "❌ npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    log $GREEN "✅ npm version: $(npm --version)"
}

# Function to check and install dependencies
check_dependencies() {
    log $BLUE "Checking Node.js dependencies..."
    
    # Check if package.json exists
    if [[ ! -f "package.json" ]]; then
        log $YELLOW "⚠️  No package.json found. Creating minimal package.json for tests..."
        cat > package.json << EOF
{
  "name": "vitea-policy-tests",
  "version": "1.0.0",
  "description": "E2E tests for Vitea.ai Policy Management System",
  "main": "tests/e2e-policy-lifecycle-test.js",
  "scripts": {
    "test:e2e": "node tests/e2e-policy-lifecycle-test.js"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "assert": "^2.0.0"
  }
}
EOF
    fi
    
    # Install dependencies
    log $BLUE "Installing dependencies..."
    if npm list axios &> /dev/null && npm list assert &> /dev/null; then
        log $GREEN "✅ Dependencies already installed"
    else
        log $BLUE "Installing missing dependencies..."
        npm install axios assert
        log $GREEN "✅ Dependencies installed"
    fi
}

# Function to check API server connectivity
check_api_server() {
    log $BLUE "Checking API server connectivity..."
    
    # Try to reach the health endpoint
    if curl -s -f "${API_BASE_URL}/health" > /dev/null 2>&1; then
        log $GREEN "✅ API server is reachable at ${API_BASE_URL}"
    else
        log $YELLOW "⚠️  Cannot reach API server at ${API_BASE_URL}"
        log $YELLOW "    Make sure the API server is running and accessible"
        
        # Try common alternatives
        for port in 8000 3001 8001; do
            local alt_url="http://localhost:${port}"
            if curl -s -f "${alt_url}/health" > /dev/null 2>&1; then
                log $YELLOW "    Found API server at ${alt_url}"
                log $YELLOW "    Consider using: $0 --url ${alt_url}"
                break
            fi
        done
        
        # Don't exit here - let the test handle the connection error
    fi
}

# Function to run the tests
run_tests() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local test_script="${script_dir}/e2e-policy-lifecycle-test.js"
    
    if [[ ! -f "$test_script" ]]; then
        log $RED "❌ Test script not found: $test_script"
        exit 1
    fi
    
    log $BLUE "Starting E2E Policy Lifecycle Tests..."
    log $BLUE "Configuration:"
    log $BLUE "  API URL: ${API_BASE_URL}"
    log $BLUE "  Auth Token: ${AUTH_TOKEN:0:10}..." # Show only first 10 chars
    log $BLUE "  Timeout: ${TEST_TIMEOUT}ms"
    log $BLUE "  Cleanup on Failure: ${CLEANUP_ON_FAILURE}"
    
    # Set environment variables for the test script
    export API_BASE_URL="${API_BASE_URL}"
    export AUTH_TOKEN="${AUTH_TOKEN}"
    export TEST_TIMEOUT="${TEST_TIMEOUT}"
    export CLEANUP_ON_FAILURE="${CLEANUP_ON_FAILURE}"
    
    # Run the test script
    if [[ "$VERBOSE" == "true" ]]; then
        node "$test_script"
    else
        # Filter output to show only important messages
        node "$test_script" | grep -E "\[(START|PASS|FAIL|ERROR|SUCCESS|SUMMARY)\]"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            API_BASE_URL="$2"
            shift 2
            ;;
        -t|--token)
            AUTH_TOKEN="$2"
            shift 2
            ;;
        -T|--timeout)
            TEST_TIMEOUT="$2"
            shift 2
            ;;
        -c|--no-cleanup)
            CLEANUP_ON_FAILURE="false"
            shift
            ;;
        -v|--verbose)
            VERBOSE="true"
            shift
            ;;
        --check-deps)
            check_prerequisites
            check_dependencies
            exit 0
            ;;
        --dry-run)
            log $BLUE "Dry run mode - showing configuration:"
            log $BLUE "  API URL: ${API_BASE_URL}"
            log $BLUE "  Auth Token: ${AUTH_TOKEN:0:10}..."
            log $BLUE "  Timeout: ${TEST_TIMEOUT}ms"
            log $BLUE "  Cleanup on Failure: ${CLEANUP_ON_FAILURE}"
            log $BLUE "  Verbose: ${VERBOSE}"
            exit 0
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log $RED "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Override with environment variables if set
API_BASE_URL="${API_BASE_URL:-$API_BASE_URL}"
AUTH_TOKEN="${AUTH_TOKEN:-$AUTH_TOKEN}"
TEST_TIMEOUT="${TEST_TIMEOUT:-$TEST_TIMEOUT}"

# Main execution
main() {
    log $GREEN "🚀 Vitea.ai Policy Management E2E Test Runner"
    log $GREEN "============================================="
    
    # Run prerequisite checks
    check_prerequisites
    check_dependencies
    check_api_server
    
    # Run the tests
    local start_time=$(date +%s)
    
    if run_tests; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log $GREEN "🎉 Tests completed successfully in ${duration}s"
        exit 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log $RED "💥 Tests failed after ${duration}s"
        exit 1
    fi
}

# Trap to handle script interruption
trap 'log $YELLOW "Test execution interrupted"; exit 130' INT

# Execute main function
main "$@"