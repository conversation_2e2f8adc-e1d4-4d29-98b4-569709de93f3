#!/usr/bin/env node

/**
 * Quick Health Check Script
 * 
 * Performs a rapid health check of all API endpoints used in the E2E tests
 * without creating or modifying any data.
 * 
 * Usage: node tests/quick-health-check.js
 */

const axios = require('axios');

const CONFIG = {
  BASE_URL: process.env.API_BASE_URL || 'http://localhost:8001',
  AUTH_TOKEN: process.env.AUTH_TOKEN || 'admin-token',
  TEST_TIMEOUT: 10000
};

const log = (message, type = 'INFO') => {
  const timestamp = new Date().toISOString();
  const colors = {
    INFO: '\x1b[36m',    // Cyan
    PASS: '\x1b[32m',    // Green  
    FAIL: '\x1b[31m',    // Red
    WARN: '\x1b[33m',    // Yellow
    RESET: '\x1b[0m'     // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] [${type}] ${message}${colors.RESET}`);
};

class HealthChecker {
  constructor() {
    this.client = axios.create({
      baseURL: CONFIG.BASE_URL,
      headers: {
        'Authorization': `Bearer ${CONFIG.AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: CONFIG.TEST_TIMEOUT
    });
    
    this.results = {
      passed: 0,
      failed: 0,
      endpoints: []
    };
  }

  async checkEndpoint(name, method, path, expectedStatus = 200) {
    try {
      log(`Checking ${method} ${path}`, 'INFO');
      
      let response;
      switch (method.toUpperCase()) {
        case 'GET':
          response = await this.client.get(path);
          break;
        case 'POST':
          response = await this.client.post(path, {});
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      
      if (response.status === expectedStatus) {
        this.results.passed++;
        this.results.endpoints.push({ name, status: 'PASS', code: response.status });
        log(`✅ ${name}: ${response.status}`, 'PASS');
        return true;
      } else {
        this.results.failed++;
        this.results.endpoints.push({ name, status: 'FAIL', code: response.status, expected: expectedStatus });
        log(`❌ ${name}: Got ${response.status}, expected ${expectedStatus}`, 'FAIL');
        return false;
      }
    } catch (error) {
      this.results.failed++;
      const statusCode = error.response?.status || 'CONN_ERR';
      this.results.endpoints.push({ name, status: 'FAIL', code: statusCode, error: error.message });
      log(`❌ ${name}: ${error.message}`, 'FAIL');
      return false;
    }
  }

  async runHealthChecks() {
    log('🏥 Starting API Health Check...', 'INFO');
    log(`Base URL: ${CONFIG.BASE_URL}`, 'INFO');
    log(`Auth Token: ${CONFIG.AUTH_TOKEN.substring(0, 10)}...`, 'INFO');
    
    // Core API endpoints
    await this.checkEndpoint('Health Check', 'GET', '/health');
    await this.checkEndpoint('API Test', 'GET', '/api/v1/test');
    
    // Schema endpoints
    await this.checkEndpoint('Schemas List', 'GET', '/api/v1/schemas');
    await this.checkEndpoint('Schema Metadata', 'GET', '/api/v1/schemas/list');
    
    // Policy endpoints
    await this.checkEndpoint('Policies List', 'GET', '/api/v1/policies');
    
    // Policy Group endpoints  
    await this.checkEndpoint('Policy Groups List', 'GET', '/api/v1/policy-groups');
    
    // Agent endpoints
    await this.checkEndpoint('Agents List', 'GET', '/api/v1/agents');
    
    // Role endpoints
    await this.checkEndpoint('Roles List', 'GET', '/api/v1/roles');
    
    // Metrics endpoint
    await this.checkEndpoint('Metrics', 'GET', '/api/v1/metrics');

    this.printSummary();
    return this.results.failed === 0;
  }

  printSummary() {
    log('', 'INFO');
    log('='.repeat(60), 'INFO');
    log('API HEALTH CHECK SUMMARY', 'INFO');
    log('='.repeat(60), 'INFO');
    log(`Total Endpoints: ${this.results.passed + this.results.failed}`, 'INFO');
    log(`✅ Healthy: ${this.results.passed}`, this.results.passed > 0 ? 'PASS' : 'INFO');
    log(`❌ Unhealthy: ${this.results.failed}`, this.results.failed > 0 ? 'FAIL' : 'INFO');
    
    if (this.results.failed > 0) {
      log('\nFailed Endpoints:', 'WARN');
      this.results.endpoints
        .filter(ep => ep.status === 'FAIL')
        .forEach(ep => {
          log(`  - ${ep.name}: ${ep.code} ${ep.error ? `(${ep.error})` : ''}`, 'FAIL');
        });
    }
    
    log('='.repeat(60), 'INFO');
    
    if (this.results.failed === 0) {
      log('🎉 ALL ENDPOINTS HEALTHY! Ready for E2E tests! 🎉', 'PASS');
    } else {
      log('💥 SOME ENDPOINTS UNHEALTHY! Fix issues before running E2E tests! 💥', 'FAIL');
    }
  }
}

async function main() {
  const checker = new HealthChecker();
  
  try {
    const healthy = await checker.runHealthChecks();
    process.exit(healthy ? 0 : 1);
  } catch (error) {
    log(`Health check failed: ${error.message}`, 'FAIL');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = HealthChecker;