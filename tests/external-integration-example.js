#!/usr/bin/env node
/**
 * External Integration Testing Example
 * 
 * This script demonstrates how an external system would:
 * 1. Receive webhooks from Vitea
 * 2. Pull snapshot data via API
 * 3. Make updates to agent role policies and policies
 * 
 * Based on the user's requirements for updating:
 * - agent_role_policies with agent_id, agent_active, policy_ids
 * - policies with policy_id, is_active
 */

import express from 'express';
import crypto from 'crypto';
import fetch from 'node-fetch';
import { randomUUID } from 'crypto';

const config = {
  // Vitea API configuration
  vitea_api_base: process.env.VITEA_API_BASE || 'http://localhost:8000/api/v1',
  auth_token: process.env.VITEA_AUTH_TOKEN || 'admin-token',
  
  // Webhook receiver configuration  
  webhook_port: process.env.WEBHOOK_RECEIVER_PORT || 9000,
  webhook_secret: process.env.INTEGRATION_WEBHOOK_SECRET_ACTIVE || 'test-secret',
  
  // Integration test token for snapshot API
  integration_token: process.env.INTEGRATIONS_TEST_TOKEN || 'test-token'
};

class ExternalIntegrationTest {
  constructor() {
    this.receivedWebhooks = new Map();
    this.app = express();
    this.setupWebhookReceiver();
  }
  
  setupWebhookReceiver() {
    this.app.use(express.json({ type: '*/*' }));
    
    // Webhook endpoint - matches your webhook-receiver.js pattern
    this.app.post('/webhooks/policy-events', (req, res) => {
      try {
        const rawBody = JSON.stringify(req.body);
        const signature = req.headers['x-signature'] || '';
        const eventId = req.headers['x-event-id'];
        const eventType = req.headers['x-event-type'];
        
        console.log(`[Webhook] Received: ${eventType} (${eventId})`);
        
        // Verify HMAC signature (like your webhook-receiver.js)
        if (!this.verifySignature(rawBody, signature)) {
          console.error('[Webhook] Invalid signature');
          return res.status(401).json({ error: 'Invalid signature' });
        }
        
        // Store webhook for testing
        this.receivedWebhooks.set(eventId, {
          ...req.body,
          headers: {
            eventId,
            eventType,
            timestamp: req.headers['x-timestamp'],
            correlationId: req.headers['x-correlation-id']
          },
          receivedAt: new Date()
        });
        
        console.log(`[Webhook] Processed: ${eventType} for ${JSON.stringify(req.body.subject?.resource_id)}`);
        res.json({ status: 'processed', eventId });
        
      } catch (error) {
        console.error('[Webhook] Processing error:', error);
        res.status(500).json({ error: 'Processing failed' });
      }
    });
  }
  
  verifySignature(rawBody, signature) {
    if (!config.webhook_secret) return true; // Skip if no secret
    const expectedSignature = crypto
      .createHmac('sha256', config.webhook_secret)
      .update(rawBody)
      .digest('base64');
    return expectedSignature === signature;
  }
  
  async startWebhookReceiver() {
    return new Promise((resolve) => {
      this.server = this.app.listen(config.webhook_port, () => {
        console.log(`[Webhook] Receiver listening on http://localhost:${config.webhook_port}/webhooks/policy-events`);
        resolve();
      });
    });
  }
  
  async stopWebhookReceiver() {
    if (this.server) {
      this.server.close();
    }
  }
  
  // External system API calls to Vitea
  
  /**
   * Update agent role policies as specified by user
   * @param {string} agentId 
   * @param {Object} updates - { agent_active: boolean, policy_ids: string[], roleId: string, groupId: string }
   */
  async updateAgentRolePolicies(agentId, updates) {
    console.log(`[API] Updating agent role policies for ${agentId}:`, updates);
    
    try {
      // 1. Update agent active status (if specified)
      if (updates.agent_active !== undefined) {
        console.log(`[API] Setting agent ${agentId} active status to ${updates.agent_active}`);
        
        const agentResponse = await fetch(`${config.vitea_api_base}/agents/${agentId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.auth_token}`
          },
          body: JSON.stringify({
            is_active: updates.agent_active
          })
        });
        
        if (!agentResponse.ok) {
          throw new Error(`Failed to update agent: ${agentResponse.status} ${await agentResponse.text()}`);
        }
        
        console.log(`[API] Agent ${agentId} active status updated successfully`);
      }
      
      // 2. Bulk assign policies to agent role (if policy_ids provided)
      if (updates.policy_ids && updates.policy_ids.length > 0) {
        console.log(`[API] Bulk assigning ${updates.policy_ids.length} policies to agent ${agentId}`);
        
        const bulkResponse = await fetch(`${config.vitea_api_base}/agents/${agentId}/role-policies/bulk`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json', 
            'Authorization': `Bearer ${config.auth_token}`
          },
          body: JSON.stringify({
            roleId: updates.roleId,
            groupId: updates.groupId,
            policyIds: updates.policy_ids
          })
        });
        
        if (!bulkResponse.ok) {
          throw new Error(`Failed to bulk assign policies: ${bulkResponse.status} ${await bulkResponse.text()}`);
        }
        
        const result = await bulkResponse.json();
        console.log(`[API] Bulk assignment completed:`, result);
      }
      
      return { success: true };
      
    } catch (error) {
      console.error(`[API] Error updating agent role policies:`, error);
      throw error;
    }
  }
  
  /**
   * Update policy as specified by user
   * @param {string} policyId 
   * @param {Object} updates - { is_active: boolean }
   */
  async updatePolicy(policyId, updates) {
    console.log(`[API] Updating policy ${policyId}:`, updates);
    
    try {
      // First get current policy details (required for PUT)
      const getCurrentResponse = await fetch(`${config.vitea_api_base}/policies/${policyId}`, {
        headers: {
          'Authorization': `Bearer ${config.auth_token}`
        }
      });
      
      if (!getCurrentResponse.ok) {
        throw new Error(`Failed to get current policy: ${getCurrentResponse.status}`);
      }
      
      const currentPolicy = await getCurrentResponse.json();
      console.log(`[API] Current policy retrieved: ${currentPolicy.name}`);
      
      // Update with new values
      const updateResponse = await fetch(`${config.vitea_api_base}/policies/${policyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.auth_token}`
        },
        body: JSON.stringify({
          ...currentPolicy,
          is_active: updates.is_active
        })
      });
      
      if (!updateResponse.ok) {
        throw new Error(`Failed to update policy: ${updateResponse.status} ${await updateResponse.text()}`);
      }
      
      const result = await updateResponse.json();
      console.log(`[API] Policy ${policyId} updated successfully:`, result);
      
      return result;
      
    } catch (error) {
      console.error(`[API] Error updating policy:`, error);
      throw error;
    }
  }
  
  /**
   * Pull assignment snapshot from Vitea
   */
  async pullAssignmentSnapshot() {
    console.log('[API] Pulling assignment snapshot...');
    
    try {
      let cursor = null;
      const allAssignments = [];
      let pageCount = 0;
      
      do {
        pageCount++;
        const params = new URLSearchParams({
          page_size: '500',
          ...(cursor && { cursor })
        });
        
        console.log(`[API] Fetching snapshot page ${pageCount}...`);
        
        const response = await fetch(
          `${config.vitea_api_base}/integrations/assignments-snapshot?${params}`,
          {
            headers: {
              'Authorization': `Bearer ${config.integration_token}`
            }
          }
        );
        
        if (!response.ok) {
          throw new Error(`Snapshot API error: ${response.status} ${await response.text()}`);
        }
        
        const data = await response.json();
        allAssignments.push(...data.items);
        cursor = data.next_cursor;
        
        console.log(`[API] Page ${pageCount}: Got ${data.items.length} items, next_cursor: ${cursor ? 'exists' : 'null'}`);
        
      } while (cursor);
      
      console.log(`[API] Snapshot complete: ${allAssignments.length} total assignments across ${pageCount} pages`);
      return allAssignments;
      
    } catch (error) {
      console.error('[API] Error pulling snapshot:', error);
      throw error;
    }
  }
  
  /**
   * Wait for a specific webhook event
   */
  async waitForWebhook(eventType, resourceId, timeout = 30000) {
    console.log(`[Test] Waiting for webhook: ${eventType} (timeout: ${timeout}ms)`);
    
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Webhook ${eventType} not received within ${timeout}ms`));
      }, timeout);
      
      const checkWebhook = () => {
        for (const [eventId, webhook] of this.receivedWebhooks) {
          if (webhook.event_type === eventType) {
            // Match resource ID if provided
            if (!resourceId || this.matchesResourceId(webhook, resourceId)) {
              clearTimeout(timeoutId);
              console.log(`[Test] Webhook received: ${eventType} (${eventId})`);
              resolve(webhook);
              return;
            }
          }
        }
        setTimeout(checkWebhook, 100);
      };
      
      checkWebhook();
    });
  }
  
  matchesResourceId(webhook, resourceId) {
    const subject = webhook.subject?.resource_id;
    if (!subject) return false;
    
    // Check if resourceId matches any of the IDs in the webhook
    return Object.values(subject).includes(resourceId);
  }
  
  /**
   * Complete integration test demonstrating the user's requirements
   */
  async runCompleteIntegrationTest() {
    console.log('='.repeat(60));
    console.log('🚀 External Integration Test Starting');
    console.log('='.repeat(60));
    
    try {
      // 1. Start webhook receiver
      await this.startWebhookReceiver();
      console.log('✅ Webhook receiver started');
      
      // 2. Test the user's update patterns
      console.log('\n📊 Testing update patterns from user requirements...');
      
      // Example agent and policy IDs (you'd get these from your test data)
      const testAgentId = '89c0d4ee-b4f0-4494-a8fc-3fe6e79de729';
      const testPolicyId = '5ce919b4-7fb2-4ecf-af56-ac103f3701d6';
      const testRoleId = 'sample-role-uuid';
      const testGroupId = 'sample-group-uuid';
      
      // User's first example: update agent_role_policies
      console.log('\\n🔄 Testing agent_role_policies update...');
      const agentUpdates = {
        agent_active: true,
        policy_ids: [
          '5ce919b4-7fb2-4ecf-af56-ac103f3701d6',
          '597df3ba-ae0d-4e38-ba40-aab0f84beb88',
          '1cad0241-8fff-4fa2-aa2c-bed60f28d67f',
          'aa3c7ffd-0a16-4d40-9210-74ef909ba2fd'
        ],
        roleId: testRoleId,
        groupId: testGroupId
      };
      
      await this.updateAgentRolePolicies(testAgentId, agentUpdates);
      console.log('✅ Agent role policies update completed');
      
      // User's second example: update policies
      console.log('\\n🔄 Testing policy update...');
      const policyUpdates = {
        is_active: true
      };
      
      await this.updatePolicy(testPolicyId, policyUpdates);
      console.log('✅ Policy update completed');
      
      // 3. Pull snapshot to verify state
      console.log('\\n📸 Pulling assignment snapshot...');
      const snapshot = await this.pullAssignmentSnapshot();
      console.log(`✅ Snapshot retrieved: ${snapshot.length} assignments`);
      
      // 4. Show webhook summary
      console.log('\\n📥 Webhook Summary:');
      console.log(`Received ${this.receivedWebhooks.size} webhooks:`);
      for (const [eventId, webhook] of this.receivedWebhooks) {
        console.log(`  - ${webhook.event_type} (${eventId})`);
      }
      
      console.log('\\n' + '='.repeat(60));
      console.log('🎉 Integration test completed successfully!');
      console.log('✅ External system can now:');
      console.log('  • Receive webhooks from Vitea');
      console.log('  • Update agent role policies');
      console.log('  • Update policy activation status');
      console.log('  • Pull full state snapshots');
      console.log('='.repeat(60));
      
    } catch (error) {
      console.error('❌ Integration test failed:', error);
      throw error;
    } finally {
      await this.stopWebhookReceiver();
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new ExternalIntegrationTest();
  
  // Check command line arguments
  const command = process.argv[2];
  
  switch (command) {
    case 'webhook-only':
      console.log('Starting webhook receiver only (Ctrl+C to stop)...');
      await tester.startWebhookReceiver();
      process.on('SIGINT', async () => {
        console.log('\\nStopping webhook receiver...');
        await tester.stopWebhookReceiver();
        process.exit(0);
      });
      break;
      
    case 'update-agent':
      await tester.updateAgentRolePolicies(process.argv[3], {
        agent_active: true,
        policy_ids: process.argv[4]?.split(',') || [],
        roleId: process.argv[5],
        groupId: process.argv[6]
      });
      break;
      
    case 'update-policy': 
      await tester.updatePolicy(process.argv[3], {
        is_active: process.argv[4] === 'true'
      });
      break;
      
    case 'snapshot':
      const snapshot = await tester.pullAssignmentSnapshot();
      console.log(JSON.stringify(snapshot, null, 2));
      break;
      
    default:
      await tester.runCompleteIntegrationTest();
  }
}

export default ExternalIntegrationTest;