# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# Dependencies
node_modules/
admin-ui-project/node_modules/
enhanced-api-project/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.vite/
.parcel-cache/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Database
*.sqlite
*.sqlite3
*.db
data/
postgres-data/
mysql-data/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Django
*.pot
local_settings.py
db.sqlite3

# Flask
instance/
.webassets-cache

# Java
*.class
*.jar
*.war
*.ear
target/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/
.temp/

# Docker
.dockerignore
docker-compose.override.yml

# Testing
.jest/
.nyc_output/
coverage/

# Serverless
.serverless/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Misc
*.tgz
*.tar.gz
.sass-cache/
.eslintcache
.stylelintcache