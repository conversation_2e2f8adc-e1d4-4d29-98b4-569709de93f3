// Example: How to integrate external system calls into Agent Registry page
// This shows how to modify your existing agent and policy management functionality

import React, { useState } from 'react';
import { 
  Switch, 
  IconButton, 
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ExternalIntegrationDialog from '../components/ExternalIntegrationDialog';
import useExternalIntegration from '../hooks/useExternalIntegration';

// This would replace or enhance your existing AgentDetailsModal component
const AgentRegistryWithIntegration = () => {
  const [selectedAgent, setSelectedAgent] = useState({
    agent_id: '89c0d4ee-b4f0-4494-a8fc-3fe6e79de729',
    name: 'CDI Agent',
    is_active: true,
    vendor: 'Nuance',
    department: 'CDS',
    purpose: 'custom'
  });

  const [agentPolicies, setAgentPolicies] = useState([
    {
      role: 'Admin',
      policy_group: 'DRG Group',
      policy_id: '5ce919b4-7fb2-4ecf-af56-ac103f3701d6',
      policy_name: 'DRG - Policy 1'
    },
    {
      role: 'Admin',
      policy_group: 'DRG Group', 
      policy_id: '597df3ba-ae0d-4e38-ba40-aab0f84beb88',
      policy_name: 'DRG - Policy 2'
    },
    {
      role: 'Admin',
      policy_group: 'DRG Group',
      policy_id: '1cad0241-8fff-4fa2-aa2c-bed60f28d67f', 
      policy_name: 'DRG - Policy 3'
    }
  ]);

  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handleAgentPolicyDelete,
    handleAgentToggle
  } = useExternalIntegration();

  // Your existing API functions (keep as is)
  const deleteAgentPolicyInVitea = async (agentId, policyId) => {
    const response = await fetch(`/api/v1/agents/${agentId}/policies/${policyId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': 'Bearer admin-token'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to delete policy: ${response.status}`);
    }
  };

  const updateAgentInVitea = async (agentId, isActive) => {
    const response = await fetch(`/api/v1/agents/${agentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin-token'
      },
      body: JSON.stringify({
        is_active: isActive
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to update agent: ${response.status}`);
    }

    return response.json();
  };

  // Enhanced policy deletion handler
  const onDeletePolicy = (policyToDelete) => {
    handleAgentPolicyDelete(
      selectedAgent.agent_id,
      policyToDelete.policy_id,
      selectedAgent.name,
      policyToDelete.policy_name,
      // Local update callback
      async () => {
        await deleteAgentPolicyInVitea(selectedAgent.agent_id, policyToDelete.policy_id);
        
        // Update local state
        setAgentPolicies(prev => prev.filter(p => p.policy_id !== policyToDelete.policy_id));
      },
      // Function to get remaining policy IDs after deletion
      async () => {
        return agentPolicies
          .filter(p => p.policy_id !== policyToDelete.policy_id)
          .map(p => p.policy_id);
      }
    );
  };

  // Enhanced agent toggle handler
  const onAgentToggle = () => {
    const newStatus = !selectedAgent.is_active;
    
    handleAgentToggle(
      selectedAgent.agent_id,
      newStatus,
      selectedAgent.name,
      // Local update callback
      async () => {
        await updateAgentInVitea(selectedAgent.agent_id, newStatus);
        
        // Update local state
        setSelectedAgent(prev => ({ ...prev, is_active: newStatus }));
      },
      // Function to get current agent policy IDs
      async () => {
        return agentPolicies.map(p => p.policy_id);
      }
    );
  };

  return (
    <Dialog open={true} maxWidth="md" fullWidth>
      <DialogTitle>
        Agent Details: {selectedAgent.name}
      </DialogTitle>
      
      <DialogContent>
        {/* Agent Info */}
        <div style={{ marginBottom: '20px' }}>
          <p><strong>Vendor:</strong> {selectedAgent.vendor}</p>
          <p><strong>Department:</strong> {selectedAgent.department}</p>
          <p><strong>Purpose:</strong> {selectedAgent.purpose}</p>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <strong>Active:</strong>
            <Switch
              checked={selectedAgent.is_active}
              onChange={onAgentToggle}
              color="primary"
            />
            <span>{selectedAgent.is_active ? 'Yes' : 'No'}</span>
          </div>
        </div>

        {/* Policy Assignments */}
        <div>
          <h4>Policy Assignments</h4>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Role</TableCell>
                <TableCell>Policy Group</TableCell>
                <TableCell>Policy</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {agentPolicies.map((policy, index) => (
                <TableRow key={policy.policy_id}>
                  <TableCell>{policy.role}</TableCell>
                  <TableCell>{policy.policy_group}</TableCell>
                  <TableCell>{policy.policy_name}</TableCell>
                  <TableCell>
                    <IconButton
                      color="error"
                      onClick={() => onDeletePolicy(policy)}
                      title="Remove policy from agent"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </DialogContent>

      {/* External Integration Dialog */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </Dialog>
  );
};

export default AgentRegistryWithIntegration;