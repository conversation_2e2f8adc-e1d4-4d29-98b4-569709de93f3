// Example: How to integrate external system calls into Policy Configuration page
// This shows how to modify your existing policy toggle functionality

import React, { useState } from 'react';
import { Switch, IconButton, Tooltip } from '@mui/material';
import ExternalIntegrationDialog from '../components/ExternalIntegrationDialog';
import useExternalIntegration from '../hooks/useExternalIntegration';

// This would replace or enhance your existing PolicyConfiguration component
const PolicyConfigurationWithIntegration = () => {
  const [policies, setPolicies] = useState([
    {
      policy_id: '5ce919b4-7fb2-4ecf-af56-ac103f3701d6',
      name: 'DRG - Policy 1',
      is_active: true,
      category: 'Data Privacy',
      severity: 'medium'
    },
    {
      policy_id: '597df3ba-ae0d-4e38-ba40-aab0f84beb88', 
      name: 'DRG - Policy 2',
      is_active: true,
      category: 'Data Privacy',
      severity: 'medium'
    },
    {
      policy_id: '1cad0241-8fff-4fa2-aa2c-bed60f28d67f',
      name: 'DRG - Policy 3', 
      is_active: true,
      category: 'Data Privacy',
      severity: 'medium'
    }
  ]);

  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handlePolicyToggle
  } = useExternalIntegration();

  // Your existing API call function (keep as is)
  const updatePolicyInVitea = async (policyId, isActive) => {
    const response = await fetch(`/api/v1/policies/${policyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin-token'
      },
      body: JSON.stringify({
        // Include existing policy data plus the new status
        is_active: isActive
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to update policy: ${response.status}`);
    }

    return response.json();
  };

  // Enhanced policy toggle handler
  const onPolicyToggleClick = (policy) => {
    const newStatus = !policy.is_active;
    
    handlePolicyToggle(
      policy.policy_id,
      newStatus,
      policy.name,
      // This callback handles the local Vitea update
      async () => {
        await updatePolicyInVitea(policy.policy_id, newStatus);
        
        // Update local state
        setPolicies(prev => prev.map(p => 
          p.policy_id === policy.policy_id 
            ? { ...p, is_active: newStatus }
            : p
        ));
      }
    );
  };

  return (
    <div>
      <h2>Policy Configuration</h2>
      
      {/* Your existing policy list */}
      <div>
        {policies.map(policy => (
          <div key={policy.policy_id} style={{ 
            display: 'flex', 
            alignItems: 'center', 
            padding: '10px',
            border: '1px solid #ddd',
            margin: '5px 0'
          }}>
            <div style={{ flex: 1 }}>
              <strong>{policy.name}</strong>
              <div>Category: {policy.category} | Severity: {policy.severity}</div>
            </div>
            
            <Tooltip title={`${policy.is_active ? 'Deactivate' : 'Activate'} policy globally`}>
              <Switch
                checked={policy.is_active}
                onChange={() => onPolicyToggleClick(policy)}
                color="primary"
              />
            </Tooltip>
          </div>
        ))}
      </div>

      {/* External Integration Dialog */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </div>
  );
};

export default PolicyConfigurationWithIntegration;