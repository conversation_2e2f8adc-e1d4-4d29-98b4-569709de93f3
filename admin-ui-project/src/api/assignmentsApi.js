import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/dist/query/react';
import { getApiBaseUrl } from './getApiBase.js';

export const assignmentsApi = createApi({
  reducerPath: 'assignmentsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getApiBaseUrl()}/api/v1/`,
    prepareHeaders: (headers) => {
      headers.set('Authorization', 'Bearer admin-token');
      return headers;
    },
  }),
  tagTypes: ['AgentPolicy', 'Agent'],
  endpoints: (builder) => ({
    bulkAssign: builder.mutation({
      query: (body) => ({
        url: 'bulk-policy-assign',
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, { targetAgentIds }) => [
        ...targetAgentIds.map((id) => ({ type: 'AgentPolicy', id })),
        ...targetAgentIds.map((id) => ({ type: 'Agent', id })),
      ],
    }),
    unassign: builder.mutation({
      query: (agentPolicyId) => ({
        url: `agent-policies/${agentPolicyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, agentPolicyId) => [{ type: 'AgentPolicy', id: agentPolicyId }],
    }),
  }),
});

export const { useBulkAssignMutation, useUnassignMutation } = assignmentsApi;