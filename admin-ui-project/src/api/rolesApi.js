import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/dist/query/react';
import { getApiBaseUrl } from './getApiBase.js';

export const rolesApi = createApi({
  reducerPath: 'rolesApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getApiBaseUrl()}/api/v1/`,
    prepareHeaders: (headers) => {
      headers.set('Authorization', 'Bearer admin-token');
      return headers;
    },
  }),
  tagTypes: ['Role'],
  endpoints: (builder) => ({
    listRoles: builder.query({
      query: () => 'roles',
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ role_id }) => ({ type: 'Role', id: role_id })),
              { type: 'Role', id: 'LIST' },
            ]
          : [{ type: 'Role', id: 'LIST' }],
    }),
    createRole: builder.mutation({
      query: (body) => ({ url: 'roles', method: 'POST', body }),
      invalidatesTags: [{ type: 'Role', id: 'LIST' }],
    }),
    updateRole: builder.mutation({
      query: ({ id, ...patch }) => ({ url: `roles/${id}`, method: 'PUT', body: patch }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Role', id },
        { type: 'Role', id: 'LIST' },
      ],
    }),
    deleteRole: builder.mutation({
      query: (id) => ({ url: `roles/${id}`, method: 'DELETE' }),
      invalidatesTags: [{ type: 'Role', id: 'LIST' }],
    }),
  }),
});

export const {
  useListRolesQuery,
  useCreateRoleMutation,
  useUpdateRoleMutation,
  useDeleteRoleMutation,
} = rolesApi;