import { getApiBaseUrl } from './getApiBase.js';

const BASE = `${getApiBaseUrl()}/api/v1/roles`;
const headers = {
  'Content-Type': 'application/json',
  Authorization: 'admin',
};

export async function listRoles() {
  const res = await fetch(BASE, { headers });
  if (!res.ok) throw new Error('Failed to fetch roles');
  return res.json();
}

export async function createRole(payload) {
  const res = await fetch(BASE, { method: 'POST', headers, body: JSON.stringify(payload) });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function updateRole(id, payload) {
  const res = await fetch(`${BASE}/${id}`, { method: 'PUT', headers, body: JSON.stringify(payload) });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function deleteRole(id) {
  const res = await fetch(`${BASE}/${id}`, { method: 'DELETE', headers });
  if (!res.ok) throw new Error(await res.text());
}
