import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/dist/query/react';
import { getApiBaseUrl } from './getApiBase.js';

export const policyGroupsApi = createApi({
  reducerPath: 'policyGroupsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getApiBaseUrl()}/api/v1/`,
    prepareHeaders: (headers) => {
      headers.set('Authorization', 'Bearer admin-token');
      return headers;
    },
  }),
  tagTypes: ['PolicyGroup'],
  endpoints: (builder) => ({
    listPolicyGroups: builder.query({
      query: (status = 'active') => `policy-groups?status=${status}`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ group_id }) => ({ type: 'PolicyGroup', id: group_id })),
              { type: 'PolicyGroup', id: 'LIST' },
            ]
          : [{ type: 'PolicyGroup', id: 'LIST' }],
    }),
    createPolicyGroup: builder.mutation({
      query: (body) => ({
        url: 'policy-groups',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'PolicyGroup', id: 'LIST' }],
    }),
    updatePolicyGroup: builder.mutation({
      query: ({ id, ...patch }) => ({
        url: `policy-groups/${id}`,
        method: 'PUT',
        body: patch,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'PolicyGroup', id },
        { type: 'PolicyGroup', id: 'LIST' },
      ],
    }),
    deprecatePolicyGroup: builder.mutation({
      query: (id) => ({
        url: `policy-groups/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'PolicyGroup', id: 'LIST' }],
    }),
    restorePolicyGroup: builder.mutation({
      query: (id) => ({
        url: `policy-groups/${id}/restore`,
        method: 'POST',
      }),
      invalidatesTags: [{ type: 'PolicyGroup', id: 'LIST' }],
    }),
  }),
});

export const {
  useListPolicyGroupsQuery,
  useCreatePolicyGroupMutation,
  useUpdatePolicyGroupMutation,
  useDeprecatePolicyGroupMutation,
  useRestorePolicyGroupMutation,
} = policyGroupsApi;