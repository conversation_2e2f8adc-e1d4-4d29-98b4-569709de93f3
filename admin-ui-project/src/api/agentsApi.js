import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/dist/query/react';
import { getApiBaseUrl } from './getApiBase.js';

export const agentsApi = createApi({
  reducerPath: 'agentsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${getApiBaseUrl()}/api/v1/`,
    prepareHeaders: (headers) => {
      headers.set('Authorization', 'Bearer admin-token');
      return headers;
    },
  }),
  tagTypes: ['Agent', 'AgentPolicy'],
  endpoints: (builder) => ({
    listAgents: builder.query({
      query: () => 'agents',
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ agent_id }) => ({ type: 'Agent', id: agent_id })),
              { type: 'Agent', id: 'LIST' },
            ]
          : [{ type: 'Agent', id: 'LIST' }],
    }),
    updateAgent: builder.mutation({
      query: ({ id, body }) => ({
        url: `agents/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Agent', id }, { type: 'Agent', id: 'LIST' }],
    }),
    deleteAgent: builder.mutation({
      query: (id) => ({
        url: `agents/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Agent', id: 'LIST' }],
    }),
    createAgent: builder.mutation({
      query: (body) => ({
        url: 'agents',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Agent', id: 'LIST' }],
    }),
    getAgent: builder.query({
      query: (id) => `agents/${id}`,
      providesTags: (result, error, id) => [{ type: 'Agent', id }],
    }),
    listAgentRoles: builder.query({
      query: (id) => `agents/${id}/roles`,
      providesTags: (result, error, id) => [{ type: 'Agent', id }],
    }),
    getAgentPolicies: builder.query({
      query: (id) => `agents/${id}/policies`,
      providesTags: (result, error, id) => [{ type: 'AgentPolicy', id }],
    }),
    grantAccess: builder.mutation({
      query: ({ id, roleId }) => ({
        url: `agents/${id}/access`,
        method: 'POST',
        body: { roleId },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Agent', id },
        { type: 'AgentPolicy', id },
      ],
    }),
    revokeAccess: builder.mutation({
      query: ({ id, roleId }) => ({
        url: `agents/${id}/access`,
        method: 'DELETE',
        body: { roleId },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Agent', id },
        { type: 'AgentPolicy', id },
      ],
    }),
    removeDirectPolicy: builder.mutation({
      query: ({ id, policyId }) => ({
        url: `agents/${id}/policies/${policyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'AgentPolicy', id },
        { type: 'Agent', id },
      ],
    }),
    removeGroupAssignments: builder.mutation({
      query: ({ id, groupId }) => ({
        url: `agents/${id}/groups/${groupId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'AgentPolicy', id },
        { type: 'Agent', id },
      ],
    }),
  }),
});

export const {
  useListAgentsQuery,
  useGetAgentQuery,
  useListAgentRolesQuery,
  useCreateAgentMutation,
  useUpdateAgentMutation,
  useDeleteAgentMutation,
  useGetAgentPoliciesQuery,
  useGrantAccessMutation,
  useRevokeAccessMutation,
  useRemoveDirectPolicyMutation,
  useRemoveGroupAssignmentsMutation,
} = agentsApi;