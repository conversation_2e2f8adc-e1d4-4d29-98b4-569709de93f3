import { getApiBaseUrl } from './getApiBase.js';

const BASE = `${getApiBaseUrl()}/api/v1/policy-groups`;
const headers = {
  'Content-Type': 'application/json',
  Authorization: 'admin', // current simplistic auth scheme
};

export async function listPolicyGroups(status='all') {
  const res = await fetch(`${BASE}?status=${status}`, { headers });
  if (!res.ok) throw new Error('Failed to fetch policy groups');
  return res.json();
}

export async function createPolicyGroup(payload) {
  const res = await fetch(BASE, {
    method: 'POST',
    headers,
    body: JSON.stringify(payload),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function updatePolicyGroup(id, payload) {
  const res = await fetch(`${BASE}/${id}`, {
    method: 'PUT',
    headers,
    body: JSON.stringify(payload),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function softDeletePolicyGroup(id) {
  const res = await fetch(`${BASE}/${id}`, { method: 'DELETE', headers });
  if (!res.ok) throw new Error(await res.text());
  if (res.status === 204) return;
  return res.json();
}

export async function restorePolicyGroup(id) {
  const res = await fetch(`${BASE}/${id}/restore`, {
    method: 'POST',
    headers,
  });
  if (!res.ok) throw new Error(await res.text());
  if (res.status === 204) return;
  return res.json();
}

export async function addPoliciesToGroup(id, policyIds) {
  const res = await fetch(`${BASE}/${id}/policies`, {
    method: 'POST',
    headers,
    body: JSON.stringify({ policyIds }),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function removePolicyFromGroup(id, policyId) {
  const res = await fetch(`${BASE}/${id}/policies/${policyId}`, {
    method: 'DELETE',
    headers,
  });
  if (!res.ok) throw new Error(await res.text());
}
