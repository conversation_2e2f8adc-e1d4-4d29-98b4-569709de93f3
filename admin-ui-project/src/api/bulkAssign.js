import { getApiBaseUrl } from './getApiBase.js';

const BASE = `${getApiBaseUrl()}/api/v1/bulk-policy-assign`;
const headers = {
  'Content-Type': 'application/json',
  Authorization: 'admin',
};

export async function bulkAssign({ policyIds = [], groupIds = [], targetAgentIds = [] }) {
  const res = await fetch(BASE, {
    method: 'POST',
    headers,
    body: JSON.stringify({ policyIds, groupIds, targetAgentIds }),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}
