import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { agentsApi } from '../api/agentsApi.js';

beforeEach(() => {
  fetch.resetMocks();
});

describe('agentsApi', () => {
  it('fetches listAgents', async () => {
    fetch.mockResponseOnce(
      JSON.stringify([{ agent_id: 'a1', name: 'Agent One' }])
    );

    const store = configureStore({
      reducer: { [agentsApi.reducerPath]: agentsApi.reducer },
      middleware: (gDM) => gDM().concat(agentsApi.middleware),
    });
    setupListeners(store.dispatch);

    const data = await store
      .dispatch(agentsApi.endpoints.listAgents.initiate())
      .unwrap();

    expect(data[0].name).toBe('Agent One');
    const called = fetch.mock.calls[0][0];
    expect(called instanceof Request ? called.url : called).toEqual(
      expect.stringContaining('/agents')
    );
  });
});