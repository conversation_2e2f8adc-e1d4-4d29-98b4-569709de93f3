import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { rolesApi } from '../api/rolesApi.js';

beforeEach(() => {
  fetch.resetMocks();
});

describe('rolesApi', () => {
  it('fetches listRoles', async () => {
    fetch.mockResponseOnce(
      JSON.stringify([{ role_id: '1', code: 'ADMIN', name: 'Administrator' }])
    );

    const store = configureStore({
      reducer: { [rolesApi.reducerPath]: rolesApi.reducer },
      middleware: (gDM) => gDM().concat(rolesApi.middleware),
    });
    setupListeners(store.dispatch);

    const data = await store
      .dispatch(rolesApi.endpoints.listRoles.initiate())
      .unwrap();

    expect(data[0].code).toBe('ADMIN');
    // Verify fetch was called with correct endpoint
    const calledUrl = fetch.mock.calls[0][0];
    expect(calledUrl instanceof Request ? calledUrl.url : calledUrl).toEqual(
      expect.stringContaining('/roles')
    );
  });
});