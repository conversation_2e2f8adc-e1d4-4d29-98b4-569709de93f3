import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { assignmentsApi } from '../api/assignmentsApi.js';

beforeEach(() => {
  fetch.resetMocks();
});

describe('assignmentsApi', () => {
  it('bulkAssign posts to API and returns success', async () => {
    fetch.mockResponseOnce(
      JSON.stringify({ message: 'Assignments created' }),
      { status: 201, headers: { 'Content-Type': 'application/json' } }
    );

    const store = configureStore({
      reducer: { [assignmentsApi.reducerPath]: assignmentsApi.reducer },
      middleware: (gDM) => gDM().concat(assignmentsApi.middleware),
    });
    setupListeners(store.dispatch);

    const payload = {
      policyIds: ['p1'],
      groupIds: [],
      targetAgentIds: ['a1'],
    };

    const data = await store
      .dispatch(assignmentsApi.endpoints.bulkAssign.initiate(payload))
      .unwrap();

    expect(data.message).toBe('Assignments created');

    const called = fetch.mock.calls[0][0];
    const req = called instanceof Request ? called : new Request(called, fetch.mock.calls[0][1]);
    expect(req.url).toMatch(/\/bulk-policy-assign$/);
    expect(req.method).toBe('POST');
    const bodyText = await req.clone().text();
    expect(JSON.parse(bodyText)).toEqual(payload);
  });
});