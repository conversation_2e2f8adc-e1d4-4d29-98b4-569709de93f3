import React, { useEffect, useState } from 'react';
import { useListPolicyGroupsQuery } from '../api/policyGroupsApi.js';
import PolicyGroupCard from '../components/PolicyGroupCard.js';
import PolicyGroupModal from '../components/PolicyGroupModal.js';
import PolicyGroupPoliciesModal from '../components/PolicyGroupPoliciesModal.js';

export default function PolicyGroupsPage() {
  const { data: groups = [], isLoading, isError, refetch } = useListPolicyGroupsQuery('all', {
    refetchOnMountOrArgChange: true
  });

  const [filtered, setFiltered] = useState([]);
  const [search, setSearch] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalGroup, setModalGroup] = useState(null);
  const [showPoliciesModal, setShowPoliciesModal] = useState(false);
  const [policiesModalGroup, setPoliciesModalGroup] = useState(null);

  const loading = isLoading;
  const error = isError ? 'Failed to load groups' : null;

  // Live filter whenever groups or search changes
  useEffect(() => {
    const q = search.toLowerCase();
    setFiltered(
      (groups || []).filter((g) =>
        g.name.toLowerCase().includes(q) || (g.description || '').toLowerCase().includes(q)
      )
    );
  }, [search, groups]);

  if (loading) return <div className="p-8 text-gray-500">Loading policy groups…</div>;
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>;

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <div className="flex-shrink-0 p-5 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-semibold">Policy Groups Manager</h1>
          <button
            className="bg-blue-600 text-white px-3 py-1.5 rounded hover:bg-blue-700"
            onClick={async () => {
              setShowModal(true);
            }}
          >
            + Create New Group
          </button>
        </div>
        <input
          type="text"
          placeholder="Search policy groups…"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-full px-2.5 py-1.5 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600"
        />
      </div>
      
      <div className="flex-1 overflow-y-auto p-5">
        {filtered.length === 0 ? (
          <p>No policy groups found.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 pb-3">
            {filtered.map((g) => (
              <PolicyGroupCard
                key={g.group_id}
                group={g}
                onEdit={(grp) => {
                  setModalGroup(grp);
                  setShowModal(true);
                }}
                onToggleStatus={async (grp) => {
                  if (grp.status === 'active') {
                    if (!window.confirm('Deprecate this group?')) return;
                    await (await import('../api/policyGroups.js')).softDeletePolicyGroup(grp.group_id);
                  } else {
                    await (await import('../api/policyGroups.js')).restorePolicyGroup(grp.group_id);
                  }
                  await refetch();
                }}
                onViewPolicies={(grp) => {
                  setPoliciesModalGroup(grp);
                  setShowPoliciesModal(true);
                }}
              />
            ))}
          </div>
        )}
      </div>
      
      {showModal && (
        <PolicyGroupModal
          existing={modalGroup}
          onClose={() => {
            setShowModal(false);
            setModalGroup(null);
          }}
          onSaved={refetch}
        />
      )}
      
      {showPoliciesModal && policiesModalGroup && (
        <PolicyGroupPoliciesModal
          groupId={policiesModalGroup.group_id}
          groupName={policiesModalGroup.name}
          onClose={() => {
            setShowPoliciesModal(false);
            setPoliciesModalGroup(null);
          }}
          onPolicyToggle={() => {
            refetch();
          }}
        />
      )}
    </div>
  );
}
