import React, { useEffect, useState } from 'react';
import {
  AlertTriangle,
  Shield,
  Activity,
  Plus,
  Search,
  Edit,
  Trash2,
  CheckCircle,
  Loader2,
  X,
} from 'lucide-react';
import EnhancedPolicyModal from '../EnhancedPolicyModal';
import { listAgents } from '../api/agents';
import { listPolicyGroups } from '../api/policyGroups';
import EnumManagement from '../components/EnumManagement';
import { getApiBaseUrl } from '../api/getApiBase';
import PolicyAssignmentsModal from '../components/PolicyAssignmentsModal';
import { fetchSchemaList } from '../utils/schemaApi';
// External Integration - Temporary dev code
import useExternalIntegration from '../hooks/useExternalIntegration';
import ExternalIntegrationDialog from '../components/ExternalIntegrationDialog';

function ToggleSwitch({ isActive, onToggle, isLoading }) {
  if (isLoading) {
    return <Loader2 className="animate-spin text-gray-500 mx-auto" />;
  }

  return (
    <label className="inline-flex items-center cursor-pointer align-middle leading-none">
      <input
        type="checkbox"
        checked={isActive}
        onChange={onToggle}
        className="sr-only peer"
        disabled={isLoading}
      />
      <div className="relative w-11 h-6 rounded-full bg-gray-300 transition-colors peer-checked:bg-green-500 after:content-[''] after:absolute after:top-0.5 after:left-0.5 after:w-5 after:h-5 after:bg-white after:rounded-full after:transition-transform peer-checked:after:translate-x-5"></div>
    </label>
  );
}

export default function PolicyConfigurationPage() {
  const [policies, setPolicies] = useState([]);
  const [violations, setViolations] = useState([]);
  const [metrics, setMetrics] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedPolicy, setSelectedPolicy] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPolicyForEdit, setSelectedPolicyForEdit] = useState(null);
  const [assignmentsModalPolicyId, setAssignmentsModalPolicyId] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showEnumManagement, setShowEnumManagement] = useState(false);
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loadingStatusId, setLoadingStatusId] = useState(null);
  const [availableCategories, setAvailableCategories] = useState([]);

  // Agents & Groups filter state
  const [agents, setAgents] = useState([]);
  const [groups, setGroups] = useState([]);
  const [filterAgent, setFilterAgent] = useState('all'); // Default to "all"
  const [filterGroup, setFilterGroup] = useState('all'); // Default to "all"

  // External Integration - Temporary dev code
  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handlePolicyToggle
  } = useExternalIntegration();

  const API_BASE_URL = getApiBaseUrl();

  useEffect(() => {
    fetchData();
  }, []);

  // Fetch agents and policy groups lists for dropdowns
  useEffect(() => {
    async function loadFilters() {
      try {
        const [agentsData, groupsData, schemaList] = await Promise.all([
          listAgents(),
          listPolicyGroups('active'),
          fetchSchemaList(),
        ]);
        setAgents(agentsData);
        setGroups(groupsData);
        
        // Extract unique categories from schemas
        const categories = schemaList.map(schema => {
          // Convert schema_name to display name
          const displayName = schema.schema_name
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
          return {
            value: schema.schema_name,
            label: displayName
          };
        });
        setAvailableCategories(categories);
      } catch (err) {
        console.error('Failed to load agents, policy groups, or schemas', err);
      }
    }
    loadFilters();
  }, []);

  useEffect(() => {
    // Keep dashboard stats in sync with local data without needing to re-fetch
    setMetrics((prev) => ({
      ...prev,
      current_stats: {
        ...(prev.current_stats || {}),
        total_policies: policies.length,
        active_policies: policies.filter((p) => p && p.is_active).length,
        critical_policies: policies.filter((p) => p && p.severity === 'critical').length,
        total_violations: violations.length,
      },
    }));
  }, [policies, violations]);

  // Re-fetch policies whenever filter agents or groups change
  useEffect(() => {
    fetchData();
  }, [filterAgent, filterGroup]);

  const getProxyApiBaseUrl = () => getApiBaseUrl();

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      // Only add agentId if a specific agent is selected (not "all")
      if (filterAgent !== 'all') {
        params.append('agentId', filterAgent);
      }
      // Only add groupId if a specific group is selected (not "all")
      if (filterGroup !== 'all') {
        params.append('groupId', filterGroup);
      }
      const policiesUrl = `${API_BASE_URL}/api/v1/policies${params.toString() ? `?${params.toString()}` : ''}`;

      const [policiesRes, violationsRes, metricsRes] = await Promise.all([
        fetch(policiesUrl, {
          headers: { Authorization: 'Bearer admin-token' },
        }),
        fetch(`${API_BASE_URL}/api/v1/policies/violations?limit=100`, {
          headers: { Authorization: 'Bearer admin-token' },
        }),
        fetch(`${API_BASE_URL}/api/v1/metrics`, {
          headers: { Authorization: 'Bearer admin-token' },
        }),
      ]);

      if (policiesRes.ok) {
        const policiesData = await policiesRes.json();
        setPolicies(policiesData.policies || policiesData);
      }
      if (violationsRes.ok) setViolations(await violationsRes.json());
      if (metricsRes.ok) setMetrics(await metricsRes.json());
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (policyId) => {
    if (window.confirm('Are you sure you want to delete this policy?')) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/v1/policies/${policyId}`, {
          method: 'DELETE',
          headers: {
            Authorization: 'Bearer admin-token',
          },
        });

        if (response.ok) {
          fetchData(); // Refresh data after successful deletion
        } else {
          const errorData = await response.json();
          console.error('Failed to delete policy:', errorData);
          alert(`Error: ${errorData.error}`);
        }
      } catch (error) {
        console.error('Error deleting policy:', error);
        alert('An unexpected error occurred while deleting the policy.');
      }
    }
  };

  const handleSaveNewPolicy = async () => {
    try {
      // The EnhancedPolicyModal already performs the API call for both create and update.
      // Simply refresh the table data and close the modal.
      fetchData(); // Refresh data
      setShowCreateModal(false);
      setSelectedPolicyForEdit(null);
    } catch (error) {
      console.error('Error saving policy:', error);
      alert('An unexpected error occurred while saving the policy.');
    }
  };

  const handleUpdatePolicy = async (updatedPolicy) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/policies/${updatedPolicy.policy_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer admin-token',
        },
        body: JSON.stringify(updatedPolicy),
      });

      if (response.ok) {
        setSelectedPolicy(null); // Close modal on success
        fetchData(); // Refresh data
      } else {
        const errorData = await response.json();
        console.error('Failed to update policy:', errorData);
        alert(`Error: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating policy:', error);
      alert('An unexpected error occurred while updating the policy.');
    }
  };

  const handleEditPolicy = (policy) => {
    setSelectedPolicyForEdit(policy);
    setShowCreateModal(true);
  };

  // Original policy update logic - moved to be called by external integration
  const originalHandleToggleStatus = async (policy) => {
    if (!policy) return;

    let updatedDefinition = policy.definition;
    try {
      if (typeof policy.definition === 'string') {
        updatedDefinition = JSON.parse(policy.definition);
      } else {
        updatedDefinition = { ...policy.definition };
      }
      updatedDefinition.enabled = !policy.is_active;
    } catch (error) {
      console.error('Error parsing policy definition:', error);
      updatedDefinition = policy.definition;
    }

    const updatedPolicy = {
      ...policy,
      is_active: !policy.is_active,
      definition: updatedDefinition,
    };
    setLoadingStatusId(policy.policy_id);

    let featureFlag = null;
    try {
      featureFlag = (await import('../featureFlags.json')).default.find(
        (f) => f.policy_id === String(policy.policy_id) || f.policy_name === policy.name
      );
    } catch (e) {
      featureFlag = null;
      console.error('Error loading featureFlag mapping:', e);
    }

    let policyUpdateSuccess = false;
    let featureFlagUpdateSuccess = false;
    let errorMsg = '';

    try {
      // Only send the fields that the API expects for update
      // Note: Omitting groupIds to preserve existing policy group assignments
      const updatePayload = {
        name: policy.name,
        description: policy.description,
        category: policy.category,
        definition: updatedDefinition,
        is_active: !policy.is_active,
        severity: policy.severity,
        applies_to_roles: policy.applies_to_roles,
        policy_type: policy.policy_type
      };

      const response = await fetch(`${API_BASE_URL}/api/v1/policies/${policy.policy_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer admin-token',
        },
        body: JSON.stringify(updatePayload),
      });
      if (response.ok) {
        policyUpdateSuccess = true;
        const updated = await response.json();
        // Merge the response with the original policy to preserve any client-side fields
        const mergedPolicy = {
          ...policy,
          ...updated,
          definition: updated.definition || updatedDefinition,
          is_active: updated.is_active
        };
        setPolicies((prev) => prev.map((p) => (p.policy_id === mergedPolicy.policy_id ? mergedPolicy : p)));
      } else {
        errorMsg = 'Failed to update policy status.';
        console.error(errorMsg);
      }
    } catch (err) {
      errorMsg = 'Error toggling policy status.';
      console.error(errorMsg, err);
    }

    if (
      featureFlag &&
      featureFlag.feature_flag_endpoint !== 'placeholder' &&
      featureFlag.feature_flag_name !== 'placeholder'
    ) {
      try {
        const flagValue = updatedPolicy.is_active ? 'true' : 'false';
        const description = `${policy.name} - ${updatedPolicy.is_active ? 'Enabled' : 'Disabled'}`;
        const flagResponse = await fetch(`${getProxyApiBaseUrl()}${featureFlag.feature_flag_endpoint}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer admin-secret-key-123',
          },
          body: JSON.stringify({ flag_value: flagValue, description }),
        });
        if (flagResponse.ok) {
          featureFlagUpdateSuccess = true;
        } else {
          errorMsg = 'Failed to update feature flag.';
          console.error(errorMsg);
        }
      } catch (err) {
        errorMsg = 'Error updating feature flag.';
        console.error(errorMsg, err);
      }
    } else {
      featureFlagUpdateSuccess = true;
    }

    if (policyUpdateSuccess && featureFlagUpdateSuccess) {
      // success
    } else {
      alert(errorMsg || 'Failed to update policy or feature flag.');
      fetchData();
      console.error('Policy or feature flag update failed:', errorMsg);
    }
    setLoadingStatusId(null);
  };

  // External Integration wrapper - Shows authorization dialog and curl commands
  const handleToggleStatus = (policy) => {
    const newStatus = !policy.is_active;
    
    handlePolicyToggle(
      policy.policy_id,
      newStatus,
      policy.name,
      async () => {
        await originalHandleToggleStatus(policy);
      }
    );
  };

  const filteredPolicies = policies.filter((policy) => {
    if (!policy) return false;

    const matchesSearch =
      policy.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      policy.description?.toLowerCase().includes(searchTerm.toLowerCase());
    // Match by policy_type which corresponds to schema name, or by category for backwards compatibility
    const matchesCategory = filterCategory === 'all' || 
                           policy.policy_type === filterCategory || 
                           policy.category === filterCategory;
    const matchesStatus =
      filterStatus === 'all' ||
      (filterStatus === 'active' && policy.is_active) ||
      (filterStatus === 'inactive' && !policy.is_active);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'content_safety':
        return 'bg-blue-100 text-blue-800';
      case 'data_masking':
        return 'bg-purple-100 text-purple-800';
      case 'access_control':
        return 'bg-green-100 text-green-800';
      case 'medical_privacy':
        return 'bg-pink-100 text-pink-800';
      case 'compliance':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Action handler stubs (kept for feature parity if buttons are reintroduced)
  const [generatingRegoId, setGeneratingRegoId] = useState(null);
  const handleGenerateRego = async (policyId) => {
    setGeneratingRegoId(policyId);
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/generate-rego`, {
        method: 'POST',
        headers: {
          Authorization: 'Bearer admin-token',
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        fetchData();
      } else {
        const errorData = await response.json();
        alert(`Failed to generate Rego: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      alert('An unexpected error occurred while generating Rego.');
      console.error(error);
    } finally {
      setGeneratingRegoId(null);
    }
  };

  const handleUploadRego = (policyId) => {
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/rego`, {
      method: 'GET',
      headers: {
        Authorization: 'Bearer admin-token',
      },
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to fetch generated rego: ${errorText || response.statusText}`);
          return;
        }
        const data = await response.json();
        const regoCode = data.regoCode || data.rego_code || data.code;
        if (!regoCode) {
          alert('No generated rego code found for this policy.');
          return;
        }
        fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/upload-blob`, {
          method: 'POST',
          headers: {
            Authorization: 'Bearer admin-token',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ regoCode }),
        })
          .then(async (uploadResponse) => {
            const result = await uploadResponse.json();
            if (uploadResponse.ok && result.success) {
              alert('Rego uploaded successfully!');
              fetchData();
            } else {
              alert(`Failed to upload rego: ${result.error || 'Unknown error'}`);
            }
          })
          .catch((err) => {
            alert('Error uploading rego: ' + err.message);
          });
      })
      .catch((err) => {
        alert('Error fetching generated rego: ' + err.message);
      });
  };

  const handleDownloadRego = (policyId) => {
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/blob`, {
      method: 'GET',
      headers: {
        Authorization: 'Bearer admin-token',
      },
    })
      .then(async (response) => {
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to download rego: ${errorText || response.statusText}`);
          return;
        }
        const disposition = response.headers.get('Content-Disposition');
        let filename = `policy_${policyId}.rego`;
        if (disposition && disposition.includes('filename=')) {
          filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => {
        alert('Error downloading rego: ' + err.message);
      });
  };

  const handleDeleteRego = (policyId) => {
    if (!window.confirm(`Are you sure you want to delete the rego file for policy ${policyId}?`)) return;
    fetch(`${API_BASE_URL}/api/v1/policies/${policyId}/blob`, {
      method: 'DELETE',
      headers: {
        Authorization: 'Bearer admin-token',
      },
    })
      .then(async (response) => {
        if (response.ok) {
          alert('Rego file deleted successfully!');
          fetchData();
        } else {
          const errorText = await response.text();
          alert(`Failed to delete rego: ${errorText || response.statusText}`);
        }
      })
      .catch((err) => {
        alert('Error deleting rego: ' + err.message);
      });
  };

  if (loading) {
    return (
      <div className="min-h-[50vh] bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading Policy Dashboard...</div>
      </div>
    );
  }

  const handleBundleRego = () => {
    setLoading(true);
    fetch(`${API_BASE_URL}/api/v1/policies/bundle-rego`, {
      method: 'POST',
      headers: {
        Authorization: 'Bearer admin-token',
      },
    })
      .then(async (response) => {
        setLoading(false);
        if (!response.ok) {
          const errorText = await response.text();
          alert(`Failed to bundle rego files: ${errorText || response.statusText}`);
          return;
        }
        const disposition = response.headers.get('Content-Disposition');
        let filename = 'rego_bundle.tar.gz';
        if (disposition && disposition.includes('filename=')) {
          filename = disposition.split('filename=')[1].replace(/"/g, '').trim();
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      })
      .catch((err) => {
        setLoading(false);
        alert('Error bundling rego files: ' + err.message);
      });
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Header actions (page-level) */}
      <div className="flex-shrink-0 p-6 pb-0">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Policy Configuration</h1>
            <p className="text-gray-600 dark:text-gray-300">Manage policies, monitor compliance, and configure enforcement</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-green-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>New Policy</span>
            </button>
          </div>
        </div>
      </div>

      {/* Scrollable content area */}
      <div className="flex-1 min-h-0 p-6 pt-0 flex flex-col">
        {/* Metrics Cards */}
        <div className="flex-shrink-0 grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Total Policies</p>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{metrics.current_stats?.total_policies || policies.length}</p>
            </div>
            <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Shield className="h-5 w-5 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Active Policies</p>
              <p className="text-xl font-bold text-green-600">{metrics.current_stats?.active_policies || policies.filter((p) => p && p.is_active).length}</p>
            </div>
            <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Critical Policies</p>
              <p className="text-xl font-bold text-red-600">{metrics.current_stats?.critical_policies || policies.filter((p) => p.severity === 'critical').length}</p>
            </div>
            <div className="h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Total Violations</p>
              <p className="text-xl font-bold text-orange-600">{metrics.current_stats?.total_violations || violations.length}</p>
            </div>
            <div className="h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Activity className="h-5 w-5 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex-shrink-0 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row flex-wrap gap-3 flex-1 min-w-0">
            <div className="relative flex-1 min-w-[160px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search policies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="flex-1 min-w-[112px] px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Categories</option>
              {availableCategories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="flex-1 min-w-[96px] px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            <select
              value={filterAgent}
              onChange={(e) => setFilterAgent(e.target.value)}
              className="flex-1 min-w-[104px] px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              data-cy="agents-dd"
            >
              <option value="all">All Agents</option>
              {agents.map(a => (
                <option key={a.agent_id || a.id} value={a.agent_id || a.id}>
                  {a.name}
                </option>
              ))}
            </select>

            <select
              value={filterGroup}
              onChange={(e) => setFilterGroup(e.target.value)}
              className="flex-1 min-w-[104px] px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              data-cy="policy-groups-dd"
            >
              <option value="all">All Groups</option>
              {groups.map(g => (
                <option key={g.group_id || g.policy_group_id || g.id} value={g.group_id || g.policy_group_id || g.id}>
                  {g.name}
                </option>
              ))}
            </select>
          </div>

          <div className="text-sm text-gray-600">
            Showing {filteredPolicies.length} of {policies.length} policies
          </div>
        </div>
      </div>

      {/* Policies Table */}
      <div className="flex-1 min-h-0 bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 flex flex-col">
        <div className="flex-1 overflow-auto relative">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0 z-10">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[30%]">Policy Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[15%]">Category</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[10%]">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[10%]">Severity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[20%]">Policy Groups</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-[15%] hidden lg:table-cell">Last Modified</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredPolicies.map((policy) => {
                if (!policy) return null;
                return (
                  <tr key={policy.policy_id} className="hover:bg-gray-50 dark:hover:bg-gray-900">
                    <td className="px-6 py-4">
                      <div className="min-w-0">
                        <button
                          className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline truncate block max-w-xs"
                          onClick={() => setAssignmentsModalPolicyId(policy.policy_id)}
                          title={policy.name || 'Unnamed Policy'}
                        >
                          {policy.name || 'Unnamed Policy'}
                        </button>
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">{policy.description || 'No description'}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(policy.category)}`}>
                        {policy.category.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex justify-center items-center">
                        <ToggleSwitch isLoading={loadingStatusId === policy.policy_id} isActive={policy.is_active} onToggle={() => handleToggleStatus(policy)} />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(policy.severity)}`}>
                        {policy.severity}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {policy.policy_groups && policy.policy_groups.length ? (
                        <div className="flex flex-wrap gap-1 max-w-xs">
                          {policy.policy_groups.slice(0, 2).map((g) => (
                            <span key={g} className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
                              {g}
                            </span>
                          ))}
                          {policy.policy_groups.length > 2 && (
                            <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
                              +{policy.policy_groups.length - 2} more
                            </span>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">
                      {new Date(policy.updated_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button onClick={() => handleEditPolicy(policy)} className="text-green-600 hover:text-green-900 p-1 rounded" title="Edit Policy">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button onClick={() => handleDelete(policy.policy_id)} className="text-red-600 hover:text-red-900 p-1 rounded" title="Delete Policy">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {filteredPolicies.length === 0 && (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-32 w-32 bg-blue-100 dark:bg-blue-900/20 rounded-full opacity-20"></div>
            </div>
            <Shield className="h-16 w-16 text-gray-400 dark:text-gray-500 relative z-10" />
          </div>
          <h3 className="mt-6 text-xl font-medium text-gray-900 dark:text-gray-100">No policies found</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 max-w-sm text-center">
            Get started by creating your first policy to manage and secure your AI agents.
          </p>
          <button 
            onClick={() => setShowCreateModal(true)} 
            className="mt-6 inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Your First Policy
          </button>
        </div>
      )}

      {/* Policy Details Modal */}
      {selectedPolicy && (
        <PolicyModal policy={selectedPolicy} onClose={() => setSelectedPolicy(null)} onSave={handleUpdatePolicy} />
      )}

      {/* Create Policy Modal */}
      {showCreateModal && (
        <EnhancedPolicyModal
          onClose={() => {
            setShowCreateModal(false);
            setSelectedPolicyForEdit(null);
          }}
          onSave={handleSaveNewPolicy}
          existingPolicy={selectedPolicyForEdit}
        />
      )}

      {/* Enum Management Modal */}
      {showEnumManagement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold">Enum Value Management</h2>
              <button onClick={() => setShowEnumManagement(false)} className="text-gray-500 hover:text-gray-700">
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="overflow-y-auto h-full">
              <EnumManagement />
            </div>
          </div>
        </div>
      )}

      {/* Policy Assignments Modal */}
      {assignmentsModalPolicyId && (
        <PolicyAssignmentsModal
          policyId={assignmentsModalPolicyId}
          onClose={() => setAssignmentsModalPolicyId(null)}
        />
      )}

      {/* External Integration Dialog - Temporary dev code */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        operation={dialogState.operation}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
      </div>
    </div>
  );
}

function PolicyModal({ policy, onClose, onSave }) {
  const [editedPolicy, setEditedPolicy] = useState({ ...policy });

  const handleSave = async () => {
    onSave(editedPolicy);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Edit Policy</h2>
        <div className="space-y-4">
          <input
            type="text"
            value={editedPolicy.name}
            onChange={(e) => setEditedPolicy({ ...editedPolicy, name: e.target.value })}
            placeholder="Policy Name"
            className="w-full p-2 border rounded"
          />
          <textarea
            value={editedPolicy.description}
            onChange={(e) => setEditedPolicy({ ...editedPolicy, description: e.target.value })}
            placeholder="Description"
            className="w-full p-2 border rounded"
            rows="3"
          ></textarea>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                value={editedPolicy.category}
                onChange={(e) => setEditedPolicy({ ...editedPolicy, category: e.target.value })}
                className="w-full p-2 border rounded"
              >
                <option value="content_safety">Content Safety</option>
                <option value="data_masking">Data Masking</option>
                <option value="access_control">Access Control</option>
                <option value="medical_privacy">Medical Privacy</option>
                <option value="compliance">Compliance</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
              <select
                value={editedPolicy.severity}
                onChange={(e) => setEditedPolicy({ ...editedPolicy, severity: e.target.value })}
                className="w-full p-2 border rounded"
              >
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Policy Type</label>
              <input type="text" value={editedPolicy.policy_type || 'opa'} readOnly className="w-full p-2 border rounded bg-gray-100" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Policy Definition (JSON)</label>
            <textarea
              value={JSON.stringify(editedPolicy.definition, null, 2)}
              onChange={(e) => setEditedPolicy({ ...editedPolicy, definition: JSON.parse(e.target.value) })}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button onClick={onClose} className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
              Cancel
            </button>
            <button onClick={handleSave} className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}