import React, { useMemo, useState } from 'react';
import { Plus, X, Edit as EditIcon, Trash2 } from 'lucide-react';
import { useListAgentsQuery, useCreateAgentMutation, useUpdateAgentMutation, useDeleteAgentMutation } from '../api/agentsApi.js';
import AgentDetailsModal from '../components/AgentDetailsModal.js';
// External Integration - Temporary dev code
import useExternalIntegration from '../hooks/useExternalIntegration';
import ExternalIntegrationDialog from '../components/ExternalIntegrationDialog';

export default function AgentRegistryPage() {
  const { data: agents = [], isLoading, refetch } = useListAgentsQuery();
  const [open, setOpen] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const defaultForm = {
    name: '',
    description: '',
    agent_type: '',
    endpoint_url: '',
    is_active: true,
    vendor: '',
    department: '',
    risk_score: 0.0,
    status: 'active',
  };
  const [form, setForm] = useState(defaultForm);
  const [search, setSearch] = useState('');
  const [createAgent, { isLoading: saving }] = useCreateAgentMutation();
  const [updateAgent] = useUpdateAgentMutation();
  const [deleteAgent] = useDeleteAgentMutation();
  const [detailsId, setDetailsId] = useState(null);

  // External Integration - Temporary dev code
  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handleAgentToggle
  } = useExternalIntegration();

  function Toggle({ checked, onChange }) {
    return (
      <label className="inline-flex items-center cursor-pointer">
        <input type="checkbox" checked={checked} onChange={onChange} className="sr-only peer" />
        <div className="relative w-10 h-5 rounded-full bg-gray-600 transition-colors peer-checked:bg-green-500 after:content-[''] after:absolute after:top-0.5 after:left-0.5 after:w-4 after:h-4 after:bg-white after:rounded-full after:transition-transform peer-checked:after:translate-x-5" />
      </label>
    );
  }

  // Filter agents based on search
  const filteredAgents = (agents || []).filter((a) => {
    if (!search.trim()) return true;
    const q = search.toLowerCase();
    return (
      (a.name || '').toLowerCase().includes(q) ||
      (a.description || '').toLowerCase().includes(q) ||
      (a.vendor || '').toLowerCase().includes(q) ||
      (a.department || '').toLowerCase().includes(q) ||
      (a.agent_type || '').toLowerCase().includes(q)
    );
  });

  const handleToggleChange = (agent) => {
    const newStatus = !agent.is_active;
    
    const getAgentPolicies = async () => {
      try {
        const response = await fetch(`/api/v1/agents/${agent.agent_id}/role-policies`, {
          headers: { Authorization: 'Bearer admin-token' }
        });
        if (response.ok) {
          const assignments = await response.json();
          return assignments.map(assignment => assignment.policy_id);
        }
        return [];
      } catch (e) {
        console.error('Failed to fetch agent policies:', e);
        return [];
      }
    };
    
    handleAgentToggle(
      agent.agent_id,
      newStatus,
      agent.name,
      async () => {
        try {
          await updateAgent({ id: agent.agent_id, body: { is_active: newStatus } }).unwrap();
        } catch (e) {
          alert(e?.data?.error || 'Failed to update');
        }
      },
      getAgentPolicies
    );
  };

  const handleEdit = (agent) => {
    setForm({
      name: agent.name || '',
      description: agent.description || '',
      agent_type: agent.agent_type || 'policy_engine',
      endpoint_url: agent.endpoint_url || '',
      is_active: !!agent.is_active,
      vendor: agent.vendor || '',
      department: agent.department || '',
      risk_score: agent.risk_score ?? 0,
      status: agent.status || 'active',
    });
    setEditingId(agent.agent_id);
    setOpen(true);
  };

  const handleDelete = async (agent) => {
    if (!window.confirm(`Delete agent ${agent.name}?`)) return;
    try {
      await deleteAgent(agent.agent_id).unwrap();
      refetch();
    } catch (e) {
      alert(e?.data?.error || 'Failed to delete agent');
    }
  };

  return (
    <div className="h-full w-full overflow-hidden">
      <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="flex-shrink-0 p-3 sm:p-5">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100">
                  Agent Registry
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Central hub for all AI agents and applications ({filteredAgents.length} agents)
                </p>
              </div>
              <button
                onClick={() => { setEditingId(null); setForm(defaultForm); setOpen(true); }}
                className="bg-blue-600 text-white px-3 py-1.5 rounded-lg flex items-center space-x-2 hover:bg-blue-700 flex-shrink-0 text-sm"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">New Agent</span>
                <span className="sm:hidden">New</span>
              </button>
            </div>
            
            {/* Search bar */}
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search agents by name, description, vendor, department, or type…"
              className="w-full p-1.5 text-xs rounded bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Scrollable table area */}
        <div className="flex-1 min-h-0 p-3 sm:p-5 pt-0">
          <div className="h-full overflow-auto">
            <table className="min-w-[259px] w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
                <tr>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[58px]">Agent Name</th>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[29px] hidden md:table-cell">Vendor</th>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[29px] hidden lg:table-cell">Department</th>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[29px] hidden sm:table-cell">Purpose</th>
                  <th className="px-1.5 py-2.5 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[38px]">Active</th>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[29px] hidden lg:table-cell">Risk Score</th>
                  <th className="px-1.5 py-2.5 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[29px] hidden xl:table-cell">Last Modified</th>
                  <th className="px-1.5 py-2.5 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[45px]">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                {isLoading ? (
                  <tr>
                    <td colSpan={8} className="p-5 text-center text-gray-500 dark:text-gray-400">Loading…</td>
                  </tr>
                ) : filteredAgents.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="p-5 text-center text-gray-500 dark:text-gray-400">No agents found</td>
                  </tr>
                ) : (
                  filteredAgents.map((agent) => (
                    <tr key={agent.agent_id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-1.5 py-2.5">
                        <div className="min-w-0">
                          <button
                            className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline truncate block w-full text-left"
                            onClick={() => setDetailsId(agent.agent_id)}
                            title={agent.name}
                          >
                            {agent.name}
                          </button>
                          <div className="text-xs text-gray-500 dark:text-gray-400 truncate w-full">
                            {agent.description || '—'}
                          </div>
                        </div>
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 hidden md:table-cell">
                        <div className="truncate">{agent.vendor || '—'}</div>
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 hidden lg:table-cell">
                        <div className="truncate">{agent.department || '—'}</div>
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 hidden sm:table-cell">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          {agent.agent_type || '—'}
                        </span>
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-center">
                        <Toggle
                          checked={!!agent.is_active}
                          onChange={() => handleToggleChange(agent)}
                        />
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 hidden lg:table-cell">
                        <span className={`inline-flex px-1 py-1 text-xs font-semibold rounded ${
                          (agent.risk_score ?? 0) > 0.7 ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          (agent.risk_score ?? 0) > 0.3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        }`}>
                          {agent.risk_score ?? '0.0'}
                        </span>
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400 hidden xl:table-cell">
                        {new Date(agent.updated_at).toLocaleDateString()}
                      </td>
                      <td className="px-1.5 py-2.5 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-1">
                          <button
                            onClick={() => handleEdit(agent)}
                            className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200 p-1 rounded"
                            title="Edit Agent"
                          >
                            <EditIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(agent)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200 p-1 rounded"
                            title="Delete Agent"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add/Edit Agent Modal */}
      {open && (
        <AddAgentModal
          form={form}
          setForm={setForm}
          onClose={() => { setOpen(false); setEditingId(null); setForm(defaultForm); }}
          saving={saving}
          isEdit={Boolean(editingId)}
          onSubmit={async () => {
            // Basic validation
            if (!form.name.trim()) {
              alert('Name is required');
              return;
            }
            try {
              if (editingId) {
                await updateAgent({ id: editingId, body: {
                  name: form.name.trim(),
                  description: form.description || null,
                  agent_type: form.agent_type || 'policy_engine',
                  endpoint_url: form.endpoint_url || null,
                  is_active: Boolean(form.is_active),
                  vendor: form.vendor || null,
                  department: form.department || null,
                  risk_score: Number(form.risk_score) || 0.0,
                  status: form.status || 'active',
                }}).unwrap();
              } else {
                await createAgent({
                  name: form.name.trim(),
                  description: form.description || null,
                  agent_type: form.agent_type || 'policy_engine',
                  endpoint_url: form.endpoint_url || null,
                  is_active: Boolean(form.is_active),
                  vendor: form.vendor || null,
                  department: form.department || null,
                  risk_score: Number(form.risk_score) || 0.0,
                  status: form.status || 'active',
                }).unwrap();
              }
              setOpen(false);
              setForm(defaultForm);
              setEditingId(null);
              refetch();
            } catch (e) {
              alert(e?.data?.error || e.message || 'Failed to save agent');
            }
          }}
        />
      )}

      {/* Agent Details Modal */}
      {detailsId && (
        <AgentDetailsModal agentId={detailsId} onClose={() => setDetailsId(null)} />
      )}

      {/* External Integration Dialog */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        operation={dialogState.operation}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </div>
  );
}

function AddAgentModal({ form, setForm, onClose, onSubmit, saving, isEdit }) {
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl ring-1 ring-black/10 dark:ring-white/10 w-full max-w-[calc(100vw-2rem)] sm:max-w-2xl lg:max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-start justify-between p-4 sm:p-5 border-b dark:border-gray-700">
          <div className="space-y-1">
            <div className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100">
              {isEdit ? 'Edit Agent' : 'New Agent'}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {isEdit ? 'Update the selected agent' : 'Create a new agent'}
            </div>
          </div>
          <button 
            onClick={onClose} 
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-700 flex-shrink-0"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="px-4 sm:px-5 py-4 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">
                Name<span className="text-red-500">*</span>
              </label>
              <input 
                value={form.name} 
                onChange={(e) => setForm({ ...form, name: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Agent Type</label>
              <select 
                value={form.agent_type} 
                onChange={(e) => setForm({ ...form, agent_type: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="" disabled>Select type…</option>
                <option value="policy_engine">policy_engine</option>
                <option value="chatbot">chatbot</option>
                <option value="data_pipeline">data_pipeline</option>
                <option value="custom">custom</option>
              </select>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Description</label>
              <textarea 
                value={form.description} 
                onChange={(e) => setForm({ ...form, description: e.target.value })} 
                rows={3} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Endpoint URL</label>
              <input 
                value={form.endpoint_url} 
                onChange={(e) => setForm({ ...form, endpoint_url: e.target.value })} 
                placeholder="https://..." 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Active</label>
              <select 
                value={form.is_active ? 'true' : 'false'} 
                onChange={(e) => setForm({ ...form, is_active: e.target.value === 'true' })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="true">Yes</option>
                <option value="false">No</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Vendor</label>
              <input 
                value={form.vendor} 
                onChange={(e) => setForm({ ...form, vendor: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Department</label>
              <input 
                value={form.department} 
                onChange={(e) => setForm({ ...form, department: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Risk Score</label>
              <input 
                type="number" 
                min="0" 
                max="10" 
                step="0.1" 
                value={form.risk_score} 
                onChange={(e) => setForm({ ...form, risk_score: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Status</label>
              <select 
                value={form.status} 
                onChange={(e) => setForm({ ...form, status: e.target.value })} 
                className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 px-3 py-2 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="active">active</option>
                <option value="pending">pending</option>
                <option value="deprecated">deprecated</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-end gap-2 p-4 sm:p-5 border-t dark:border-gray-700">
          <button 
            onClick={onClose} 
            className="px-3 sm:px-4 py-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-100 text-sm hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button 
            onClick={onSubmit} 
            disabled={saving} 
            className="px-3 sm:px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60 text-sm"
          >
            {saving ? (isEdit ? 'Saving…' : 'Creating…') : (isEdit ? 'Save Changes' : 'Create Agent')}
          </button>
        </div>
      </div>
    </div>
  );
}