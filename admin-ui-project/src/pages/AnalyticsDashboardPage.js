import React, { useEffect, useState } from 'react';
import { getApiBaseUrl } from '../api/getApiBase.js';

export default function AnalyticsDashboardPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [groupStats, setGroupStats] = useState({ policies_per_group: [], agents_per_group: [] });
  const [orphanedPolicies, setOrphanedPolicies] = useState([]);

  useEffect(() => {
    (async () => {
      try {
        const base = getApiBaseUrl();
        const [groupRes, orphanRes] = await Promise.all([
          fetch(`${base}/api/v1/metrics/group-stats`, {
            headers: { Authorization: 'Bearer admin-token' }
          }),
          fetch(`${base}/api/v1/metrics/orphaned-policies`, {
            headers: { Authorization: 'Bearer admin-token' }
          })
        ]);
        if (!groupRes.ok) throw new Error('Failed to load group stats');
        if (!orphanRes.ok) throw new Error('Failed to load orphaned policies');
        const groupData = await groupRes.json();
        const orphanData = await orphanRes.json();
        setGroupStats(groupData);
        setOrphanedPolicies(orphanData.orphaned_policies || []);
      } catch (err) {
        console.error(err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  if (loading) return <div className="p-8 text-gray-500">Loading analytics…</div>;
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>;

  return (
    <div className="p-8 space-y-12">
      <h1 className="text-2xl font-semibold mb-4">Policy Analytics Dashboard</h1>

      {/* Policies per Group */}
      <section>
        <h2 className="text-xl font-medium mb-2">Policies per Group</h2>
        <div className="overflow-x-auto shadow rounded">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group Name</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"># Policies</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {groupStats.policies_per_group.map((g) => (
                <tr key={g.id}>
                  <td className="px-6 py-4 whitespace-nowrap">{g.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{g.policy_count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>

      {/* Agents per Group */}
      <section>
        <h2 className="text-xl font-medium mb-2">Agents per Group</h2>
        <div className="overflow-x-auto shadow rounded">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group Name</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"># Agents</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {groupStats.agents_per_group.map((g) => (
                <tr key={g.id}>
                  <td className="px-6 py-4 whitespace-nowrap">{g.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{g.agent_count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>

      {/* Orphaned Policies */}
      <section>
        <h2 className="text-xl font-medium mb-2">Orphaned Policies</h2>
        {orphanedPolicies.length === 0 ? (
          <p className="text-gray-500">No orphaned policies 🎉</p>
        ) : (
          <div className="overflow-x-auto shadow rounded">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Policy ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Policy Name</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {orphanedPolicies.map((p) => (
                  <tr key={p.id}>
                    <td className="px-6 py-4 whitespace-nowrap">{p.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{p.name}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </section>
    </div>
  );
}