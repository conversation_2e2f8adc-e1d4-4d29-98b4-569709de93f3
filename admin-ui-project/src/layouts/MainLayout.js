import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { Shield, Users, Building2, Menu, ChevronDown, Home, Activity, TestTube, BarChart3, Target, Settings, HelpCircle, BookOpen, GraduationCap } from 'lucide-react';
import {
  CircleStackIcon,
  BeakerIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  ShieldExclamationIcon
} from '@heroicons/react/24/outline';
import { isPolicyGroupFeatureEnabled } from '../utils/featureFlags.js';

export default function MainLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [govOpen, setGovOpen] = useState(true);
  const [agentOpen, setAgentOpen] = useState(true);
  const [testingOpen, setTestingOpen] = useState(true);
  const [monitoringOpen, setMonitoringOpen] = useState(true);
  const [cisoOpen, setCisoOpen] = useState(true);
  const [adminOpen, setAdminOpen] = useState(true);
  const [supportOpen, setSupportOpen] = useState(true);
  const location = useLocation();

  // breadcrumb mapping for all sections
  const breadcrumb = () => {
    // Existing pages (preserve functionality)
    if (location.pathname.startsWith('/agent-registry')) return 'Agent Registry';
    if (location.pathname.startsWith('/policy-groups')) return 'Policy Groups';
    
    // Testing & Evaluation pages
    if (location.pathname.startsWith('/datasets')) return 'Dataset Management';
    if (location.pathname === '/evaluations') return 'Evaluations Library';
    if (location.pathname === '/experiments') return 'Experiments & Test Runs';
    if (location.pathname.startsWith('/evaluation-results')) return 'Evaluation Results';
    if (location.pathname === '/red-team') return 'Red Team Console';
    
    // Runtime Monitoring & Observability pages
    if (location.pathname === '/monitoring/live') return 'Live Agent Monitor';
    if (location.pathname === '/monitoring/performance') return 'Performance Analytics';
    if (location.pathname === '/monitoring/usage') return 'Usage & Adoption Tracking';
    if (location.pathname === '/monitoring/audit') return 'Audit Trail Viewer';
    
    // CISO Executive Dashboard pages
    if (location.pathname === '/ciso/risk') return 'Enterprise Risk Overview';
    if (location.pathname === '/ciso/threats') return 'Threat Intelligence Center';
    
    // Administration pages
    if (location.pathname === '/admin/users') return 'User Management';
    if (location.pathname === '/admin/security') return 'Security Settings';
    if (location.pathname === '/admin/system') return 'System Configuration';
    if (location.pathname === '/admin/audit') return 'Admin Audit Logs';
    
    // Support & Help pages
    if (location.pathname === '/support/center') return 'Support Center';
    if (location.pathname === '/support/documentation') return 'Documentation Center';
    if (location.pathname === '/support/training') return 'Training Materials Hub';
    
    // Default fallback for existing functionality
    return 'Policy Configuration';
  };

  const getTopLevelCrumb = () => {
    // Existing pages (preserve functionality)
    if (location.pathname.startsWith('/agent-registry')) return 'AGENT MANAGEMENT';
    if (location.pathname.startsWith('/policy-groups')) return 'Governance & Policies';
    
    // New sections
    if (location.pathname.startsWith('/datasets') || location.pathname === '/evaluations' || location.pathname === '/experiments' || location.pathname.startsWith('/evaluation-results') || location.pathname === '/red-team') return 'TESTING & EVALUATION';
    if (location.pathname.startsWith('/monitoring/')) return 'RUNTIME MONITORING & OBSERVABILITY';
    if (location.pathname.startsWith('/ciso/')) return 'CISO EXECUTIVE DASHBOARD';
    if (location.pathname.startsWith('/admin/')) return 'ADMINISTRATION';
    if (location.pathname.startsWith('/support/')) return 'SUPPORT & HELP';
    
    // Default fallback for existing functionality
    return 'Governance & Policies';
  };

  const topLevelCrumb = getTopLevelCrumb();

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex overflow-hidden">
      {/* Sidebar */}
      <div className={`transition-all duration-200 ${sidebarOpen ? 'w-72' : 'w-16'} bg-white dark:bg-gray-800 shadow-lg h-screen border-r dark:border-gray-700 flex flex-col`}>
        {/* Sidebar brand + burger */}
        <div className={`flex items-center ${sidebarOpen ? 'justify-start gap-3 px-4' : 'justify-center px-2'} py-4 border-b dark:border-gray-700`}>
          <button
            aria-label={sidebarOpen ? 'Collapse sidebar' : 'Expand sidebar'}
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
          >
            <Menu className="h-5 w-5 text-gray-700 dark:text-gray-200" />
          </button>
          {sidebarOpen && (
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <div className="whitespace-nowrap">
                <div className="text-base font-semibold text-gray-900 dark:text-gray-100">Vitea Governance</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Enterprise Platform</div>
              </div>
            </div>
          )}
        </div>

        <nav
          className={`${sidebarOpen ? 'p-4' : 'p-2'} space-y-3 flex-1 overflow-y-auto overflow-x-hidden`}
        >
          {/* Agent Management */}
          <button
            onClick={() => setAgentOpen(!agentOpen)}
            className={`w-full py-2.5 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none transition-colors ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-3' : ''}`}>
              <Building2 className="h-5 w-5 text-purple-500" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Agent Management</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${agentOpen ? '' : '-rotate-90'}`} />}
          </button>
          {agentOpen && (
            <div className={`${sidebarOpen ? 'ml-8' : 'ml-0'} space-y-1 mt-1`}>
              <Link to="/agent-registry" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <Activity className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Agent Registry</span>}
              </Link>
              <div className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-400 dark:text-gray-500 cursor-not-allowed rounded-lg`}>
                <Activity className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Agent Analytics</span>}
              </div>
            </div>
          )}

          {/* Governance & Policies */}
          <button
            onClick={() => setGovOpen(!govOpen)}
            className={`w-full py-2.5 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none transition-colors ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-3' : ''}`}>
              <Shield className="h-5 w-5 text-blue-500" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Governance & Policies</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${govOpen ? '' : '-rotate-90'}`} />}
          </button>
          {govOpen && (
            <div className={`${sidebarOpen ? 'ml-8' : 'ml-0'} space-y-1 mt-1`}>
              <Link to="/policy-configuration" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <Shield className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Policy Configuration</span>}
              </Link>
              {isPolicyGroupFeatureEnabled() && (
                <Link to="/policy-groups" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                  <Users className="h-4 w-4 flex-shrink-0" />
                  {sidebarOpen && <span className="whitespace-nowrap">Policy Groups</span>}
                </Link>
              )}
            </div>
          )}

          {/* Testing & Evaluation */}
          <button
            onClick={() => setTestingOpen(!testingOpen)}
            className={`w-full py-2.5 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none transition-colors ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-3' : ''}`}>
              <BeakerIcon className="h-5 w-5 text-green-500" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Testing & Evaluation</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${testingOpen ? '' : '-rotate-90'}`} />}
          </button>
          {testingOpen && (
            <div className={`${sidebarOpen ? 'ml-8' : 'ml-0'} space-y-1 mt-1`}>
              <Link to="/datasets" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <CircleStackIcon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Dataset Management</span>}
              </Link>
              <Link to="/evaluations" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <ClipboardDocumentListIcon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Evaluations Library</span>}
              </Link>
              <Link to="/experiments" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <BeakerIcon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Experiments</span>}
              </Link>
              <Link to="/evaluation-results" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <ChartBarIcon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Evaluation Results</span>}
              </Link>
              <Link to="/red-team" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <ShieldExclamationIcon className="h-4 w-4 flex-shrink-0" />
                {sidebarOpen && <span className="whitespace-nowrap">Red Team Console</span>}
              </Link>
            </div>
          )}

          {/* Runtime Monitoring & Observability */}
          <button
            onClick={() => setMonitoringOpen(!monitoringOpen)}
            className={`w-full py-2.5 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none transition-colors ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-3' : ''}`}>
              <BarChart3 className="h-5 w-5 text-orange-500" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Runtime Monitoring</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${monitoringOpen ? '' : '-rotate-90'}`} />}
          </button>
          {monitoringOpen && (
            <div className={`${sidebarOpen ? 'ml-8' : 'ml-0'} space-y-1 mt-1`}>
              <Link to="/monitoring/live" className={`${sidebarOpen ? 'px-3 gap-3' : 'p-2 justify-center'} py-2.5 flex items-center text-gray-600 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-gray-900 dark:hover:text-gray-100 rounded-lg transition-colors`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Live Agent Monitor</span>}
              </Link>
              <Link to="/monitoring/performance" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Performance Analytics</span>}
              </Link>
              <Link to="/monitoring/usage" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Usage & Adoption Tracking</span>}
              </Link>
              <Link to="/monitoring/audit" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Audit Trail Viewer</span>}
              </Link>
            </div>
          )}

          {/* CISO Executive Dashboard */}
          <button
            onClick={() => setCisoOpen(!cisoOpen)}
            className={`w-full py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-2' : ''}`}>
              <Target className="h-4 w-4" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">CISO Executive Dashboard</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${cisoOpen ? '' : '-rotate-90'}`} />}
          </button>
          {cisoOpen && (
            <div className={`${sidebarOpen ? 'ml-6' : 'ml-0'} space-y-1`}>
              <Link to="/ciso/risk" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Enterprise Risk Overview</span>}
              </Link>
              <Link to="/ciso/threats" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Threat Intelligence Center</span>}
              </Link>
            </div>
          )}

          {/* Administration */}
          <button
            onClick={() => setAdminOpen(!adminOpen)}
            className={`w-full py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-2' : ''}`}>
              <Settings className="h-4 w-4" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Administration</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${adminOpen ? '' : '-rotate-90'}`} />}
          </button>
          {adminOpen && (
            <div className={`${sidebarOpen ? 'ml-6' : 'ml-0'} space-y-1`}>
              <Link to="/admin/users" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">User Management</span>}
              </Link>
              <Link to="/admin/security" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Security Settings</span>}
              </Link>
              <Link to="/admin/system" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">System Configuration</span>}
              </Link>
              <Link to="/admin/audit" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <Activity className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Audit Logs</span>}
              </Link>
            </div>
          )}

          {/* Support & Help */}
          <button
            onClick={() => setSupportOpen(!supportOpen)}
            className={`w-full py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg focus:outline-none ${sidebarOpen ? 'px-3 justify-between' : 'justify-center'}`}
          >
            <span className={`flex items-center ${sidebarOpen ? 'space-x-2' : ''}`}>
              <Building2 className="h-4 w-4" />
              {sidebarOpen && <span className="font-medium whitespace-nowrap">Support & Help</span>}
            </span>
            {sidebarOpen && <ChevronDown className={`h-4 w-4 transform transition-transform ${supportOpen ? '' : '-rotate-90'}`} />}
          </button>
          {supportOpen && (
            <div className={`${sidebarOpen ? 'ml-6' : 'ml-0'} space-y-1`}>
              <Link to="/support/center" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <HelpCircle className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Support Center</span>}
              </Link>
              <Link to="/support/documentation" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <BookOpen className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Documentation Center</span>}
              </Link>
              <Link to="/support/training" className={`${sidebarOpen ? 'px-3 space-x-2' : 'p-2 justify-center'} py-2 flex items-center text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg`}>
                <GraduationCap className="h-4 w-4" />
                {sidebarOpen && <span className="whitespace-nowrap">Training Materials</span>}
              </Link>
            </div>
          )}
        </nav>
      </div>

      {/* Main area */}
      <div className="flex-1 flex flex-col">
        {/* Header with breadcrumbs */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700">
          <div className="px-6 py-3 flex items-center text-sm text-gray-600 dark:text-gray-400 space-x-2">
            <Link to="/" className="hover:text-blue-600"><Home size={14} /></Link>
            <span className="select-none">›</span>
            <span className="uppercase tracking-wide text-gray-700 dark:text-gray-300 text-xs font-semibold">{topLevelCrumb}</span>
            <span className="select-none">›</span>
            <span className="font-medium text-gray-800 dark:text-gray-200">{breadcrumb()}</span>
          </div>
        </header>

        {/* Page content injected here */}
        <div className="flex-1 min-h-0 overflow-hidden bg-gray-50 dark:bg-gray-900">
          <Outlet />
        </div>
      </div>
    </div>
  );
}