import React, { useState } from 'react';
import externalIntegrationService from '../services/externalIntegrationService';

/**
 * Custom hook for handling external system integrations with confirmation dialogs
 * This is temporary development code for rapid testing
 */
const useExternalIntegration = () => {
  const [dialogState, setDialogState] = useState({
    open: false,
    title: '',
    message: '',
    operation: null,
    isLoading: false,
    integrationResult: null
  });



  const closeDialog = () => {
    setDialogState(prev => ({ ...prev, open: false }));
    // Reset state after close animation
    setTimeout(() => {
      setDialogState({
        open: false,
        title: '',
        message: '',
        operation: null,
        isLoading: false,
        integrationResult: null
      });
    }, 300);
  };

  /**
   * Handle global policy toggle
   * @param {string} policyId - Policy UUID
   * @param {boolean} isActive - New active status
   * @param {string} policyName - Policy display name
   * @param {Function} onSuccess - Callback after successful local update
   */
  const handlePolicyToggle = (policyId, isActive, policyName, onSuccess) => {

    setDialogState({
      open: true,
      title: 'Global Policy Update',
      message: `${isActive ? 'Activate' : 'Deactivate'} "${policyName}" globally? This will affect ALL agents with this policy.`,
      operation: async () => {
        try {
          setDialogState(prev => ({ ...prev, isLoading: true }));
          
          // First update local system
          if (onSuccess) {
            await onSuccess();
          }
          
          // Then notify external system
          const result = await externalIntegrationService.updatePolicyGlobally(policyId, isActive);
          
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: result
          }));
          
        } catch (error) {
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: {
              success: false,
              error: error.message
            }
          }));
        }
      },
      isLoading: false,
      integrationResult: null
    });
  };

  /**
   * Handle agent policy deletion
   * @param {string} agentId - Agent UUID
   * @param {string} policyId - Policy UUID to delete
   * @param {string} agentName - Agent display name
   * @param {string} policyName - Policy display name
   * @param {Function} onSuccess - Callback after successful local update
   * @param {Function} getRemainingPolicies - Function to get remaining policy IDs after deletion
   */
  const handleAgentPolicyDelete = (agentId, policyId, agentName, policyName, onSuccess, getRemainingPolicies) => {

    setDialogState({
      open: true,
      title: 'Remove Policy Assignment',
      message: `Remove "${policyName}" from "${agentName}"? This will deactivate the policy for this agent only.`,
      operation: async () => {
        try {
          setDialogState(prev => ({ ...prev, isLoading: true }));
          
          // First update local system
          if (onSuccess) {
            await onSuccess();
          }
          
          // Get remaining policies after deletion
          const remainingPolicyIds = getRemainingPolicies ? await getRemainingPolicies() : [];
          
          // Then notify external system
          const result = await externalIntegrationService.handleAgentPolicyDeletion(agentId, remainingPolicyIds);
          
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: result
          }));
          
        } catch (error) {
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: {
              success: false,
              error: error.message
            }
          }));
        }
      },
      isLoading: false,
      integrationResult: null
    });
  };

  /**
   * Handle agent active status toggle
   * @param {string} agentId - Agent UUID
   * @param {boolean} isActive - New active status
   * @param {string} agentName - Agent display name
   * @param {Function} onSuccess - Callback after successful local update
   * @param {Function} getAgentPolicies - Function to get current agent policy IDs
   */
  const handleAgentToggle = (agentId, isActive, agentName, onSuccess, getAgentPolicies) => {

    setDialogState({
      open: true,
      title: 'Agent Status Update',
      message: `${isActive ? 'Activate' : 'Deactivate'} "${agentName}"? This will ${isActive ? 'enable' : 'disable'} all policies for this agent.`,
      operation: async () => {
        try {
          setDialogState(prev => ({ ...prev, isLoading: true }));
          
          // First update local system
          if (onSuccess) {
            await onSuccess();
          }
          
          // Get current agent policies
          const assignedPolicyIds = getAgentPolicies ? await getAgentPolicies() : [];
          
          // Then notify external system
          const result = await externalIntegrationService.handleAgentToggle(agentId, isActive, assignedPolicyIds);
          
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: result
          }));
          
        } catch (error) {
          setDialogState(prev => ({
            ...prev,
            isLoading: false,
            integrationResult: {
              success: false,
              error: error.message
            }
          }));
        }
      },
      isLoading: false,
      integrationResult: null
    });
  };

  const confirmOperation = () => {
    if (dialogState.operation) {
      dialogState.operation();
    }
  };

  return {
    dialogState,
    closeDialog,
    confirmOperation,
    handlePolicyToggle,
    handleAgentPolicyDelete,
    handleAgentToggle
  };
};

export default useExternalIntegration;