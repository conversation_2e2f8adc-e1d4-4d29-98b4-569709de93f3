import React, { useState, useEffect } from 'react';
import { ChevronDown, AlertCircle, CheckCircle, XCircle, Loader, Terminal } from 'lucide-react';
import { externalSystemConfig } from '../config/externalIntegration';
import '../styles/ExternalIntegration.css';

const ExternalIntegrationDialog = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  operation,
  isLoading = false,
  integrationResult = null
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // Prevent background scrolling when modal is open
  useEffect(() => {
    if (open) {
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [open]);

  const handleConfirm = () => {
    setShowDetails(true);
    onConfirm();
  };

  const getStatusIcon = (status) => {
    if (status >= 200 && status < 300) return <CheckCircle className="w-4 h-4 text-green-500" />;
    if (status >= 400 && status < 500) return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    if (status >= 500) return <XCircle className="w-4 h-4 text-red-500" />;
    return <AlertCircle className="w-4 h-4 text-gray-500" />;
  };

  const formatJson = (obj) => {
    return JSON.stringify(obj, null, 2);
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            {externalSystemConfig.debugMode && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium">
                DEV MODE
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          <p className="text-gray-700 mb-4">{message}</p>

          {!integrationResult && !isLoading && (
            <div className="flex items-center gap-2 p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
              <AlertCircle className="w-5 h-5 text-blue-500" />
              <div className="text-blue-700">
                <p>This will trigger an external system integration call.</p>
                {externalSystemConfig.debugMode && (
                  <p className="text-sm mt-1">The curl command and response will be shown below for debugging.</p>
                )}
              </div>
            </div>
          )}

          {isLoading && (
            <div className="flex items-center gap-3 p-4 bg-gray-50 border border-gray-200 rounded-lg mb-4">
              <Loader className="w-5 h-5 text-blue-500 animate-spin" />
              <span className="text-gray-700">Sending request to external system...</span>
            </div>
          )}

          {integrationResult && (
            <div className="space-y-4">
              {/* Status Alert */}
              <div className={`flex items-center gap-2 p-4 rounded-lg border ${
                integrationResult.success
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}>
                {integrationResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                <span className={integrationResult.success ? 'text-green-700' : 'text-red-700'}>
                  {integrationResult.success 
                    ? `✅ External system updated successfully (${integrationResult.status})`
                    : `❌ External system error: ${integrationResult.error || integrationResult.statusText}`
                  }
                </span>
              </div>

              {/* Debug Information */}
              {externalSystemConfig.debugMode && (
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setShowDetails(!showDetails)}
                    className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <Terminal className="w-5 h-5 text-gray-600" />
                      <span className="font-medium text-gray-900">🔧 Debug Information</span>
                    </div>
                    <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform ${
                      showDetails ? 'rotate-180' : ''
                    }`} />
                  </button>

                  {showDetails && (
                    <div className="p-4 space-y-4 border-t border-gray-200">
                      {/* Request Payload */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">📤 Request Payload:</h4>
                        <div className="bg-gray-100 rounded-lg p-3 overflow-auto">
                          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                            {formatJson(integrationResult.payload)}
                          </pre>
                        </div>
                      </div>

                      {/* Curl Command */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">💻 Curl Command:</h4>
                        <div className="bg-black rounded-lg p-3 overflow-auto">
                          <pre className="text-sm text-green-400 whitespace-pre-wrap">
                            {integrationResult.curlCommand}
                          </pre>
                        </div>
                      </div>

                      {/* Response */}
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium text-gray-900">📥 Response:</h4>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(integrationResult.status)}
                            <span className="text-sm text-gray-600">
                              {integrationResult.status} {integrationResult.statusText}
                            </span>
                          </div>
                        </div>
                        <div className="bg-gray-100 rounded-lg p-3 overflow-auto">
                          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                            {integrationResult.data 
                              ? formatJson(integrationResult.data)
                              : 'No response data'
                            }
                          </pre>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {integrationResult ? 'Close' : 'Cancel'}
          </button>
          {!integrationResult && !isLoading && (
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Confirm & Send to External System
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExternalIntegrationDialog;