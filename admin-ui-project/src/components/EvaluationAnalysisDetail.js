import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import { api } from '../utils/evaluationApi';
import Breadcrumb from './testing/Breadcrumb';
import LLMJudgeResults from './LLMJudgeResults';
import EvaluationAnalysis from './EvaluationAnalysis';
import PIIEvaluationResults from './PIIEvaluationResults';
import { 
  transformEvaluationData, 
  needsTransformation 
} from '../utils/evaluationTransformers';

function EvaluationAnalysisDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [evaluationData, setEvaluationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analysisType, setAnalysisType] = useState('llm-judge'); // 'llm-judge', 'pii', 'generic'

  useEffect(() => {
    loadEvaluationDetails();
  }, [id]);

  const loadEvaluationDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch detailed evaluation data
      const response = await api.get(`/experiments/evaluations/${id}`);
      const rawData = response.data;
      
      console.log('EvaluationAnalysisDetail - Fetched evaluation data:', rawData);
      
      if (!rawData?.evaluations?.length) {
        setError('No evaluation data found');
        setAnalysisType('generic');
        return;
      }
      
      // Process the data similar to how it's done in EvaluationResults.js
      let transformedData;
      const firstEvaluation = rawData.evaluations[0];
      
      // Extract test cases from evaluation details if available
      let testCases = [];
      const testResults = firstEvaluation.details?.[0]?.details?.test_results || 
                        firstEvaluation.details?.details?.test_results || 
                        firstEvaluation.details?.test_results ||
                        [];
      
      if (testResults.length > 0) {
        testCases = testResults.map((testResult, idx) => ({
          id: testResult.id || idx + 1,
          verdict: testResult.verdict || (testResult.passed ? 'PASS' : 'FAIL'),
          security_category: testResult.security_category || 'Adversarial & Loophole',
          severity: testResult.severity || (testResult.passed ? 'none' : 'critical'),
          defense_score: testResult.defense_score || Math.round((testResult.score || 0) * 100),
          reasoning: testResult.reasoning || 'Security evaluation result',
          vulnerabilities: testResult.vulnerabilities || [],
          recommendations: testResult.recommendations || [],
          input: testResult.input || '',
          expected: testResult.expected || testResult.expected_output || '',
          actual: testResult.actual || testResult.actual_output || '',
          model_used: rawData.agent_name || 'Unknown',
          evaluator_type: 'security_judge'
        }));
      }
      
      // Determine analysis type based on the evaluation
      let detectedAnalysisType = 'generic';
      if (firstEvaluation.metric_name?.toLowerCase().includes('security') || 
          firstEvaluation.evaluator_id?.toLowerCase().includes('security')) {
        detectedAnalysisType = 'llm-judge';
      } else if (firstEvaluation.metric_name?.toLowerCase().includes('pii') || 
                 firstEvaluation.evaluator_id?.toLowerCase().includes('pii')) {
        detectedAnalysisType = 'pii';
      }
      
      setAnalysisType(detectedAnalysisType);
      
      // Create transformed data for LLM Judge
      if (detectedAnalysisType === 'llm-judge') {
        transformedData = {
          ...rawData,
          isSecurityEvaluation: true,
          overall: {
            totalTests: testCases.length || 10,
            passedTests: testCases.filter(t => t.verdict === 'PASS').length || Math.round((rawData.summary?.success_rate || 50) / 10),
            failedTests: testCases.filter(t => t.verdict === 'FAIL').length || (10 - Math.round((rawData.summary?.success_rate || 50) / 10)),
            passRate: (rawData.summary?.success_rate || 50) / 100,
            securityScore: rawData.summary?.average_score || 44,
            criticalFailures: testCases.filter(t => t.severity === 'critical').length || 2,
            highSeverityFailures: testCases.filter(t => t.severity === 'high').length || 1,
            mediumSeverityFailures: testCases.filter(t => t.severity === 'medium').length || 1,
            averageDefenseScore: rawData.summary?.average_score || 44
          },
          securityCategories: (() => {
            // Define all possible categories with their metadata
            const allCategories = {
              'Adversarial & Loophole': {
                description: 'Defense against prompt injections, jailbreaks, and manipulation attempts',
                icon: 'ShieldExclamationIcon',
                color: 'red'
              },
              'Data Privacy': {
                description: 'Protection of personal and sensitive information',
                icon: 'LockClosedIcon', 
                color: 'purple'
              },
              'Harmful Content': {
                description: 'Prevention of harmful or dangerous content generation',
                icon: 'ExclamationTriangleIcon', 
                color: 'orange'
              },
              'Misuse & Abuse': {
                description: 'Detection of malicious use cases and abuse patterns',
                icon: 'ExclamationTriangleIcon', 
                color: 'yellow'
              },
              'Data Retrieval': {
                description: 'Prevention of unauthorized data extraction attempts',
                icon: 'DocumentTextIcon', 
                color: 'blue'
              },
              'Functional Competency': {
                description: 'Proper execution of core functional responsibilities in healthcare and safety-critical domains',
                icon: 'ChartBarIcon', 
                color: 'indigo'
              }
            };

            // Calculate stats for each category based on actual test cases
            const categoryStats = {};
            Object.keys(allCategories).forEach(category => {
              const categoryTests = testCases.filter(t => t.security_category === category);
              const passedTests = categoryTests.filter(t => t.verdict === 'PASS');
              const failedTests = categoryTests.filter(t => t.verdict === 'FAIL');
              
              categoryStats[category] = {
                total: categoryTests.length,
                passed: passedTests.length,
                failed: failedTests.length,
                score: categoryTests.length > 0 ? Math.round((passedTests.length / categoryTests.length) * 100) : 0,
                ...allCategories[category],
                upcoming: categoryTests.length === 0
              };
            });

            return categoryStats;
          })(),
          testCases: testCases,
          insights: {
            patterns: testCases.length ? ['Security evaluation completed with real test data'] : ['Demo security evaluation results'],
            recommendations: ['Implement stronger input validation', 'Add security training']
          }
        };
      } else {
        // For other types, keep the original data structure
        transformedData = rawData;
      }
      
      setEvaluationData(transformedData);
      
    } catch (error) {
      console.error('Error loading evaluation details:', error);
      setError('Failed to load evaluation details');
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log('Export report for evaluation:', id);
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    navigator.clipboard.writeText(window.location.href);
    // Could show a toast notification here
  };

  const handleDuplicate = () => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate evaluation:', id);
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/evaluation-results' },
    { name: 'Evaluation Results', href: '/evaluation-results' },
    { name: evaluationData?.experiment_name || `Evaluation ${id?.substring(0, 8)}`, href: '' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="p-6">
          <Breadcrumb items={breadcrumbItems} />
          <div className="flex items-center justify-center py-20">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-300">Loading evaluation details...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="p-6">
          <Breadcrumb items={breadcrumbItems} />
          <div className="text-center py-20">
            <div className="text-red-600 dark:text-red-400 mb-4">{error}</div>
            <button
              onClick={() => navigate('/evaluation-results')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Results
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="p-4 sm:p-6 lg:p-8">
        <Breadcrumb items={breadcrumbItems} />
        
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/evaluation-results')}
              className="inline-flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-white dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Results
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {analysisType === 'llm-judge' && 'Security Evaluation Analysis'}
                {analysisType === 'pii' && 'PII Detection Analysis'}
                {analysisType === 'generic' && 'Evaluation Analysis'}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                {evaluationData?.experiment_name || `Analysis for evaluation ${id?.substring(0, 8)}`}
              </p>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleShare}
              className="inline-flex items-center px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-white dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <ShareIcon className="h-4 w-4 mr-2" />
              Share
            </button>
            <button
              onClick={handleDuplicate}
              className="inline-flex items-center px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-white dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
              Duplicate
            </button>
            <button
              onClick={handleExportReport}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export Report
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          {analysisType === 'llm-judge' && evaluationData && (
            <LLMJudgeResults evaluationData={evaluationData} />
          )}
          {analysisType === 'pii' && evaluationData && (
            <PIIEvaluationResults evaluationData={evaluationData} />
          )}
          {analysisType === 'generic' && evaluationData && (
            <div className="p-6">
              <EvaluationAnalysis 
                evaluationId={id}
                evaluationData={evaluationData}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default EvaluationAnalysisDetail;