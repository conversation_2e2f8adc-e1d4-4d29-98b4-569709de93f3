import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { api } from '../utils/evaluationApi';

function EvaluationAnalysis({ evaluationId, evaluationData }) {
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalysis();
  }, [evaluationId, evaluationData]);

  const loadAnalysis = async () => {
    try {
      setLoading(true);
      
      let response;
      if (evaluationId) {
        try {
          response = await api.get(`/evaluation-analysis/${evaluationId}`);
        } catch (err) {
          // If not found, fall back to analyzing provided data if available
          if (err?.response?.status === 404 && evaluationData) {
            response = await api.post('/evaluation-analysis/analyze', {
              evaluation_data: evaluationData
            });
          } else {
            throw err;
          }
        }
      } else if (evaluationData) {
        response = await api.post('/evaluation-analysis/analyze', {
          evaluation_data: evaluationData
        });
      } else {
        throw new Error('No evaluation data provided');
      }
      
      setAnalysis(response.data.analysis);
    } catch (err) {
      console.error('Error loading analysis:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format) => {
    try {
      const response = await api.post('/evaluation-analysis/export', {
        evaluation_id: evaluationId || evaluationData?.evaluation_id,
        format
      });
      
      // Handle download based on format
      if (format === 'markdown' || format === 'html') {
        const blob = new Blob([response.data], { 
          type: format === 'markdown' ? 'text/markdown' : 'text/html' 
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `evaluation_report.${format === 'markdown' ? 'md' : 'html'}`;
        a.click();
      }
    } catch (err) {
      console.error('Error exporting report:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p className="text-red-600 dark:text-red-400">Error loading analysis: {error}</p>
      </div>
    );
  }

  if (!analysis) return null;

  const getGradeColor = (grade) => {
    switch (grade) {
      case 'A': return 'text-green-600 dark:text-green-400';
      case 'B': return 'text-blue-600 dark:text-blue-400';
      case 'C': return 'text-yellow-600 dark:text-yellow-400';
      case 'D': return 'text-orange-600 dark:text-orange-400';
      case 'F': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusColor = (level) => {
    switch (level) {
      case 'excellent': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'good': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'acceptable': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'poor': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
      case 'critical': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'high':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />;
      case 'medium':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'high': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'low': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      default: return 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Export Options */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Evaluation Analysis
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => exportReport('markdown')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export MD
          </button>
          <button
            onClick={() => exportReport('html')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export HTML
          </button>
          <button
            onClick={() => exportReport('json')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export JSON
          </button>
        </div>
      </div>

      {/* Summary Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Performance Grade */}
          <div className="text-center">
            <div className={`text-6xl font-bold ${getGradeColor(analysis.summary?.performance_grade)}`}>
              {analysis.summary?.performance_grade || 'N/A'}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Performance Grade</p>
          </div>

          {/* Average Score */}
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 dark:text-white">
              {(analysis.summary?.average_score || 0).toFixed(1)}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Average Score</p>
          </div>

          {/* Success Rate */}
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 dark:text-white">
              {analysis.summary?.success_rate || 0}%
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Success Rate</p>
          </div>

          {/* Status */}
          <div className="text-center">
            <div className={`inline-block px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(analysis.summary?.status_interpretation?.level)}`}>
              {analysis.summary?.status_interpretation?.level || 'Unknown'}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
              {analysis.summary?.status_interpretation?.message}
            </p>
          </div>
        </div>

        {/* Score Distribution Bar */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Score Distribution</h4>
          <div className="flex space-x-1">
            {analysis.summary?.score_distribution && Object.entries(analysis.summary.score_distribution).map(([range, count]) => {
              const total = Object.values(analysis.summary.score_distribution).reduce((a, b) => a + b, 0);
              const percentage = total > 0 ? (count / total) * 100 : 0;
              const colors = {
                excellent: 'bg-green-500',
                good: 'bg-blue-500',
                acceptable: 'bg-yellow-500',
                poor: 'bg-orange-500',
                failing: 'bg-red-500'
              };
              return (
                <div key={range} className="flex-1">
                  <div className="relative">
                    <div className={`${colors[range]} rounded`} style={{ height: `${Math.max(percentage, 5)}px` }}></div>
                    <div className="text-xs text-center mt-1 text-gray-600 dark:text-gray-400">{count}</div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>Failing</span>
            <span>Poor</span>
            <span>Accept</span>
            <span>Good</span>
            <span>Excel</span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {['overview', 'issues', 'recommendations', 'evaluators', 'insights'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Evaluator Breakdown */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Evaluator Results</h3>
              <div className="space-y-3">
                {analysis.summary?.evaluator_breakdown?.map((evaluator) => (
                  <div key={evaluator.name} className="flex items-center justify-between">
                    <div className="flex items-center">
                      {evaluator.passed ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                      )}
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {evaluator.name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-bold ${evaluator.passed ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {evaluator.score?.toFixed(1) || 'N/A'}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        evaluator.status === 'completed' 
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {evaluator.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Patterns */}
            {analysis.patterns && Object.keys(analysis.patterns).length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Detected Patterns</h3>
                <div className="space-y-3">
                  {analysis.patterns.consistent_failures?.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-red-600 dark:text-red-400">Consistent Failures:</p>
                      <ul className="mt-1 space-y-1">
                        {analysis.patterns.consistent_failures.map((failure, idx) => (
                          <li key={idx} className="text-sm text-gray-600 dark:text-gray-400">
                            • {failure.metric}: {failure.score.toFixed(1)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {analysis.patterns.outlier_metrics?.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Outlier Metrics:</p>
                      <ul className="mt-1 space-y-1">
                        {analysis.patterns.outlier_metrics.map((outlier, idx) => (
                          <li key={idx} className="text-sm text-gray-600 dark:text-gray-400">
                            • {outlier.metric}: {outlier.deviation > 0 ? '+' : ''}{outlier.deviation.toFixed(1)} from average
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'issues' && (
          <div className="space-y-4">
            {analysis.issues_found?.length > 0 ? (
              analysis.issues_found.map((issue, idx) => (
                <div key={idx} className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-start">
                    {getSeverityIcon(issue.severity)}
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {issue.evaluator}: {issue.issue}
                        </h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(issue.severity)}`}>
                          {issue.severity}
                        </span>
                      </div>
                      {issue.details && (
                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          {typeof issue.details === 'string' ? issue.details : JSON.stringify(issue.details, null, 2)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <p className="text-green-700 dark:text-green-300">No issues detected - all evaluations passed successfully!</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div className="space-y-4">
            {analysis.recommendations?.length > 0 ? (
              analysis.recommendations.map((rec, idx) => (
                <div key={idx} className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-start">
                    <LightBulbIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {rec.recommendation}
                        </h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(rec.priority)}`}>
                          {rec.priority}
                        </span>
                      </div>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        From: {rec.evaluator}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-blue-700 dark:text-blue-300">No specific recommendations at this time.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'evaluators' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(analysis.evaluator_analyses || {}).map(([name, evalAnalysis]) => (
              <div key={name} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">{name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 text-sm rounded-full ${
                      evalAnalysis.status === 'completed'
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                        : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                    }`}>
                      {evalAnalysis.score?.toFixed(1) || 'N/A'}
                    </span>
                    {evalAnalysis.confidence_score && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {(evalAnalysis.confidence_score * 100).toFixed(0)}% conf
                      </span>
                    )}
                  </div>
                </div>
                
                {/* Confidence and Reasoning */}
                {evalAnalysis.confidence_reasoning && (
                  <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-700 dark:text-blue-300">
                    {evalAnalysis.confidence_reasoning}
                  </div>
                )}
                
                {/* Cost Analysis for LLM Judge */}
                {evalAnalysis.cost_analysis && (
                  <div className="mb-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cost Analysis:</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Total Cost:</span>
                        <span className="ml-1 font-medium">${evalAnalysis.cost_analysis.total_cost?.toFixed(4)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Tokens:</span>
                        <span className="ml-1 font-medium">{evalAnalysis.cost_analysis.estimated_tokens_used}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Monthly Est:</span>
                        <span className="ml-1 font-medium">${evalAnalysis.cost_analysis.monthly_projection?.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Provider:</span>
                        <span className="ml-1 font-medium">{evalAnalysis.cost_analysis.provider}</span>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Self-Explaining Metrics */}
                {evalAnalysis.self_explaining_metrics && (
                  <div className="mb-3">
                    {evalAnalysis.self_explaining_metrics.why_score_not_higher && (
                      <div className="p-2 bg-gray-50 dark:bg-gray-900 rounded mb-2">
                        <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Why not higher?</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {evalAnalysis.self_explaining_metrics.why_score_not_higher}
                        </p>
                      </div>
                    )}
                    {evalAnalysis.self_explaining_metrics.specific_improvements && (
                      <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded">
                        <p className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">Improvements:</p>
                        <ul className="text-xs text-green-600 dark:text-green-400 space-y-1">
                          {evalAnalysis.self_explaining_metrics.specific_improvements.map((imp, idx) => (
                            <li key={idx}>• {imp}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
                
                {evalAnalysis.insights && (
                  <div className="space-y-3">
                    {Object.entries(evalAnalysis.insights).map(([key, value]) => {
                      if (typeof value === 'object' && !Array.isArray(value)) {
                        return (
                          <div key={key} className="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                            </p>
                            <pre className="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 p-2 rounded overflow-x-auto">
                              {JSON.stringify(value, null, 2)}
                            </pre>
                          </div>
                        );
                      } else if (Array.isArray(value) && value.length > 0) {
                        return (
                          <div key={key} className="border-t border-gray-200 dark:border-gray-700 pt-3">
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                            </p>
                            <ul className="space-y-1">
                              {value.slice(0, 5).map((item, idx) => (
                                <li key={idx} className="text-xs text-gray-600 dark:text-gray-400">
                                  • {typeof item === 'object' ? JSON.stringify(item) : item}
                                </li>
                              ))}
                            </ul>
                          </div>
                        );
                      } else if (value !== null && value !== undefined) {
                        return (
                          <div key={key} className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                            </span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {typeof value === 'number' ? value.toFixed(2) : value.toString()}
                            </span>
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {activeTab === 'insights' && (
          <div className="space-y-6">
            {/* Actionable Insights */}
            {analysis.actionable_insights && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Action Items</h3>
                
                {analysis.actionable_insights.immediate_actions?.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-2">Immediate Actions Required</h4>
                    <ul className="space-y-2">
                      {analysis.actionable_insights.immediate_actions.map((action, idx) => (
                        <li key={idx} className="flex items-start">
                          <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mt-0.5 mr-2" />
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">{action.action}</p>
                            {action.reason && (
                              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{action.reason}</p>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {analysis.actionable_insights.short_term_improvements?.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-2">Short-term Improvements</h4>
                    <ul className="space-y-2">
                      {analysis.actionable_insights.short_term_improvements.map((improvement, idx) => (
                        <li key={idx} className="flex items-start">
                          <ArrowTrendingUpIcon className="h-4 w-4 text-yellow-500 mt-0.5 mr-2" />
                          <p className="text-sm text-gray-900 dark:text-white">{improvement.action}</p>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {analysis.actionable_insights.long_term_strategies?.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">Long-term Strategies</h4>
                    <ul className="space-y-2">
                      {analysis.actionable_insights.long_term_strategies.map((strategy, idx) => (
                        <li key={idx} className="flex items-start">
                          <ChartBarIcon className="h-4 w-4 text-blue-500 mt-0.5 mr-2" />
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">{strategy.strategy}</p>
                            {strategy.details && (
                              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{strategy.details}</p>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Comparative Insights */}
            {analysis.comparative_insights && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Historical Comparison</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Trend</p>
                    <div className="flex items-center mt-1">
                      {analysis.comparative_insights.trend === 'improving' ? (
                        <ArrowTrendingUpIcon className="h-5 w-5 text-green-500 mr-1" />
                      ) : analysis.comparative_insights.trend === 'declining' ? (
                        <ArrowTrendingDownIcon className="h-5 w-5 text-red-500 mr-1" />
                      ) : (
                        <span className="text-gray-500">→</span>
                      )}
                      <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                        {analysis.comparative_insights.trend}
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Change Rate</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white mt-1">
                      {analysis.comparative_insights.improvement_rate?.toFixed(1) || 0}%
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Consistency</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white mt-1 capitalize">
                      {analysis.comparative_insights.consistency}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Best Performance</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white mt-1">
                      {analysis.comparative_insights.best_performance?.toFixed(1) || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default EvaluationAnalysis;