import React, { useEffect, useState } from 'react';
import { X, Loader2, <PERSON>T<PERSON>t, Settings, Search, Edit3, Trash2, MoreVertical, RotateCw } from 'lucide-react';
import { getApiBaseUrl } from '../api/getApiBase';

export default function PolicyGroupPoliciesModal({ groupId, groupName, onClose, onPolicyToggle }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [policies, setPolicies] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [togglingPolicies, setTogglingPolicies] = useState(new Set());
  const [showInactive, setShowInactive] = useState(true);

  // Prevent background scrolling when modal is open
  useEffect(() => {
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  // Fetch policies for this group
  useEffect(() => {
    const controller = new AbortController();
    async function fetchPolicies() {
      try {
        setLoading(true);
        setError(null);
        const url = `${getApiBaseUrl()}/api/v1/policy-groups/${groupId}/policies?include_inactive=${showInactive}`;
        const res = await fetch(url, {
          headers: { Authorization: 'Bearer admin-token' },
          signal: controller.signal,
        });
        if (!res.ok) {
          const text = await res.text();
          throw new Error(text || `Failed to fetch policies (${res.status})`);
        }
        const json = await res.json();
        setPolicies(json);
      } catch (err) {
        if (err.name !== 'AbortError') setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchPolicies();
    return () => controller.abort();
  }, [groupId, showInactive]);

  // Handle ESC key to close modal and close dropdowns on outside click
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        if (activeDropdown) {
          setActiveDropdown(null);
        } else {
          onClose();
        }
      }
    };
    const handleClick = (e) => {
      if (activeDropdown && !e.target.closest('.relative')) {
        setActiveDropdown(null);
      }
    };
    document.addEventListener('keydown', handleEsc);
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.removeEventListener('click', handleClick);
    };
  }, [onClose, activeDropdown]);

  const activePolicies = policies.filter(p => !p.deleted_at);
  
  // Filter policies based on search query
  const filteredPolicies = activePolicies.filter(policy => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      policy.name.toLowerCase().includes(query) ||
      (policy.description || '').toLowerCase().includes(query) ||
      (policy.policy_template || '').toLowerCase().includes(query)
    );
  });
  
  const policyTypeGroups = activePolicies.reduce((acc, policy) => {
    const type = policy.policy_template || 'Other';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});
  
  const statusGroups = activePolicies.reduce((acc, policy) => {
    const status = policy.is_active ? 'active' : 'inactive';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  // Toggle policy active status
  const handleToggleStatus = async (policy) => {
    const policyId = policy.policy_id;
    const newStatus = !policy.is_active;
    
    // Add to toggling set for loading state
    setTogglingPolicies(prev => new Set([...prev, policyId]));
    
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/policies/${policyId}`, {
        method: 'PUT',
        headers: {
          'Authorization': 'Bearer admin-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_active: newStatus,
          definition: policy.definition,
          name: policy.name,
          description: policy.description,
          category: policy.category,
          severity: policy.severity,
          policy_type: policy.policy_type,
          applies_to_roles: policy.applies_to_roles
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update policy status (${response.status})`);
      }

      const updatedPolicy = await response.json();
      
      // Update the policy in the list
      setPolicies(prev => 
        prev.map(p => 
          p.policy_id === policyId 
            ? { ...p, is_active: updatedPolicy.is_active }
            : p
        )
      );
      
      // Notify parent component to refetch policy group data
      if (onPolicyToggle) {
        onPolicyToggle();
      }
      
    } catch (err) {
      console.error('Failed to toggle policy status:', err);
      alert(`Failed to ${newStatus ? 'activate' : 'deactivate'} policy: ${err.message}`);
    } finally {
      // Remove from toggling set
      setTogglingPolicies(prev => {
        const newSet = new Set(prev);
        newSet.delete(policyId);
        return newSet;
      });
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <FileText className="h-6 w-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-semibold text-white">{groupName}</h2>
              <p className="text-sm text-gray-400">
                {activePolicies.length} {activePolicies.length === 1 ? 'policy' : 'policies'} 
                {searchQuery && filteredPolicies.length !== activePolicies.length && (
                  <span> • {filteredPolicies.length} shown</span>
                )}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            aria-label="Close modal"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        {/* Search Bar and Filters */}
        {(activePolicies.length > 0 || showInactive) && (
          <div className="px-3 sm:px-5 py-3 border-b border-gray-700">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search policies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <label className="flex items-center gap-2 text-sm text-gray-300 cursor-pointer shrink-0">
                <input
                  type="checkbox"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="h-4 w-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
                <span>Show inactive policies</span>
              </label>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-5">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 text-blue-400 animate-spin" />
              <span className="ml-3 text-gray-300">Loading policies...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-400 mb-2">Error loading policies</div>
              <div className="text-sm text-gray-400">{error}</div>
            </div>
          ) : activePolicies.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-300 mb-2">No Policies</h3>
              <p className="text-gray-400">This policy group doesn't have any policies assigned.</p>
            </div>
          ) : (
            <>
              {/* Summary Stats */}
              {Object.keys(policyTypeGroups).length > 0 && (
                <div className="mb-6 p-4 bg-gray-700/50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-300 mb-3">Policy Types</h3>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(policyTypeGroups).map(([type, count]) => (
                          <span
                            key={type}
                            className="px-2 py-1 bg-blue-600/20 text-blue-300 text-xs rounded-full"
                          >
                            {type}: {count}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-300 mb-3">Status</h3>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(statusGroups).map(([status, count]) => (
                          <span
                            key={status}
                            className={`px-2 py-1 text-xs rounded-full ${
                              status === 'active' 
                                ? 'bg-green-600/20 text-green-300' 
                                : 'bg-red-600/20 text-red-300'
                            }`}
                          >
                            {status}: {count}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Policy List */}
              <div className="space-y-3">
                {filteredPolicies.length === 0 && searchQuery ? (
                  <div className="text-center py-8">
                    <Search className="h-8 w-8 text-gray-500 mx-auto mb-3" />
                    <p className="text-gray-400">No policies found matching "{searchQuery}"</p>
                  </div>
                ) : (
                  filteredPolicies.map((policy) => (
                    <div
                      key={policy.policy_id}
                      className="p-4 bg-gray-700/50 rounded-lg border border-gray-600/50 hover:bg-gray-700/70 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0 pr-3">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-white font-medium truncate">{policy.name}</h4>
                            {policy.policy_template && (
                              <span className="px-2 py-0.5 bg-blue-600/20 text-blue-300 text-xs rounded-full shrink-0">
                                {policy.policy_template}
                              </span>
                            )}
                          </div>
                          {policy.description && (
                            <p className="text-sm text-gray-400 line-clamp-2 mb-2">
                              {policy.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Version {policy.version}</span>
                            <span>Updated {new Date(policy.updated_at).toLocaleDateString()}</span>
                            {policy.created_by_name && (
                              <span>by {policy.created_by_name}</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-start gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleStatus(policy);
                            }}
                            disabled={togglingPolicies.has(policy.policy_id)}
                            className={`px-2 py-0.5 text-xs rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${
                              policy.is_active 
                                ? 'bg-green-600/20 text-green-300 hover:bg-green-600/30 hover:text-green-200'
                                : 'bg-red-600/20 text-red-300 hover:bg-red-600/30 hover:text-red-200'
                            }`}
                            title={`Click to ${policy.is_active ? 'deactivate' : 'activate'} policy`}
                          >
                            {togglingPolicies.has(policy.policy_id) ? (
                              <div className="flex items-center gap-1">
                                <RotateCw className="h-3 w-3 animate-spin" />
                                <span>...</span>
                              </div>
                            ) : (
                              <span>{policy.is_active ? 'Active' : 'Inactive'}</span>
                            )}
                          </button>
                          <div className="relative">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setActiveDropdown(activeDropdown === policy.policy_id ? null : policy.policy_id);
                              }}
                              className="p-1 hover:bg-gray-600 rounded transition-colors"
                              title="More actions"
                            >
                              <MoreVertical className="h-4 w-4 text-gray-400" />
                            </button>
                          {activeDropdown === policy.policy_id && (
                            <div className="absolute right-0 top-8 w-40 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10">
                              <button
                                onClick={() => {
                                  window.location.href = `/admin/policy-configuration?edit=${policy.policy_id}`;
                                }}
                                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                              >
                                <Edit3 className="h-3 w-3" />
                                Edit Policy
                              </button>
                              <button
                                onClick={async () => {
                                  if (window.confirm(`Remove "${policy.name}" from this group?`)) {
                                    try {
                                      const res = await fetch(
                                        `${getApiBaseUrl()}/api/v1/policy-groups/${groupId}/policies/${policy.policy_id}`,
                                        {
                                          method: 'DELETE',
                                          headers: { Authorization: 'Bearer admin-token' },
                                        }
                                      );
                                      if (res.ok) {
                                        setPolicies(prev => prev.filter(p => p.policy_id !== policy.policy_id));
                                      }
                                    } catch (err) {
                                      alert('Failed to remove policy from group');
                                    }
                                  }
                                  setActiveDropdown(null);
                                }}
                                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-400 hover:bg-gray-700 transition-colors"
                              >
                                <Trash2 className="h-3 w-3" />
                                Remove from Group
                              </button>
                            </div>
                          )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row items-center justify-between p-3 sm:p-5 border-t border-gray-700 gap-3 sm:gap-0">
          <div className="text-sm text-gray-400 order-2 sm:order-1">
            {activePolicies.length > 0 && (
              <span>{activePolicies.length} policies shown</span>
            )}
          </div>
          <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3 w-full sm:w-auto order-1 sm:order-2">
            <button
              onClick={() => {
                // Navigate to policy configuration page with group filter
                const searchParams = new URLSearchParams();
                searchParams.set('group', groupName);
                window.location.href = `/admin/policy-configuration?${searchParams.toString()}`;
              }}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors w-full sm:w-auto"
            >
              <Settings className="h-4 w-4" />
              <span>Manage Policies</span>
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors w-full sm:w-auto"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}