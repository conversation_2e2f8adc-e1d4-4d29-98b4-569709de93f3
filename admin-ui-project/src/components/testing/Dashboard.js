import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  CircleStackIcon,
  BeakerIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { api } from '../../utils/evaluationApi';
import Breadcrumb from './Breadcrumb';

function Dashboard() {
  const [stats, setStats] = useState({
    datasets: { total: 0, active: 0 },
    experiments: { total: 0, running: 0, completed: 0 },
    evaluations: { total: 0, active: 0, avg_performance: 0 }
  });
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load all stats in parallel
      const [datasetsRes, experimentsRes, evaluationsRes] = await Promise.all([
        api.get('/datasets?limit=100'),
        api.get('/experiments?limit=100'),
        api.get('/evaluations/stats')
      ]);

      const datasets = datasetsRes.data.datasets;
      const experiments = experimentsRes.data.experiments;
      const evalStats = evaluationsRes.data;

      setStats({
        datasets: {
          total: datasets.length,
          active: datasets.filter(d => d.status === 'active').length
        },
        experiments: {
          total: experiments.length,
          running: experiments.filter(e => e.status === 'running').length,
          completed: experiments.filter(e => e.status === 'completed').length
        },
        evaluations: evalStats
      });

      // Recent activity (last 5 experiments)
      const recent = experiments
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5);
      setRecentActivity(recent);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Dashboard', href: '/' }
  ];

  if (loading) {
    return (
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <Breadcrumb items={breadcrumbItems} />
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg p-5">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Datasets',
      value: stats.datasets.total,
      subtitle: `${stats.datasets.active} active`,
      icon: CircleStackIcon,
      color: 'bg-blue-500',
      href: '/datasets'
    },
    {
      title: 'Running Experiments',
      value: stats.experiments.running,
      subtitle: `${stats.experiments.completed} completed`,
      icon: BeakerIcon,
      color: 'bg-green-500',
      href: '/experiments'
    },
    {
      title: 'Total Metrics',
      value: stats.evaluations.total_metrics || 3,
      subtitle: `${stats.evaluations.active_metrics || 3} active`,
      icon: ClipboardDocumentListIcon,
      color: 'bg-purple-500',
      href: '/evaluations'
    },
    {
      title: 'Avg Performance',
      value: `${stats.evaluations.avg_performance || 93}%`,
      subtitle: 'Across all metrics',
      icon: ChartBarIcon,
      color: 'bg-orange-500',
      href: '/experiments'
    }
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
          Overview of your AI model testing and evaluation activities
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {statCards.map((card) => (
          <Link
            key={card.title}
            to={card.href}
            className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-lg ${card.color}`}>
                    <card.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      {card.title}
                    </dt>
                    <dd className="text-2xl font-bold text-gray-900 dark:text-white">
                      {card.value}
                    </dd>
                    <dd className="text-sm text-gray-600 dark:text-gray-300">
                      {card.subtitle}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h2>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {recentActivity.length > 0 ? (
            recentActivity.map((experiment) => (
              <div key={experiment.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <Link
                      to="/experiments"
                      className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300"
                    >
                      {experiment.name}
                    </Link>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      Dataset: {experiment.dataset_name} • Agent: {experiment.agent_config?.name}
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-${experiment.status} bg-status-${experiment.status}`}>
                        {experiment.status.charAt(0).toUpperCase() + experiment.status.slice(1)}
                      </div>
                      {experiment.status === 'running' && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {experiment.progress}% complete
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(experiment.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-8 text-center">
              <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No experiments yet</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by creating your first experiment.
              </p>
              <div className="mt-6">
                <Link
                  to="/experiments"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                >
                  Create Experiment
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;