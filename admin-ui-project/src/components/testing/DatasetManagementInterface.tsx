import React, { useState, useMemo } from 'react';
import { Search, Filter, Plus, Edit, Trash2, ChevronDown, Eye, Settings, Download, Upload, Tag, Calendar, Users, Database, FileText, BarChart3, CheckCircle, X } from 'lucide-react';

// Define data types for dataset management
interface Dataset {
  id: string;
  name: string;
  description: string;
  type: 'clinical_text' | 'medical_imaging' | 'patient_records' | 'lab_results' | 'medication_data' | 'vital_signs';
  status: 'active' | 'archived' | 'draft' | 'under_review';
  version: string;
  createdAt: string;
  lastUpdated: string;
  createdBy: string;
  recordCount: number;
  dataSize: string;
  tags: string[];
  complianceLevel: 'hipaa_compliant' | 'phi_redacted' | 'deidentified' | 'restricted';
  qualityScore: number;
  usageCount: number;
  department: string;
  dataSource: string;
  validationStatus: 'validated' | 'pending' | 'failed' | 'in_progress';
}

// Create realistic healthcare-focused dummy data
const DUMMY_DATASETS: Dataset[] = [
  {
    id: 'dataset_001',
    name: 'Emergency Department Clinical Notes v2.1',
    description: 'Comprehensive collection of emergency department clinical notes for AI training and evaluation',
    type: 'clinical_text',
    status: 'active',
    version: '2.1.4',
    createdAt: '2024-10-15',
    lastUpdated: '2024-12-15T10:30:00Z',
    createdBy: 'Dr. Sarah Chen',
    recordCount: 15420,
    dataSize: '2.3 GB',
    tags: ['Emergency Medicine', 'Clinical Notes', 'Training Data'],
    complianceLevel: 'deidentified',
    qualityScore: 94,
    usageCount: 23,
    department: 'Emergency Medicine',
    dataSource: 'EMR System',
    validationStatus: 'validated'
  },
  {
    id: 'dataset_002',
    name: 'Chest X-Ray Images Dataset',
    description: 'High-quality chest X-ray images for diagnostic AI model training and validation',
    type: 'medical_imaging',
    status: 'active',
    version: '1.8.2',
    createdAt: '2024-11-20',
    lastUpdated: '2024-12-14T15:45:00Z',
    createdBy: 'Dr. Michael Rodriguez',
    recordCount: 8750,
    dataSize: '15.7 GB',
    tags: ['Radiology', 'X-Ray', 'Diagnostic'],
    complianceLevel: 'phi_redacted',
    qualityScore: 91,
    usageCount: 18,
    department: 'Radiology',
    dataSource: 'PACS System',
    validationStatus: 'validated'
  },
  {
    id: 'dataset_003',
    name: 'Patient Vital Signs Time Series',
    description: 'Longitudinal vital signs data for predictive analytics and patient monitoring',
    type: 'vital_signs',
    status: 'active',
    version: '3.0.1',
    createdAt: '2024-09-10',
    lastUpdated: '2024-12-13T09:15:00Z',
    createdBy: 'Dr. Emily Watson',
    recordCount: 45600,
    dataSize: '8.9 GB',
    tags: ['Vital Signs', 'Time Series', 'Predictive'],
    complianceLevel: 'deidentified',
    qualityScore: 88,
    usageCount: 31,
    department: 'Intensive Care',
    dataSource: 'ICU Monitoring',
    validationStatus: 'validated'
  },
  {
    id: 'dataset_004',
    name: 'Medication Administration Records',
    description: 'Comprehensive medication administration data for drug interaction analysis',
    type: 'medication_data',
    status: 'under_review',
    version: '1.2.0',
    createdAt: '2024-12-01',
    lastUpdated: '2024-12-15T14:20:00Z',
    createdBy: 'Dr. James Thompson',
    recordCount: 28900,
    dataSize: '4.2 GB',
    tags: ['Medication', 'Drug Interactions', 'Safety'],
    complianceLevel: 'hipaa_compliant',
    qualityScore: 85,
    usageCount: 7,
    department: 'Pharmacy',
    dataSource: 'Medication System',
    validationStatus: 'in_progress'
  },
  {
    id: 'dataset_005',
    name: 'Laboratory Results Database',
    description: 'Comprehensive laboratory test results for diagnostic model training',
    type: 'lab_results',
    status: 'active',
    version: '2.5.3',
    createdAt: '2024-08-25',
    lastUpdated: '2024-12-12T11:30:00Z',
    createdBy: 'Dr. Lisa Park',
    recordCount: 67800,
    dataSize: '12.1 GB',
    tags: ['Laboratory', 'Test Results', 'Diagnostic'],
    complianceLevel: 'deidentified',
    qualityScore: 92,
    usageCount: 42,
    department: 'Laboratory',
    dataSource: 'LIS System',
    validationStatus: 'validated'
  },
  {
    id: 'dataset_006',
    name: 'Patient Demographics Archive',
    description: 'Historical patient demographic data for population health analysis',
    type: 'patient_records',
    status: 'archived',
    version: '1.0.8',
    createdAt: '2024-06-15',
    lastUpdated: '2024-11-30T16:45:00Z',
    createdBy: 'Dr. Robert Kim',
    recordCount: 125000,
    dataSize: '6.8 GB',
    tags: ['Demographics', 'Population Health', 'Historical'],
    complianceLevel: 'deidentified',
    qualityScore: 87,
    usageCount: 15,
    department: 'Population Health',
    dataSource: 'Patient Registry',
    validationStatus: 'validated'
  }
];

export const DatasetManagementInterface: React.FC = () => {
  const [datasets] = useState<Dataset[]>(DUMMY_DATASETS);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'qualityScore' | 'usageCount'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Filter and sort datasets
  const filteredAndSortedDatasets = useMemo(() => {
    let filtered = datasets.filter(dataset => {
      const matchesSearch = dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           dataset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           dataset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesType = filterType === 'all' || dataset.type === filterType;
      const matchesStatus = filterStatus === 'all' || dataset.status === filterStatus;
      
      return matchesSearch && matchesType && matchesStatus;
    });

    // Sort datasets
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'createdAt') {
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [datasets, searchTerm, filterType, filterStatus, sortBy, sortOrder]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'archived': return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
      case 'draft': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'under_review': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'clinical_text': return <FileText className="w-4 h-4" />;
      case 'medical_imaging': return <BarChart3 className="w-4 h-4" />;
      case 'patient_records': return <Users className="w-4 h-4" />;
      case 'lab_results': return <Database className="w-4 h-4" />;
      case 'medication_data': return <Tag className="w-4 h-4" />;
      case 'vital_signs': return <BarChart3 className="w-4 h-4" />;
      default: return <Database className="w-4 h-4" />;
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Dataset Management</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Manage and organize evaluation datasets for AI model testing</p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Create Dataset
        </button>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search datasets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Types</option>
          <option value="clinical_text">Clinical Text</option>
          <option value="medical_imaging">Medical Imaging</option>
          <option value="patient_records">Patient Records</option>
          <option value="lab_results">Lab Results</option>
          <option value="medication_data">Medication Data</option>
          <option value="vital_signs">Vital Signs</option>
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="archived">Archived</option>
          <option value="draft">Draft</option>
          <option value="under_review">Under Review</option>
        </select>

        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('-');
            setSortBy(field as any);
            setSortOrder(order as any);
          }}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="name-asc">Name (A-Z)</option>
          <option value="name-desc">Name (Z-A)</option>
          <option value="createdAt-desc">Newest First</option>
          <option value="createdAt-asc">Oldest First</option>
          <option value="qualityScore-desc">Quality (High-Low)</option>
          <option value="usageCount-desc">Usage (High-Low)</option>
        </select>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Datasets</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{datasets.length}</p>
            </div>
            <Database className="w-8 h-8 text-primary" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Datasets</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {datasets.filter(d => d.status === 'active').length}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Records</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {datasets.reduce((sum, d) => sum + d.recordCount, 0).toLocaleString()}
              </p>
            </div>
            <FileText className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Quality Score</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Math.round(datasets.reduce((sum, d) => sum + d.qualityScore, 0) / datasets.length)}
              </p>
            </div>
            <BarChart3 className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Dataset Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedDatasets.map(dataset => (
          <div 
            key={dataset.id}
            onClick={() => setSelectedDataset(dataset)}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getTypeIcon(dataset.type)}
                <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                  {dataset.type.replace('_', ' ')}
                </span>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(dataset.status)}`}>
                {dataset.status.replace('_', ' ')}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {dataset.name}
            </h3>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
              {dataset.description}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Records:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">
                  {dataset.recordCount.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Size:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">{dataset.dataSize}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Quality:</span>
                <span className={`font-medium ${getQualityColor(dataset.qualityScore)}`}>
                  {dataset.qualityScore}%
                </span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-1">
              {dataset.tags.slice(0, 2).map(tag => (
                <span key={tag} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                  {tag}
                </span>
              ))}
              {dataset.tags.length > 2 && (
                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                  +{dataset.tags.length - 2}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Dataset Detail Modal */}
      {selectedDataset && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {selectedDataset.name}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{selectedDataset.description}</p>
              </div>
              <button 
                onClick={() => setSelectedDataset(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Dataset Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Type:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedDataset.type.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Version:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.version}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Status:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedDataset.status)}`}>
                        {selectedDataset.status.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Department:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Data Source:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.dataSource}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Quality Metrics</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Quality Score:</span>
                      <span className={`font-medium ${getQualityColor(selectedDataset.qualityScore)}`}>
                        {selectedDataset.qualityScore}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Usage Count:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.usageCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Compliance Level:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedDataset.complianceLevel.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Validation Status:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedDataset.validationStatus.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Data Statistics</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Record Count:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {selectedDataset.recordCount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Data Size:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.dataSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Created:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {new Date(selectedDataset.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {new Date(selectedDataset.lastUpdated).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Created By:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedDataset.createdBy}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedDataset.tags.map(tag => (
                      <span key={tag} className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                <Download className="w-4 h-4 inline mr-2" />
                Export
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Edit className="w-4 h-4 inline mr-2" />
                Edit Dataset
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Dataset Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Create New Dataset</h2>
              <button 
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Dataset Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter dataset name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Describe the dataset purpose and contents"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Type
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select type</option>
                    <option value="clinical_text">Clinical Text</option>
                    <option value="medical_imaging">Medical Imaging</option>
                    <option value="patient_records">Patient Records</option>
                    <option value="lab_results">Lab Results</option>
                    <option value="medication_data">Medication Data</option>
                    <option value="vital_signs">Vital Signs</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Department
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select department</option>
                    <option value="Emergency Medicine">Emergency Medicine</option>
                    <option value="Radiology">Radiology</option>
                    <option value="Laboratory">Laboratory</option>
                    <option value="Pharmacy">Pharmacy</option>
                    <option value="Intensive Care">Intensive Care</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                Create Dataset
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 