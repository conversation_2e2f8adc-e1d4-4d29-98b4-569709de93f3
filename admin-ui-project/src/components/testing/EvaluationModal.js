import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  XMarkIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { api } from '../../utils/evaluationApi';

// Evaluation Modal Component
function EvaluationModal({ session, onClose, onEvaluationComplete, shouldNavigate = true, importMode = false, csvFile = null }) {
  const navigate = useNavigate();
  const [step, setStep] = useState(importMode ? 0 : (session ? 0 : -1)); // For import mode, start at dataset selection
  const [loading, setLoading] = useState(false);
  const [datasets, setDatasets] = useState([]);
  const [evaluators, setEvaluators] = useState([]);
  const [testSessions, setTestSessions] = useState([]);
  const [selectedTestSession, setSelectedTestSession] = useState(session || null);
  const [selectedDataset, setSelectedDataset] = useState('');
  const [selectedEvaluators, setSelectedEvaluators] = useState(() => {
    // Restore selected evaluators from localStorage if available
    const saved = localStorage.getItem('evaluation_modal_selected_evaluators');
    return saved ? JSON.parse(saved) : [];
  });
  const [evaluationResults, setEvaluationResults] = useState(null);
  // Dataset is now optional for all evaluations
  const [experimentName, setExperimentName] = useState('');
  const [experimentDescription, setExperimentDescription] = useState('');

  useEffect(() => {
    loadEvaluationData();
  }, []);

  const loadEvaluationData = async () => {
    try {
      // Load datasets
      const datasetsResponse = await api.get('/datasets');
      setDatasets(datasetsResponse.data.datasets || []);

      // Load test sessions that have actual test results
      const experimentsResponse = await api.get('/experiments');
      const allExperiments = experimentsResponse.data.experiments || [];
      
      // Filter to only show experiments that have completed status
      // For standalone evals, we don't need expected_output, just input/actual_output
      const sessionsWithResults = allExperiments.filter(exp => 
        exp.status === 'completed'
      );
      
      console.log('Available test sessions:', sessionsWithResults);
      setTestSessions(sessionsWithResults);

      // Load available evaluators from evaluator service
      try {
        const evaluatorsResponse = await api.get('/evaluators');
        const allEvaluators = evaluatorsResponse.data.evaluators || [];
        
        // Use all available evaluators from the service
        const implementedEvaluators = allEvaluators.map(evaluator => ({
          id: evaluator.id,
          name: evaluator.name,
          description: evaluator.description,
          category: evaluator.category,
          supported_modes: evaluator.supported_modes || ['standalone'],
          config_schema: evaluator.config_schema || {}
        }));
        
        setEvaluators(implementedEvaluators);
      } catch (error) {
        console.error('Error loading evaluators:', error);
        setEvaluators([]);
      }
    } catch (error) {
      console.error('Error loading evaluation data:', error);
    }
  };

  // Save selected evaluators to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('evaluation_modal_selected_evaluators', JSON.stringify(selectedEvaluators));
  }, [selectedEvaluators]);

  // Check if selected evaluators need dataset
  // Dataset is now optional for all evaluations - removed needsDataset logic

  const runEvaluation = async () => {
    // For import mode, handle CSV import with dataset
    if (importMode && csvFile) {
      if (!experimentName) {
        alert('Please provide a name for the experiment');
        return;
      }
      
      try {
        setLoading(true);
        
        // Create FormData to upload CSV
        const formData = new FormData();
        formData.append('file', csvFile);
        // Only append dataset_id if one is selected (not null)
        if (selectedDataset !== null && selectedDataset !== undefined) {
          formData.append('dataset_id', selectedDataset);
        }
        formData.append('name', experimentName);
        formData.append('description', experimentDescription || `Imported results for ${experimentName}`);
        formData.append('execution_mode', 'import');
        
        // Import the CSV results as an experiment
        const response = await api.post('/experiments/import-csv', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        
        const experimentId = response.data.id;
        
        // Clear the persisted evaluator selection since import was successful
        localStorage.removeItem('evaluation_modal_selected_evaluators');
        
        onClose();
        onEvaluationComplete();
        navigate('/experiments');
        
      } catch (error) {
        console.error('Error importing CSV results:', error);
        alert('Error importing CSV results: ' + (error.response?.data?.error || error.message));
        setLoading(false);
      }
      return;
    }
    
    // Original evaluation logic for non-import mode
    if (!selectedTestSession?.id) {
      alert('No test session selected');
      return;
    }

    if (selectedEvaluators.length === 0) {
      alert('Please select at least one evaluator');
      return;
    }

    // Dataset is optional - no validation needed

    try {
      setLoading(true);
      
      // Run unified evaluation
      const { evaluationId, experimentId } = await runUnifiedEvaluation();
      
      // Clear the persisted evaluator selection
      localStorage.removeItem('evaluation_modal_selected_evaluators');
      
      // Close modal and navigate
      onClose();
      onEvaluationComplete();
      
      if (shouldNavigate) {
        console.log('Navigating to results with evaluationId:', evaluationId);
        navigate(`/evaluation-results?evaluation=${evaluationId}&status=running`);
      }
      
    } catch (error) {
      console.error('Error running evaluation:', error);
      alert('Error running evaluation: ' + (error.response?.data?.error || error.message));
      setLoading(false);
    }
  };

  const runUnifiedEvaluation = async () => {
    try {
      const selectedEvaluatorIds = selectedEvaluators.map(e => e.id);
      console.log('Running evaluation with evaluators:', selectedEvaluatorIds);
      console.log('Selected test session:', selectedTestSession);
      
      if (!selectedTestSession || !selectedTestSession.id) {
        throw new Error('No test session selected or session has no ID');
      }
      
      // Use the new evaluate endpoint - don't create a new experiment!
      const evaluateResponse = await api.post(`/experiments/${selectedTestSession.id}/evaluate`, {
        evaluator_ids: selectedEvaluatorIds
      });
      
      const evaluationId = evaluateResponse.data.evaluation_id;
      const experimentId = selectedTestSession.id; // Keep the original experiment ID
      
      console.log('Started evaluation:', evaluationId, 'for experiment:', experimentId);
      console.log('Full response:', evaluateResponse.data);
      
      return { evaluationId, experimentId };
      
    } catch (error) {
      console.error('Error in unified evaluation:', error);
      throw error;
    }
  };


  const getEvaluatorsByCategory = (category) => {
    return evaluators.filter(evaluator => evaluator.category === category);
  };

  const getCategoryColor = (category) => {
    const colors = {
      accuracy: 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-700',
      safety: 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700',
      performance: 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700',
      quality: 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-700',
      efficiency: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700'
    };
    return colors[category] || 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600';
  };

  const toggleEvaluatorSelection = (evaluator) => {
    setSelectedEvaluators(prev => {
      const isSelected = prev.some(e => e.id === evaluator.id);
      if (isSelected) {
        return prev.filter(e => e.id !== evaluator.id);
      } else {
        return [...prev, evaluator];
      }
    });
  };

  const categories = [...new Set(evaluators.map(e => e.category))].sort();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {importMode ? 'Import CSV Results' : 'Run Evaluation'}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              {importMode 
                ? `Importing: ${csvFile?.name || 'No file selected'}` 
                : `Test Session: ${selectedTestSession?.name || 'No test session selected'}`}
            </p>
          </div>
          <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* Import Mode Content */}
          {importMode ? (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Import Experiment Results</h3>
              
              {/* Experiment Details */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Experiment Name *
                </label>
                <input
                  type="text"
                  value={experimentName}
                  onChange={(e) => setExperimentName(e.target.value)}
                  placeholder="e.g., PII Detection Test Results"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description (optional)
                </label>
                <textarea
                  value={experimentDescription}
                  onChange={(e) => setExperimentDescription(e.target.value)}
                  placeholder="Describe the experiment and results..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              {/* Dataset Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Link to Dataset <span className="text-gray-400 text-xs font-normal">(Optional but Recommended)</span>
                </label>
                
                {/* Benefits of linking dataset */}
                <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md">
                  <h4 className="text-xs font-semibold text-blue-800 dark:text-blue-300 mb-2">📊 Why link a dataset?</h4>
                  <ul className="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                    <li>• <strong>Accuracy Metrics:</strong> Compare actual outputs against expected outputs</li>
                    <li>• <strong>Advanced Evaluators:</strong> Enable evaluators that require ground truth data</li>
                    <li>• <strong>Performance Tracking:</strong> Monitor trends across different test datasets</li>
                    <li>• <strong>Comprehensive Reports:</strong> Generate detailed pass/fail analysis</li>
                  </ul>
                </div>

                <div className="space-y-3 max-h-64 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-3">
                  {/* Option to not link dataset */}
                  <div
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedDataset === null 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedDataset(null)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">🚀 No Dataset Link</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">Import results without linking to a dataset</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Limited to basic metrics and analysis</p>
                      </div>
                      {selectedDataset === null && (
                        <div className="text-blue-600 dark:text-blue-400">✓</div>
                      )}
                    </div>
                  </div>

                  {datasets.length > 0 && (
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-2 pb-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Available Datasets:</p>
                    </div>
                  )}
                  
                  {datasets.map(dataset => (
                    <div
                      key={dataset.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedDataset === dataset.id 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedDataset(dataset.id)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">{dataset.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{dataset.description}</p>
                          <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mr-2">{dataset.type}</span>
                            <span>{dataset.record_count || 0} test cases</span>
                          </div>
                        </div>
                        {selectedDataset === dataset.id && (
                          <div className="text-blue-600 dark:text-blue-400">✓</div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {datasets.length === 0 && (
                    <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                      <p className="text-sm">No datasets available.</p>
                      <p className="text-xs mt-1">You can still import results without a dataset link.</p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* CSV Preview */}
              {csvFile && (
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <strong>File to import:</strong> {csvFile.name} ({(csvFile.size / 1024).toFixed(2)} KB)
                  </p>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* Original Step Indicator and content */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center">
                  {!session && (
                    <>
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                        step >= -1 ? 'bg-blue-600 border-blue-600 text-white' : 'border-gray-300 dark:border-gray-600 text-gray-300 dark:text-gray-500'
                      }`}>
                        1
                      </div>
                      <div className={`h-1 w-20 mx-2 ${step > -1 ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                    </>
                  )}
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step >= 0 ? 'bg-blue-600 border-blue-600 text-white' : 'border-gray-300 dark:border-gray-600 text-gray-300 dark:text-gray-500'
                  }`}>
                    {session ? '1' : '2'}
                  </div>
                  {false && (
                    <>
                      <div className={`h-1 w-20 mx-2 ${step > 0 ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                        step >= 1 ? 'bg-blue-600 border-blue-600 text-white' : 'border-gray-300 dark:border-gray-600 text-gray-300 dark:text-gray-500'
                      }`}>
                        {session ? '2' : '3'}
                      </div>
                    </>
                  )}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {session ? (
                    `Step ${step + 1} of 2`
                  ) : (
                    `Step ${step + 2} of 3`
                  )}: {
                    step === -1 ? 'Select Experiment' :
                    step === 0 ? 'Select Evaluators' :
                    step === 1 ? 'Select Dataset (Optional)' :
                    'Configure'
                  }
                </div>
              </div>

          {/* Step -1: Select Experiment (only if no session pre-selected) */}
          {step === -1 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Experiment</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                Choose which experiment you want to run evaluations on. Only completed experiments with test results are shown.
              </p>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testSessions.map(session => (
                  <div
                    key={session.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedTestSession?.id === session.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedTestSession(session)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-gray-900 dark:text-white">{session.name}</h4>
                          {session.dataset_id && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300">
                              📊 Has Dataset
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{session.description}</p>
                        <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mr-2">{session.agent_config?.name}</span>
                          {session.dataset_name && (
                            <span className="bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded mr-2">
                              Dataset: {session.dataset_name}
                            </span>
                          )}
                          <span>{session.results?.total_tests || session.dataset_size || 0} tests</span>
                          <span className="ml-2 text-green-600 dark:text-green-400">
                            {session.status === 'completed' ? '✓ Ready for evaluation' : session.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {testSessions.length === 0 && (
                <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No experiments available</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Create and run experiments first to have test results available for evaluation.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Step 0: Select Evaluators */}
          {step === 0 && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Select Evaluators</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    Choose which evaluators to run on your experiment data. Each evaluator provides different insights into quality, safety, and performance.
                  </p>
                </div>
                {selectedEvaluators.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      {selectedEvaluators.length} evaluator{selectedEvaluators.length !== 1 ? 's' : ''} selected
                    </span>
                    <button
                      onClick={() => {
                        setSelectedEvaluators([]);
                        localStorage.removeItem('evaluation_modal_selected_evaluators');
                      }}
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300"
                    >
                      Clear selection
                    </button>
                  </div>
                )}
              </div>
              
              {false && (
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <strong>Dataset required:</strong> Some selected evaluators need a dataset for comparison. You'll select a dataset in the next step.
                  </p>
                </div>
              )}
              
              {categories.map(category => {
                const categoryEvaluators = getEvaluatorsByCategory(category);
                if (categoryEvaluators.length === 0) return null;
                
                return (
                  <div key={category} className="mb-6">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3 capitalize">
                      {category} ({categoryEvaluators.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {categoryEvaluators.map(evaluator => {
                        const isSelected = selectedEvaluators.some(e => e.id === evaluator.id);
                        return (
                          <div
                            key={evaluator.id}
                            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                              isSelected 
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                                : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                            }`}
                            onClick={() => toggleEvaluatorSelection(evaluator)}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h5 className="font-medium text-gray-900 dark:text-white text-sm">{evaluator.name}</h5>
                              <span className={`text-xs px-2 py-1 rounded-full border ${getCategoryColor(evaluator.category)}`}>
                                {evaluator.category}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{evaluator.description}</p>
                            {evaluator.supported_modes && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                                Supports: {evaluator.supported_modes.join(', ')}
                              </div>
                            )}
                            {isSelected && (
                              <div className="mt-2 text-blue-600 dark:text-blue-400 text-sm">✓ Selected</div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}

              {evaluators.length === 0 && (
                <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No evaluators available</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Configure evaluation evaluators to run evaluations.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Step 1: Dataset Selection (optional) */}
          {step === 1 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Dataset</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                The selected evaluators require a dataset for comparison. Choose a dataset that matches your evaluation goals.
              </p>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {datasets.map(dataset => (
                  <div
                    key={dataset.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedDataset === dataset.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedDataset(dataset.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{dataset.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{dataset.description}</p>
                        <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mr-2">{dataset.type}</span>
                          <span>{dataset.record_count || 0} records</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {datasets.length === 0 && (
                <div className="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No datasets available</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Create datasets first to run dataset-based evaluations.
                  </p>
                </div>
              )}
            </div>
          )}
          </>
          )}

        </div>

        {/* Footer */}
        <div className="flex justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <div>
            {!importMode && (step > 0 || (step > -1 && !session)) && (
              <button
                onClick={() => setStep(step - 1)}
                className="inline-flex items-center px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                Previous
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            {importMode && (
              <button
                onClick={runEvaluation}
                disabled={!experimentName || loading}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Importing...' : 'Import Results'}
              </button>
            )}
            {step === -1 && (
              <button
                onClick={() => setStep(0)}
                disabled={!selectedTestSession}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next: Select Evaluators
              </button>
            )}
            {step === 0 && (
              <button
                onClick={runEvaluation}
                disabled={selectedEvaluators.length === 0 || loading}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Running...' : 'Run Evaluation'}
              </button>
            )}
            {false && (
              <button
                onClick={() => setStep(1)}
                disabled={selectedEvaluators.length === 0}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next: Select Dataset
              </button>
            )}
            {step === 1 && (
              <button
                onClick={runEvaluation}
                disabled={!selectedDataset || loading}
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Running...' : 'Run Evaluation'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default EvaluationModal;