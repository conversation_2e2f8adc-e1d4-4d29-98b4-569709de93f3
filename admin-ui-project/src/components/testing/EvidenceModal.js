import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { api } from '../../utils/evaluationApi';

function EvidenceModal({ session, onClose }) {
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTestResults();
  }, [session.id]);

  const fetchTestResults = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/experiments/${session.id}/test-results`);
      
      // Extract test results from the test-results endpoint
      const results = response.data.results || [];
      setTestResults(results);
    } catch (error) {
      console.error('Error fetching test results:', error);
      setError('Failed to load test results');
    } finally {
      setLoading(false);
    }
  };


  const exportResults = () => {
    const csvContent = [
      ['Index', 'Test ID', 'Input', 'Expected Output', 'Actual Output', 'Latency (ms)', 'Tokens Used'],
      ...testResults.map((result, idx) => [
        idx + 1,
        result.test_case_id || `test_${idx + 1}`,
        typeof result.input === 'object' ? JSON.stringify(result.input) : result.input,
        result.expected_output || '',
        result.actual_output || '',
        result.latency_ms || 0,
        (result.token_usage?.input || 0) + (result.token_usage?.output || 0)
      ])
    ].map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${session.name.replace(/\s+/g, '_')}_results.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-7xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Experiment Results: {session.name}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {testResults.length} test results • {session.dataset_name || 'No dataset linked'}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={exportResults}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <ArrowDownTrayIcon className="h-4 w-4 inline mr-2" />
              Export CSV
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content - Table View */}
        <div className="flex-1 overflow-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500 dark:text-gray-400">Loading test results...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-red-500">{error}</div>
            </div>
          ) : testResults.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500 dark:text-gray-400">No test results found</div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Test ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Input
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Expected Output
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actual Output
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Latency
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Tokens
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {testResults.map((result, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {result.id ? result.id.split('-')[0] : `test_${index + 1}`}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        <div className="max-w-xs truncate" title={typeof result.input === 'object' ? JSON.stringify(result.input) : result.input}>
                          {typeof result.input === 'object' 
                            ? JSON.stringify(result.input).substring(0, 50) + '...'
                            : (result.input || '-').substring(0, 50) + (result.input?.length > 50 ? '...' : '')}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        <div className="max-w-xs truncate" title={result.expected_output}>
                          {(result.expected_output || '-').substring(0, 50) + (result.expected_output?.length > 50 ? '...' : '')}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        <div className="max-w-xs truncate" title={result.actual_output}>
                          {(result.actual_output || '-').substring(0, 50) + (result.actual_output?.length > 50 ? '...' : '')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {result.latency_ms ? `${result.latency_ms}ms` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {result.token_usage 
                          ? (result.token_usage.input + result.token_usage.output)
                          : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default EvidenceModal;