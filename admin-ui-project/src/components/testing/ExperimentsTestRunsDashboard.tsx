import React, { useState, useMemo } from 'react';
import { Search, Filter, Plus, Edit, Trash2, ChevronDown, Eye, Settings, Download, Upload, Tag, Calendar, Users, Database, FileText, BarChart3, CheckCircle, X, Play, Pause, Square, Clock, TrendingUp, <PERSON>ert<PERSON><PERSON>gle, Zap, Shield, Brain, Target } from 'lucide-react';

// Define data types for experiment management
interface Experiment {
  id: string;
  name: string;
  description: string;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'scheduled';
  agentId: string;
  agentName: string;
  datasetId: string;
  datasetName: string;
  metrics: string[];
  createdAt: string;
  startedAt: string;
  completedAt?: string;
  createdBy: string;
  testRuns: TestRun[];
  progress: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  department: string;
  tags: string[];
}

interface TestRun {
  id: string;
  experimentId: string;
  status: 'running' | 'completed' | 'failed' | 'pending';
  startedAt: string;
  completedAt?: string;
  duration: number;
  results: TestResult[];
  metrics: MetricResult[];
}

interface TestResult {
  metricName: string;
  value: number;
  threshold: number;
  passed: boolean;
  details: string;
}

interface MetricResult {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
}

// Create realistic healthcare-focused dummy data
const DUMMY_EXPERIMENTS: Experiment[] = [
  {
    id: 'exp_001',
    name: 'ScribeAI Clinical Accuracy Validation',
    description: 'Comprehensive validation of ScribeAI clinical documentation accuracy',
    status: 'completed',
    agentId: 'agent_001',
    agentName: 'ScribeAI Pro',
    datasetId: 'dataset_001',
    datasetName: 'Emergency Department Clinical Notes v2.1',
    metrics: ['Clinical Accuracy Score', 'Patient Safety Index', 'Response Time Efficiency'],
    createdAt: '2024-12-10',
    startedAt: '2024-12-10T09:00:00Z',
    completedAt: '2024-12-10T17:30:00Z',
    createdBy: 'Dr. Sarah Chen',
    progress: 100,
    priority: 'high',
    department: 'Emergency Medicine',
    tags: ['Clinical', 'Accuracy', 'Validation'],
    testRuns: [
      {
        id: 'run_001',
        experimentId: 'exp_001',
        status: 'completed',
        startedAt: '2024-12-10T09:00:00Z',
        completedAt: '2024-12-10T17:30:00Z',
        duration: 30600,
        results: [
          { metricName: 'Clinical Accuracy Score', value: 94.2, threshold: 85, passed: true, details: 'Excellent accuracy performance' },
          { metricName: 'Patient Safety Index', value: 96.8, threshold: 95, passed: true, details: 'Safety requirements met' },
          { metricName: 'Response Time Efficiency', value: 2.1, threshold: 5, passed: true, details: 'Fast response times' }
        ],
        metrics: [
          { name: 'Accuracy', value: 94.2, unit: '%', trend: 'up' },
          { name: 'Safety', value: 96.8, unit: '%', trend: 'stable' },
          { name: 'Response Time', value: 2.1, unit: 's', trend: 'down' }
        ]
      }
    ]
  },
  {
    id: 'exp_002',
    name: 'DiagnosticBot Bias Detection Test',
    description: 'Comprehensive bias detection testing for diagnostic support system',
    status: 'running',
    agentId: 'agent_002',
    agentName: 'DiagnosticBot v3',
    datasetId: 'dataset_002',
    datasetName: 'Chest X-Ray Images Dataset',
    metrics: ['Bias Detection Framework', 'Clinical Accuracy Score', 'Fairness Metrics'],
    createdAt: '2024-12-15',
    startedAt: '2024-12-15T08:00:00Z',
    createdBy: 'Dr. Michael Rodriguez',
    progress: 65,
    priority: 'critical',
    department: 'Radiology',
    tags: ['Bias Detection', 'Fairness', 'Diagnostic'],
    testRuns: [
      {
        id: 'run_002',
        experimentId: 'exp_002',
        status: 'running',
        startedAt: '2024-12-15T08:00:00Z',
        duration: 23400,
        results: [
          { metricName: 'Bias Detection Framework', value: 0.03, threshold: 0.05, passed: true, details: 'Low bias detected' },
          { metricName: 'Clinical Accuracy Score', value: 91.5, threshold: 85, passed: true, details: 'Good accuracy performance' }
        ],
        metrics: [
          { name: 'Bias Score', value: 0.03, unit: '', trend: 'down' },
          { name: 'Accuracy', value: 91.5, unit: '%', trend: 'up' }
        ]
      }
    ]
  },
  {
    id: 'exp_003',
    name: 'PatientChatbot Safety Assessment',
    description: 'Safety evaluation for patient communication chatbot',
    status: 'scheduled',
    agentId: 'agent_003',
    agentName: 'PatientChatbot',
    datasetId: 'dataset_003',
    datasetName: 'Patient Vital Signs Time Series',
    metrics: ['Patient Safety Index', 'HIPAA Compliance Checker', 'Response Time Efficiency'],
    createdAt: '2024-12-14',
    startedAt: '2024-12-16T10:00:00Z',
    createdBy: 'Dr. Emily Watson',
    progress: 0,
    priority: 'medium',
    department: 'Patient Services',
    tags: ['Safety', 'Patient Care', 'Compliance'],
    testRuns: []
  }
];

export const ExperimentsTestRunsDashboard: React.FC = () => {
  const [experiments] = useState<Experiment[]>(DUMMY_EXPERIMENTS);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'progress' | 'priority'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Filter and sort experiments
  const filteredAndSortedExperiments = useMemo(() => {
    let filtered = experiments.filter(experiment => {
      const matchesSearch = experiment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           experiment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           experiment.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesStatus = filterStatus === 'all' || experiment.status === filterStatus;
      const matchesPriority = filterPriority === 'all' || experiment.priority === filterPriority;
      
      return matchesSearch && matchesStatus && matchesPriority;
    });

    // Sort experiments
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'createdAt') {
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [experiments, searchTerm, filterStatus, filterPriority, sortBy, sortOrder]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'completed': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'failed': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'paused': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'scheduled': return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 dark:text-red-400';
      case 'high': return 'text-orange-600 dark:text-orange-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'failed': return <AlertTriangle className="w-4 h-4" />;
      case 'paused': return <Pause className="w-4 h-4" />;
      case 'scheduled': return <Clock className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Experiments & Test Runs</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Manage AI model experiments and analyze test run results</p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Experiment
        </button>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search experiments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Status</option>
          <option value="running">Running</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
          <option value="paused">Paused</option>
          <option value="scheduled">Scheduled</option>
        </select>

        <select
          value={filterPriority}
          onChange={(e) => setFilterPriority(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Priorities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>

        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('-');
            setSortBy(field as any);
            setSortOrder(order as any);
          }}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="createdAt-desc">Newest First</option>
          <option value="createdAt-asc">Oldest First</option>
          <option value="name-asc">Name (A-Z)</option>
          <option value="name-desc">Name (Z-A)</option>
          <option value="progress-desc">Progress (High-Low)</option>
          <option value="priority-desc">Priority (High-Low)</option>
        </select>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Experiments</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{experiments.length}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-primary" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Running</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {experiments.filter(e => e.status === 'running').length}
              </p>
            </div>
            <Play className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {experiments.filter(e => e.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Math.round((experiments.filter(e => e.status === 'completed').length / experiments.length) * 100)}%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Experiments List */}
      <div className="space-y-4">
        {filteredAndSortedExperiments.map(experiment => (
          <div 
            key={experiment.id}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(experiment.status)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {experiment.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {experiment.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(experiment.status)}`}>
                  {experiment.status}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(experiment.priority)}`}>
                  {experiment.priority}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Agent:</span>
                <p className="text-gray-900 dark:text-gray-100 font-medium">{experiment.agentName}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Dataset:</span>
                <p className="text-gray-900 dark:text-gray-100 font-medium">{experiment.datasetName}</p>
              </div>
              <div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Department:</span>
                <p className="text-gray-900 dark:text-gray-100 font-medium">{experiment.department}</p>
              </div>
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-500 dark:text-gray-400">Progress</span>
                <span className="text-gray-900 dark:text-gray-100">{experiment.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ width: `${experiment.progress}%` }}
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {experiment.tags.slice(0, 3).map(tag => (
                  <span key={tag} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                    {tag}
                  </span>
                ))}
                {experiment.tags.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                    +{experiment.tags.length - 3}
                  </span>
                )}
              </div>
              
              <div className="flex gap-2">
                <button 
                  onClick={() => setSelectedExperiment(experiment)}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  View Details
                </button>
                {experiment.status === 'running' && (
                  <button className="px-3 py-1 bg-yellow-200 dark:bg-yellow-700 text-yellow-800 dark:text-yellow-200 rounded text-sm hover:bg-yellow-300 dark:hover:bg-yellow-600 transition-colors">
                    Pause
                  </button>
                )}
                {experiment.status === 'paused' && (
                  <button className="px-3 py-1 bg-green-200 dark:bg-green-700 text-green-800 dark:text-green-200 rounded text-sm hover:bg-green-300 dark:hover:bg-green-600 transition-colors">
                    Resume
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Experiment Detail Modal */}
      {selectedExperiment && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {selectedExperiment.name}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{selectedExperiment.description}</p>
              </div>
              <button 
                onClick={() => setSelectedExperiment(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Experiment Information */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Experiment Details</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Status:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedExperiment.status)}`}>
                        {selectedExperiment.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Priority:</span>
                      <span className={`font-medium ${getPriorityColor(selectedExperiment.priority)}`}>
                        {selectedExperiment.priority}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Agent:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedExperiment.agentName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Dataset:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedExperiment.datasetName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Department:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedExperiment.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Created By:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedExperiment.createdBy}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Metrics</h3>
                  <div className="space-y-2">
                    {selectedExperiment.metrics.map(metric => (
                      <div key={metric} className="flex items-center gap-2">
                        <Target className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-900 dark:text-gray-100">{metric}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Test Runs */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Test Runs</h3>
                  <div className="space-y-3">
                    {selectedExperiment.testRuns.map(run => (
                      <div key={run.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium text-gray-900 dark:text-gray-100">Run {run.id}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(run.status)}`}>
                            {run.status}
                          </span>
                        </div>
                        
                        <div className="space-y-2 mb-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500 dark:text-gray-400">Duration:</span>
                            <span className="text-gray-900 dark:text-gray-100">
                              {Math.floor(run.duration / 60)}m {run.duration % 60}s
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500 dark:text-gray-400">Started:</span>
                            <span className="text-gray-900 dark:text-gray-100">
                              {new Date(run.startedAt).toLocaleString()}
                            </span>
                          </div>
                        </div>
                        
                        {run.results.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Results</h4>
                            <div className="space-y-1">
                              {run.results.map((result, index) => (
                                <div key={index} className="flex justify-between items-center text-sm">
                                  <span className="text-gray-600 dark:text-gray-400">{result.metricName}</span>
                                  <div className="flex items-center gap-2">
                                    <span className="text-gray-900 dark:text-gray-100">{result.value}</span>
                                    <span className={`px-1 py-0.5 text-xs rounded ${result.passed ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'}`}>
                                      {result.passed ? 'PASS' : 'FAIL'}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                <Download className="w-4 h-4 inline mr-2" />
                Export Results
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Edit className="w-4 h-4 inline mr-2" />
                Edit Experiment
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Experiment Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">New Experiment</h2>
              <button 
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Experiment Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter experiment name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Describe the experiment purpose and methodology"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Agent
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select agent</option>
                    <option value="agent_001">ScribeAI Pro</option>
                    <option value="agent_002">DiagnosticBot v3</option>
                    <option value="agent_003">PatientChatbot</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dataset
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select dataset</option>
                    <option value="dataset_001">Emergency Department Clinical Notes</option>
                    <option value="dataset_002">Chest X-Ray Images Dataset</option>
                    <option value="dataset_003">Patient Vital Signs Time Series</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Priority
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                Create Experiment
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 