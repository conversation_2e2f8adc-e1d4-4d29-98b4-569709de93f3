import React, { useState, useMemo } from 'react';
import { Search, Filter, Plus, Edit, Trash2, ChevronDown, Eye, Settings, Download, Upload, Tag, Calendar, Users, Database, FileText, BarChart3, CheckCircle, X, Target, TrendingUp, AlertTriangle, Zap, Shield, Brain } from 'lucide-react';

// Define data types for evaluation metrics
interface EvaluationMetric {
  id: string;
  name: string;
  description: string;
  category: 'accuracy' | 'safety' | 'fairness' | 'robustness' | 'efficiency' | 'compliance';
  type: 'classification' | 'regression' | 'generation' | 'detection' | 'ranking' | 'custom';
  status: 'active' | 'deprecated' | 'draft' | 'under_review';
  version: string;
  createdAt: string;
  lastUpdated: string;
  createdBy: string;
  usageCount: number;
  tags: string[];
  complexity: 'low' | 'medium' | 'high';
  implementationLanguage: string;
  documentationUrl: string;
  exampleCode: string;
  parameters: MetricParameter[];
  thresholds: Threshold[];
  department: string;
  validationStatus: 'validated' | 'pending' | 'failed' | 'in_progress';
  performanceScore: number;
}

interface MetricParameter {
  name: string;
  type: 'number' | 'string' | 'boolean' | 'array';
  description: string;
  defaultValue: any;
  required: boolean;
}

interface Threshold {
  level: 'critical' | 'warning' | 'acceptable';
  value: number;
  description: string;
}

// Create realistic healthcare-focused dummy data
const DUMMY_EVALUATION_METRICS: EvaluationMetric[] = [
  {
    id: 'metric_001',
    name: 'Clinical Accuracy Score',
    description: 'Comprehensive accuracy evaluation for clinical decision support systems',
    category: 'accuracy',
    type: 'classification',
    status: 'active',
    version: '2.1.4',
    createdAt: '2024-10-15',
    lastUpdated: '2024-12-15T10:30:00Z',
    createdBy: 'Dr. Sarah Chen',
    usageCount: 156,
    tags: ['Clinical', 'Accuracy', 'Decision Support'],
    complexity: 'high',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/clinical-accuracy',
    exampleCode: 'from vitea.metrics import ClinicalAccuracyScore\n\nmetric = ClinicalAccuracyScore()\nresult = metric.evaluate(predictions, ground_truth)',
    parameters: [
      {
        name: 'confidence_threshold',
        type: 'number',
        description: 'Minimum confidence threshold for predictions',
        defaultValue: 0.8,
        required: false
      },
      {
        name: 'include_uncertainty',
        type: 'boolean',
        description: 'Include uncertainty analysis in scoring',
        defaultValue: true,
        required: false
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 0.85,
        description: 'Minimum acceptable accuracy for clinical use'
      },
      {
        level: 'warning',
        value: 0.90,
        description: 'Recommended accuracy threshold'
      },
      {
        level: 'acceptable',
        value: 0.95,
        description: 'Excellent accuracy level'
      }
    ],
    department: 'Clinical Decision Support',
    validationStatus: 'validated',
    performanceScore: 94
  },
  {
    id: 'metric_002',
    name: 'Patient Safety Index',
    description: 'Multi-dimensional safety evaluation for patient-facing AI systems',
    category: 'safety',
    type: 'custom',
    status: 'active',
    version: '1.8.2',
    createdAt: '2024-11-20',
    lastUpdated: '2024-12-14T15:45:00Z',
    createdBy: 'Dr. Michael Rodriguez',
    usageCount: 89,
    tags: ['Safety', 'Patient Care', 'Risk Assessment'],
    complexity: 'high',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/patient-safety',
    exampleCode: 'from vitea.metrics import PatientSafetyIndex\n\nsafety_metric = PatientSafetyIndex()\nsafety_score = safety_metric.evaluate(model_outputs)',
    parameters: [
      {
        name: 'risk_factors',
        type: 'array',
        description: 'List of risk factors to consider',
        defaultValue: ['medication_interactions', 'allergies', 'contraindications'],
        required: true
      },
      {
        name: 'severity_weights',
        type: 'array',
        description: 'Weights for different severity levels',
        defaultValue: [0.3, 0.5, 0.2],
        required: false
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 0.95,
        description: 'Minimum safety threshold for deployment'
      },
      {
        level: 'warning',
        value: 0.98,
        description: 'Recommended safety level'
      },
      {
        level: 'acceptable',
        value: 0.99,
        description: 'Excellent safety performance'
      }
    ],
    department: 'Patient Safety',
    validationStatus: 'validated',
    performanceScore: 96
  },
  {
    id: 'metric_003',
    name: 'Bias Detection Framework',
    description: 'Comprehensive bias detection and fairness evaluation for healthcare AI',
    category: 'fairness',
    type: 'detection',
    status: 'active',
    version: '3.0.1',
    createdAt: '2024-09-10',
    lastUpdated: '2024-12-13T09:15:00Z',
    createdBy: 'Dr. Emily Watson',
    usageCount: 234,
    tags: ['Fairness', 'Bias Detection', 'Ethics'],
    complexity: 'medium',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/bias-detection',
    exampleCode: 'from vitea.metrics import BiasDetectionFramework\n\nbias_detector = BiasDetectionFramework()\nbias_report = bias_detector.analyze(model, test_data)',
    parameters: [
      {
        name: 'protected_attributes',
        type: 'array',
        description: 'Protected attributes to analyze for bias',
        defaultValue: ['age', 'gender', 'race', 'ethnicity'],
        required: true
      },
      {
        name: 'statistical_tests',
        type: 'array',
        description: 'Statistical tests to perform',
        defaultValue: ['demographic_parity', 'equalized_odds', 'calibration'],
        required: false
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 0.1,
        description: 'Maximum acceptable bias level'
      },
      {
        level: 'warning',
        value: 0.05,
        description: 'Recommended bias threshold'
      },
      {
        level: 'acceptable',
        value: 0.02,
        description: 'Excellent fairness performance'
      }
    ],
    department: 'AI Ethics',
    validationStatus: 'validated',
    performanceScore: 91
  },
  {
    id: 'metric_004',
    name: 'Adversarial Robustness Test',
    description: 'Evaluation of model robustness against adversarial attacks',
    category: 'robustness',
    type: 'custom',
    status: 'under_review',
    version: '1.2.0',
    createdAt: '2024-12-01',
    lastUpdated: '2024-12-15T14:20:00Z',
    createdBy: 'Dr. James Thompson',
    usageCount: 45,
    tags: ['Robustness', 'Security', 'Adversarial'],
    complexity: 'high',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/adversarial-robustness',
    exampleCode: 'from vitea.metrics import AdversarialRobustnessTest\n\nrobustness_test = AdversarialRobustnessTest()\nrobustness_score = robustness_test.evaluate(model)',
    parameters: [
      {
        name: 'attack_types',
        type: 'array',
        description: 'Types of adversarial attacks to test',
        defaultValue: ['fgsm', 'pgd', 'carlini_wagner'],
        required: true
      },
      {
        name: 'epsilon_values',
        type: 'array',
        description: 'Perturbation magnitudes to test',
        defaultValue: [0.01, 0.05, 0.1],
        required: false
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 0.7,
        description: 'Minimum robustness threshold'
      },
      {
        level: 'warning',
        value: 0.8,
        description: 'Recommended robustness level'
      },
      {
        level: 'acceptable',
        value: 0.9,
        description: 'Excellent robustness performance'
      }
    ],
    department: 'Cybersecurity',
    validationStatus: 'in_progress',
    performanceScore: 87
  },
  {
    id: 'metric_005',
    name: 'Response Time Efficiency',
    description: 'Evaluation of model response time and computational efficiency',
    category: 'efficiency',
    type: 'regression',
    status: 'active',
    version: '2.5.3',
    createdAt: '2024-08-25',
    lastUpdated: '2024-12-12T11:30:00Z',
    createdBy: 'Dr. Lisa Park',
    usageCount: 178,
    tags: ['Efficiency', 'Performance', 'Response Time'],
    complexity: 'low',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/response-time',
    exampleCode: 'from vitea.metrics import ResponseTimeEfficiency\n\nefficiency_metric = ResponseTimeEfficiency()\ntiming_results = efficiency_metric.evaluate(model, test_data)',
    parameters: [
      {
        name: 'timeout_threshold',
        type: 'number',
        description: 'Maximum acceptable response time in seconds',
        defaultValue: 5.0,
        required: true
      },
      {
        name: 'batch_size',
        type: 'number',
        description: 'Batch size for efficiency testing',
        defaultValue: 32,
        required: false
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 10.0,
        description: 'Maximum acceptable response time'
      },
      {
        level: 'warning',
        value: 5.0,
        description: 'Recommended response time'
      },
      {
        level: 'acceptable',
        value: 2.0,
        description: 'Excellent response time'
      }
    ],
    department: 'Performance Engineering',
    validationStatus: 'validated',
    performanceScore: 89
  },
  {
    id: 'metric_006',
    name: 'HIPAA Compliance Checker',
    description: 'Automated evaluation of HIPAA compliance for healthcare AI systems',
    category: 'compliance',
    type: 'detection',
    status: 'active',
    version: '1.0.8',
    createdAt: '2024-06-15',
    lastUpdated: '2024-11-30T16:45:00Z',
    createdBy: 'Dr. Robert Kim',
    usageCount: 203,
    tags: ['Compliance', 'HIPAA', 'Privacy'],
    complexity: 'medium',
    implementationLanguage: 'Python',
    documentationUrl: '/docs/metrics/hipaa-compliance',
    exampleCode: 'from vitea.metrics import HIPAAComplianceChecker\n\ncompliance_checker = HIPAAComplianceChecker()\ncompliance_report = compliance_checker.evaluate(system)',
    parameters: [
      {
        name: 'phi_detection',
        type: 'boolean',
        description: 'Enable PHI detection and redaction',
        defaultValue: true,
        required: true
      },
      {
        name: 'audit_logging',
        type: 'boolean',
        description: 'Enable comprehensive audit logging',
        defaultValue: true,
        required: true
      }
    ],
    thresholds: [
      {
        level: 'critical',
        value: 1.0,
        description: 'Full compliance required'
      },
      {
        level: 'warning',
        value: 0.95,
        description: 'Near-complete compliance'
      },
      {
        level: 'acceptable',
        value: 1.0,
        description: 'Perfect compliance'
      }
    ],
    department: 'Compliance',
    validationStatus: 'validated',
    performanceScore: 98
  }
];

export const EvaluationsLibrary: React.FC = () => {
  const [metrics] = useState<EvaluationMetric[]>(DUMMY_EVALUATION_METRICS);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMetric, setSelectedMetric] = useState<EvaluationMetric | null>(null);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'usageCount' | 'performanceScore'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Filter and sort metrics
  const filteredAndSortedMetrics = useMemo(() => {
    let filtered = metrics.filter(metric => {
      const matchesSearch = metric.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           metric.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           metric.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = filterCategory === 'all' || metric.category === filterCategory;
      const matchesStatus = filterStatus === 'all' || metric.status === filterStatus;
      
      return matchesSearch && matchesCategory && matchesStatus;
    });

    // Sort metrics
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      if (sortBy === 'createdAt') {
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [metrics, searchTerm, filterCategory, filterStatus, sortBy, sortOrder]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'deprecated': return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
      case 'draft': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'under_review': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'accuracy': return <Target className="w-4 h-4" />;
      case 'safety': return <Shield className="w-4 h-4" />;
      case 'fairness': return <Users className="w-4 h-4" />;
      case 'robustness': return <Zap className="w-4 h-4" />;
      case 'efficiency': return <TrendingUp className="w-4 h-4" />;
      case 'compliance': return <CheckCircle className="w-4 h-4" />;
      default: return <BarChart3 className="w-4 h-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'high': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600 dark:text-green-400';
    if (score >= 80) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Evaluations Library</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Repository of evaluation metrics and scoring methods for AI model testing</p>
        </div>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Metric
        </button>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search metrics..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Categories</option>
          <option value="accuracy">Accuracy</option>
          <option value="safety">Safety</option>
          <option value="fairness">Fairness</option>
          <option value="robustness">Robustness</option>
          <option value="efficiency">Efficiency</option>
          <option value="compliance">Compliance</option>
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="deprecated">Deprecated</option>
          <option value="draft">Draft</option>
          <option value="under_review">Under Review</option>
        </select>

        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('-');
            setSortBy(field as any);
            setSortOrder(order as any);
          }}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="name-asc">Name (A-Z)</option>
          <option value="name-desc">Name (Z-A)</option>
          <option value="createdAt-desc">Newest First</option>
          <option value="createdAt-asc">Oldest First</option>
          <option value="usageCount-desc">Usage (High-Low)</option>
          <option value="performanceScore-desc">Performance (High-Low)</option>
        </select>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Metrics</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics.length}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-primary" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Metrics</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {metrics.filter(m => m.status === 'active').length}
              </p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Usage</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {metrics.reduce((sum, m) => sum + m.usageCount, 0).toLocaleString()}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Performance</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Math.round(metrics.reduce((sum, m) => sum + m.performanceScore, 0) / metrics.length)}
              </p>
            </div>
            <Target className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedMetrics.map(metric => (
          <div 
            key={metric.id}
            onClick={() => setSelectedMetric(metric)}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 cursor-pointer hover:shadow-lg transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                {getCategoryIcon(metric.category)}
                <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                  {metric.category}
                </span>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(metric.status)}`}>
                {metric.status.replace('_', ' ')}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {metric.name}
            </h3>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
              {metric.description}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Type:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium capitalize">
                  {metric.type.replace('_', ' ')}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Complexity:</span>
                <span className={`font-medium ${getComplexityColor(metric.complexity)}`}>
                  {metric.complexity}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Usage:</span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">
                  {metric.usageCount}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">Performance:</span>
                <span className={`font-medium ${getPerformanceColor(metric.performanceScore)}`}>
                  {metric.performanceScore}%
                </span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-1">
              {metric.tags.slice(0, 2).map(tag => (
                <span key={tag} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                  {tag}
                </span>
              ))}
              {metric.tags.length > 2 && (
                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                  +{metric.tags.length - 2}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Metric Detail Modal */}
      {selectedMetric && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {selectedMetric.name}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{selectedMetric.description}</p>
              </div>
              <button 
                onClick={() => setSelectedMetric(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Metric Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Category:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedMetric.category}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Type:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedMetric.type.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Status:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedMetric.status)}`}>
                        {selectedMetric.status.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Version:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedMetric.version}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Department:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedMetric.department}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Language:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedMetric.implementationLanguage}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Performance Metrics</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Performance Score:</span>
                      <span className={`font-medium ${getPerformanceColor(selectedMetric.performanceScore)}`}>
                        {selectedMetric.performanceScore}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Usage Count:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedMetric.usageCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Complexity:</span>
                      <span className={`font-medium ${getComplexityColor(selectedMetric.complexity)}`}>
                        {selectedMetric.complexity}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Validation Status:</span>
                      <span className="text-gray-900 dark:text-gray-100 capitalize">
                        {selectedMetric.validationStatus.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Parameters and Thresholds */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Parameters</h3>
                  <div className="space-y-3">
                    {selectedMetric.parameters.map((param, index) => (
                      <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium text-gray-900 dark:text-gray-100">{param.name}</span>
                          <span className="text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded">
                            {param.type}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{param.description}</p>
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-500 dark:text-gray-400">Default: {JSON.stringify(param.defaultValue)}</span>
                          <span className={`px-2 py-1 rounded ${param.required ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'}`}>
                            {param.required ? 'Required' : 'Optional'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Thresholds</h3>
                  <div className="space-y-2">
                    {selectedMetric.thresholds.map((threshold, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                        <span className="text-sm text-gray-900 dark:text-gray-100 capitalize">{threshold.level}</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{threshold.value}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">{threshold.description}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Code Example and Tags */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Example Code</h3>
                  <div className="bg-gray-900 dark:bg-gray-900 rounded-lg p-4">
                    <pre className="text-sm text-gray-100 overflow-x-auto">
                      <code>{selectedMetric.exampleCode}</code>
                    </pre>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedMetric.tags.map(tag => (
                      <span key={tag} className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Timeline</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Created:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {new Date(selectedMetric.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {new Date(selectedMetric.lastUpdated).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Created By:</span>
                      <span className="text-gray-900 dark:text-gray-100">{selectedMetric.createdBy}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                <Download className="w-4 h-4 inline mr-2" />
                Export
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Edit className="w-4 h-4 inline mr-2" />
                Edit Metric
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Metric Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Add New Metric</h2>
              <button 
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Metric Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter metric name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Describe the metric purpose and methodology"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select category</option>
                    <option value="accuracy">Accuracy</option>
                    <option value="safety">Safety</option>
                    <option value="fairness">Fairness</option>
                    <option value="robustness">Robustness</option>
                    <option value="efficiency">Efficiency</option>
                    <option value="compliance">Compliance</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Type
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="">Select type</option>
                    <option value="classification">Classification</option>
                    <option value="regression">Regression</option>
                    <option value="generation">Generation</option>
                    <option value="detection">Detection</option>
                    <option value="ranking">Ranking</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add Metric
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 