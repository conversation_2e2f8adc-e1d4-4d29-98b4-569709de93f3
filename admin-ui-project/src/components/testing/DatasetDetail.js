import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { 
  DocumentTextIcon, 
  PencilIcon, 
  TrashIcon, 
  CircleStackIcon,
  ClipboardDocumentListIcon,
  CalendarIcon,
  ArrowUpTrayIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { api } from '../../utils/evaluationApi';
import Breadcrumb from './Breadcrumb';

function DatasetDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [dataset, setDataset] = useState(null);
  const [datasetEntries, setDatasetEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  });
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    loadDataset();
    loadDatasetEntries();
  }, [id, pagination.page]);

  const loadDataset = async () => {
    try {
      const response = await api.get(`/datasets/${id}`);
      setDataset(response.data);
    } catch (error) {
      console.error('Error loading dataset:', error);
      setError('Failed to load dataset details');
    }
  };

  const loadDatasetEntries = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/datasets/${id}/preview?limit=1000`); // Get all entries
      setDatasetEntries(response.data.preview || []);
      setPagination(prev => ({ ...prev, total: response.data.total_records || 0 }));
    } catch (error) {
      console.error('Error loading dataset entries:', error);
      setError('Failed to load dataset entries');
    } finally {
      setLoading(false);
    }
  };

  const getTypeColor = (type) => {
    const normalizedType = type ? type.toLowerCase().trim() : '';
    switch (normalizedType) {
      case 'medical': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700';
      case 'qa': case 'q&a': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-700';
      case 'classification': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700';
      case 'generation': return 'bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 border-amber-200 dark:border-amber-700';
      case 'custom': return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600';
      default: return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-700';
    }
  };

  const getDatasetTypeLabel = (type) => {
    const normalizedType = type ? type.toLowerCase().trim() : '';
    switch (normalizedType) {
      case 'qa': case 'q&a': return 'Q&A';
      case 'medical': return 'Medical';
      case 'classification': return 'Classification';
      case 'generation': return 'Generation';
      case 'custom': return 'Custom';
      default: return 'Unknown';
    }
  };

  const formatValue = (value) => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  };

  const formatTestCaseInput = (entry) => {
    if (!entry.input) {
      return <span className="text-gray-400 dark:text-gray-500">No input</span>;
    }
    
    // If input is a string, display it directly
    if (typeof entry.input === 'string') {
      return formatValue(entry.input);
    }
    
    // If input is not an object, convert to string
    if (typeof entry.input !== 'object') {
      return formatValue(String(entry.input));
    }

    // Check if it's a conversational test case
    if (entry.input.scenario || entry.input.turns) {
      return (
        <div className="space-y-2">
          {entry.input.scenario && (
            <div>
              <span className="text-xs font-medium text-purple-600 dark:text-purple-400">Scenario:</span>
              <div className="text-sm mt-1 bg-purple-50 dark:bg-purple-900/20 p-2 rounded border">
                {entry.input.scenario}
              </div>
            </div>
          )}
          {entry.input.turns && entry.input.turns.length > 0 && (
            <div>
              <span className="text-xs font-medium text-purple-600 dark:text-purple-400">
                Conversation ({entry.input.turns.length} turns):
              </span>
              <div className="mt-1 space-y-1 bg-purple-50 dark:bg-purple-900/20 p-2 rounded border max-h-32 overflow-y-auto">
                {entry.input.turns.map((turn, index) => (
                  <div key={index} className="text-sm">
                    <span className={`font-medium ${
                      turn.role === 'user' ? 'text-blue-600 dark:text-blue-400' :
                      turn.role === 'assistant' ? 'text-green-600 dark:text-green-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`}>
                      {turn.role}:
                    </span>{' '}
                    <span className="text-gray-700 dark:text-gray-300">{turn.content}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    // Single-turn test case - check for various input field names
    if (entry.input.question) {
      return (
        <div>
          <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Question:</span>
          <div className="text-sm mt-1">{entry.input.question}</div>
        </div>
      );
    }
    
    if (entry.input.prompt) {
      return (
        <div>
          <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Prompt:</span>
          <div className="text-sm mt-1">{entry.input.prompt}</div>
        </div>
      );
    }
    
    if (entry.input.text) {
      return (
        <div>
          <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Text:</span>
          <div className="text-sm mt-1">{entry.input.text}</div>
        </div>
      );
    }
    
    // If only context exists without actual input, show a warning
    if (entry.input.context && Object.keys(entry.input).length === 1) {
      return (
        <div className="space-y-1">
          <div className="text-amber-600 dark:text-amber-400 text-xs font-medium">
            ⚠️ Missing actual input query
          </div>
          <div className="text-gray-500 dark:text-gray-400 text-xs">
            Context only: {entry.input.context}
          </div>
        </div>
      );
    }
    
    // If context exists with other fields, show them separately
    const hasContext = entry.input.context;
    const otherFields = Object.keys(entry.input).filter(key => key !== 'context');
    
    if (hasContext && otherFields.length > 0) {
      return (
        <div className="space-y-1">
          {otherFields.map(key => (
            <div key={key}>
              <span className="text-xs font-medium text-blue-600 dark:text-blue-400 capitalize">{key}:</span>
              <div className="text-sm">{formatValue(entry.input[key])}</div>
            </div>
          ))}
          <div className="text-xs text-gray-500 dark:text-gray-400 italic">
            Context: {entry.input.context}
          </div>
        </div>
      );
    }

    // If no specific field found, show the entire input object
    return formatValue(entry.input);
  };

  const formatTestCaseOutput = (entry) => {
    // For conversational test cases, show expected_outcome if available
    if (entry.expected_outcome) {
      return (
        <div>
          <span className="text-xs font-medium text-purple-600 dark:text-purple-400">Expected Outcome:</span>
          <div className="text-sm mt-1">{entry.expected_outcome}</div>
        </div>
      );
    }

    // For single-turn test cases, show expected_output
    return formatValue(entry.expected_output);
  };

  const getTestCaseType = (entry) => {
    // If input is an object with scenario or turns, it's conversational
    if (entry.input && typeof entry.input === 'object') {
      if (entry.input.scenario || entry.input.turns) {
        return 'conversational';
      }
    }
    // Otherwise it's single-turn (including string inputs)
    return 'single_turn';
  };

  const handleEditDataset = () => {
    setShowEditModal(true);
  };

  const handleSaveDataset = async (datasetData) => {
    try {
      await api.put(`/datasets/${id}`, datasetData);
      setShowEditModal(false);
      // Reload dataset and entries
      loadDataset();
      loadDatasetEntries();
      alert('Dataset updated successfully');
    } catch (error) {
      console.error('Error updating dataset:', error);
      alert('Failed to update dataset');
    }
  };

  const handleDeleteDataset = async () => {
    if (!dataset) return;
    
    const confirmDelete = window.confirm(
      `Are you sure you want to delete the dataset "${dataset.name}"? This action cannot be undone.`
    );
    
    if (!confirmDelete) return;
    
    try {
      await api.delete(`/datasets/${id}`);
      alert('Dataset deleted successfully');
      // Navigate back to dataset management page
      navigate('/datasets');
    } catch (error) {
      console.error('Error deleting dataset:', error);
      alert('Failed to delete dataset. Please try again.');
    }
  };

  const handleRunTestSession = () => {
    // Navigate to test sessions page and trigger modal with pre-selected dataset
    navigate(`/experiments?create=true&dataset=${id}`);
  };

  const handleExportDataset = async () => {
    try {
      const response = await api.get(`/datasets/${id}/preview?limit=10000`);
      const data = response.data.preview || [];
      
      // Convert to CSV
      if (data.length === 0) {
        alert('No data to export');
        return;
      }
      
      const headers = ['Input', 'Expected Output', 'Metadata'];
      const csvContent = [
        headers.join(','),
        ...data.map(row => [
          `"${JSON.stringify(row.input || {}).replace(/"/g, '""')}"`,
          `"${(row.expected_output || '').toString().replace(/"/g, '""')}"`,
          `"${JSON.stringify(row.metadata || {}).replace(/"/g, '""')}"`
        ].join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${dataset?.name || 'dataset'}-export.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting dataset:', error);
      alert('Failed to export dataset');
    }
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Dataset Management', href: '/datasets' },
    { name: dataset?.name || 'Loading...', href: `/datasets/${id}` }
  ];

  if (loading && !dataset) {
    return (
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"></div>
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <Breadcrumb items={breadcrumbItems} />
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md p-4">
          <p className="text-red-800 dark:text-red-300">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <Breadcrumb items={breadcrumbItems} />
      
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{dataset?.name}</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            {dataset?.description || 'No description available'}
          </p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleExportDataset}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
            Export
          </button>
          <button 
            onClick={handleEditDataset}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </button>
          <button 
            onClick={handleDeleteDataset}
            className="inline-flex items-center px-3 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </button>
          <button 
            onClick={handleRunTestSession}
            className="inline-flex items-center px-3 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600"
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Run Test Session
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <CircleStackIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{datasetEntries.length}</div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Entries</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg p-5">
          <div className="flex items-center">
            <div className={`flex-shrink-0 p-3 rounded-lg ${getTypeColor(dataset?.type)?.includes('red') ? 'bg-red-100 dark:bg-red-900/20' : 
                                                             getTypeColor(dataset?.type)?.includes('green') ? 'bg-green-100 dark:bg-green-900/20' :
                                                             getTypeColor(dataset?.type)?.includes('purple') ? 'bg-purple-100 dark:bg-purple-900/20' :
                                                             getTypeColor(dataset?.type)?.includes('amber') ? 'bg-amber-100 dark:bg-amber-900/20' :
                                                             getTypeColor(dataset?.type)?.includes('gray') ? 'bg-gray-100 dark:bg-gray-700' :
                                                             'bg-blue-100 dark:bg-blue-900/20'}`}>
              <ClipboardDocumentListIcon className={`h-6 w-6 ${getTypeColor(dataset?.type)?.includes('red') ? 'text-red-600 dark:text-red-400' : 
                                                              getTypeColor(dataset?.type)?.includes('green') ? 'text-green-600 dark:text-green-400' :
                                                              getTypeColor(dataset?.type)?.includes('purple') ? 'text-purple-600 dark:text-purple-400' :
                                                              getTypeColor(dataset?.type)?.includes('amber') ? 'text-amber-600 dark:text-amber-400' :
                                                              getTypeColor(dataset?.type)?.includes('gray') ? 'text-gray-600 dark:text-gray-400' :
                                                              'text-blue-600 dark:text-blue-400'}`} />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{getDatasetTypeLabel(dataset?.type)}</div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Dataset Type</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <CalendarIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {dataset?.created_at ? new Date(dataset.created_at).toLocaleDateString() : 'N/A'}
              </div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <DocumentTextIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {dataset?.status?.charAt(0).toUpperCase() + dataset?.status?.slice(1) || 'Active'}
              </div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</div>
            </div>
          </div>
        </div>
      </div>

      {/* Dataset Entries Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Dataset Entries</h3>
        </div>
        
        {loading ? (
          <div className="px-6 py-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading entries...</p>
          </div>
        ) : datasetEntries.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Input
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Expected Output
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Metadata
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {datasetEntries.map((entry, index) => {
                  return (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100 max-w-md">
                        <div className="break-words">
                          {formatTestCaseInput(entry)}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100 max-w-xs">
                        <div className="break-words">
                          {formatTestCaseOutput(entry)}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-xs">
                        <div className="break-words">
                          {entry.metadata && Object.keys(entry.metadata).length > 0 ? (
                            <pre className="whitespace-pre-wrap text-xs">
                              {JSON.stringify(entry.metadata, null, 2)}
                            </pre>
                          ) : (
                            <span className="text-gray-400 dark:text-gray-500">None</span>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="px-6 py-16 text-center">
            <DocumentTextIcon className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Entries Found</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              This dataset doesn't contain any entries yet.
            </p>
          </div>
        )}
      </div>

      {/* Edit Dataset Modal */}
      {showEditModal && dataset && (
        <DatasetFormModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={handleSaveDataset}
          dataset={dataset}
          title="Edit Dataset"
        />
      )}

    </div>
  );
}

// DatasetFormModal component (imported from DatasetManagement)
function DatasetFormModal({ isOpen, onClose, onSave, dataset, title }) {
  const [formData, setFormData] = React.useState({
    name: dataset?.name || '',
    type: dataset?.type || 'custom',
    description: dataset?.description || '',
    status: dataset?.status || 'active'
  });
  const [creationMethod, setCreationMethod] = React.useState(dataset ? 'manual' : 'csv'); // If editing, default to manual
  const [uploadedFile, setUploadedFile] = React.useState(null);
  const [fileData, setFileData] = React.useState(null);
  const [isDragOver, setIsDragOver] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [manualRows, setManualRows] = React.useState([
    { input: { question: '' }, expected_output: '', metadata: {} }
  ]);
  const [existingDataLoading, setExistingDataLoading] = React.useState(false);
  const [existingDataError, setExistingDataError] = React.useState(null);

  // Load existing dataset data when in edit mode
  React.useEffect(() => {
    const loadExistingData = async () => {
      if (dataset && dataset.id) {
        setExistingDataLoading(true);
        setExistingDataError(null);
        try {
          const response = await api.get(`/datasets/${dataset.id}/preview?limit=1000`); // Get all rows for editing
          const existingRows = response.data.preview || [];
          
          if (existingRows.length > 0) {
            setManualRows(existingRows.map(row => ({
              input: row.input || { question: '' },
              expected_output: row.expected_output || '',
              metadata: row.metadata || {}
            })));
          }
        } catch (error) {
          console.error('Error loading existing data:', error);
          setExistingDataError('Failed to load existing dataset data');
        } finally {
          setExistingDataLoading(false);
        }
      }
    };

    if (isOpen && dataset) {
      loadExistingData();
    }
  }, [isOpen, dataset]);

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      setUploadedFile(null);
      setFileData(null);
      setIsDragOver(false);
      setIsProcessing(false);
      setExistingDataError(null);
      if (!dataset) {
        setManualRows([{ input: { question: '' }, expected_output: '', metadata: {} }]);
        setFormData({
          name: '',
          type: 'custom',
          description: '',
          status: 'active'
        });
      }
    }
  }, [isOpen, dataset]);

  // Add more rows functionality
  const addRow = () => {
    setManualRows([...manualRows, { input: { question: '' }, expected_output: '', metadata: {} }]);
  };

  const removeRow = (index) => {
    if (manualRows.length > 1) {
      setManualRows(manualRows.filter((_, i) => i !== index));
    }
  };

  const updateRow = (index, field, value) => {
    const updatedRows = [...manualRows];
    if (field === 'input.question') {
      updatedRows[index].input.question = value;
    } else {
      updatedRows[index][field] = value;
    }
    setManualRows(updatedRows);
  };

  if (!isOpen) return null;

  const handleSubmit = async () => {
    // Validate form
    if (!formData.name.trim()) {
      alert('Dataset name is required');
      return;
    }

    let dataToSubmit;
    if (creationMethod === 'csv' && fileData) {
      if (!fileData.allMappedRows || fileData.allMappedRows.length === 0) {
        alert('No valid data found in uploaded file');
        return;
      }
      dataToSubmit = fileData.allMappedRows;
    } else {
      // Manual creation - validate and prepare data
      const validRows = manualRows.filter(row => 
        row.input.question?.trim() || Object.keys(row.input).some(key => key !== 'question' && row.input[key]?.trim())
      );
      
      if (validRows.length === 0) {
        alert('Please add at least one row with input data');
        return;
      }
      
      dataToSubmit = validRows;
    }

    try {
      const datasetData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        data: dataToSubmit
      };
      
      await onSave(datasetData);
    } catch (error) {
      console.error('Error saving dataset:', error);
      alert(`Failed to ${dataset ? 'update' : 'create'} dataset`);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">{title || 'Dataset Form'}</h2>
          <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 text-2xl">×</button>
        </div>

        <div className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Dataset Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Enter dataset name"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Type
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="custom">Custom</option>
                <option value="qa">Q&A</option>
                <option value="medical">Medical</option>
                <option value="classification">Classification</option>
                <option value="generation">Generation</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
              placeholder="Enter dataset description (optional)"
            />
          </div>

          {/* Manual Entry Mode */}
          <div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Manual entry mode: Create rows one by one
            </div>
            {existingDataLoading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 dark:border-blue-400 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading existing data...</p>
              </div>
            ) : existingDataError ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md p-4 mb-4">
                <p className="text-red-800 dark:text-red-300 text-sm">{existingDataError}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-300 dark:border-gray-600">
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Row</th>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Input Question</th>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Expected Output</th>
                      <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="text-sm">
                    {manualRows.map((row, index) => (
                      <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                        <td className="px-2 py-2 text-gray-700 dark:text-gray-300">{index + 1}</td>
                        <td className="px-2 py-2">
                          <input
                            type="text"
                            value={row.input.question || ''}
                            onChange={(e) => updateRow(index, 'input.question', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Enter question or input"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <input
                            type="text"
                            value={row.expected_output || ''}
                            onChange={(e) => updateRow(index, 'expected_output', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Enter expected output (optional)"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <button
                            onClick={() => removeRow(index)}
                            disabled={manualRows.length <= 1}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 disabled:text-gray-400 dark:disabled:text-gray-500 disabled:cursor-not-allowed"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="mt-4 flex items-center justify-between">
              <button
                onClick={addRow}
                className="px-3 py-2 text-sm text-blue-600 dark:text-blue-400 border border-blue-600 dark:border-blue-400 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                + Add Row
              </button>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {manualRows.length} row{manualRows.length !== 1 ? 's' : ''} defined
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 border border-transparent rounded-md hover:bg-blue-700 dark:hover:bg-blue-600"
          >
            {dataset ? 'Update Dataset' : 'Create Dataset'}
          </button>
        </div>
      </div>
    </div>
  );
}

// Import the existing CreateExperimentModal from Experiments
// This will be extracted to a shared component later

export default DatasetDetail;