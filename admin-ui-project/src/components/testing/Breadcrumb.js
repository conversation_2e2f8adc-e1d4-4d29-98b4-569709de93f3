import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

function Breadcrumb({ items }) {
  return (
    <nav className="flex mb-4" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        <li>
          <Link to="/" className="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
            <HomeIcon className="h-4 w-4" />
          </Link>
        </li>
        {items.map((item, index) => (
          <li key={item.name} className="flex items-center">
            <ChevronRightIcon className="h-3 w-3 text-gray-300 dark:text-gray-600 mx-1" />
            {index === items.length - 1 ? (
              <span className="text-gray-600 dark:text-gray-300 font-medium text-sm">{item.name}</span>
            ) : (
              <Link
                to={item.href}
                className="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 font-medium text-sm"
              >
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

export default Breadcrumb;