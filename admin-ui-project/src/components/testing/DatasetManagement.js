import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  CircleStackIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CheckCircleIcon,
  MagnifyingGlassIcon,
  CloudArrowUpIcon,
  ArrowUpTrayIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  TagIcon,
  SparklesIcon,
  CalendarIcon,
  UserIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { api, apiHelpers } from '../../utils/evaluationApi';
import Breadcrumb from './Breadcrumb';

// Utility functions for dataset types
const getDatasetIcon = (type) => {
  const normalizedType = type ? type.toLowerCase().trim() : '';
  switch (normalizedType) {
    case 'medical':
      return DocumentTextIcon;
    case 'qa':
    case 'q&a':
      return ChatBubbleLeftRightIcon;
    case 'classification':
      return TagIcon;
    case 'generation':
      return SparklesIcon;
    case 'conversation':
      return ChatBubbleLeftRightIcon;
    default:
      return CircleStackIcon;
  }
};

const getDatasetTypeLabel = (type) => {
  if (!type) return 'Custom';
  
  const normalizedType = type.toLowerCase().trim();
  const labels = {
    'medical': 'Medical',
    'qa': 'Q&A',
    'q&a': 'Q&A',
    'classification': 'Classification',
    'generation': 'Generation',
    'conversation': 'Conversation',
    'custom': 'Custom'
  };
  return labels[normalizedType] || type;
};

const getTypeColor = (type) => {
  // Normalize the type to handle any case variations or extra spaces
  const normalizedType = type ? type.toLowerCase().trim() : '';
  
  switch (normalizedType) {
    case 'medical':
      return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700';
    case 'qa':
    case 'q&a':
      return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 border-purple-200 dark:border-purple-700';
    case 'classification':
      return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700';
    case 'generation':
      return 'bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 border-amber-200 dark:border-amber-700';
    case 'conversation':
      return 'bg-indigo-100 dark:bg-indigo-900/20 text-indigo-800 dark:text-indigo-300 border-indigo-200 dark:border-indigo-700';
    case 'custom':
    case '':
    case null:
    case undefined:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600';
    default:
      // For any unknown types, use a distinctive color so we can identify them
      console.log('Unknown dataset type:', type);
      return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-700';
  }
};

function DatasetManagement() {
  const navigate = useNavigate();
  const [datasets, setDatasets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDataset, setSelectedDataset] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDatasets, setSelectedDatasets] = useState(new Set());
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    status: 'active'
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    totalRecords: 0,
    avgQuality: 0
  });

  useEffect(() => {
    loadDatasets();
  }, [filters]);


  const loadDatasets = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (filters.type) params.append('type', filters.type);
      if (filters.status) params.append('status', filters.status);
      
      const response = await api.get(`/datasets?${params.toString()}`);
      let datasetsData = response.data.datasets;
      
      // Client-side search filtering
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        datasetsData = datasetsData.filter(dataset =>
          dataset.name.toLowerCase().includes(searchLower) ||
          dataset.description?.toLowerCase().includes(searchLower)
        );
      }
      
      setDatasets(datasetsData);
      
      // Calculate stats
      const totalRecords = datasetsData.reduce((sum, d) => sum + d.record_count, 0);
      const avgQuality = datasetsData.length > 0 ? 90 : 0; // Mock quality score
      
      setStats({
        total: datasetsData.length,
        active: datasetsData.filter(d => d.status === 'active').length,
        totalRecords,
        avgQuality
      });
      
    } catch (error) {
      console.error('Error loading datasets:', error);
      setError(apiHelpers.formatError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleDatasetClick = (dataset) => {
    navigate(`/datasets/${dataset.id}`);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedDataset(null);
  };

  const handleCreateDataset = () => {
    setShowCreateModal(true);
  };


  const handleEditDataset = async (dataset) => {
    try {
      const response = await api.get(`/datasets/${dataset.id}`);
      setSelectedDataset(response.data);
      setShowEditModal(true);
      setShowModal(false);
    } catch (error) {
      console.error('Error fetching dataset details:', error);
    }
  };

  const handleDeleteDataset = async (datasetId) => {
    if (!window.confirm('Are you sure you want to delete this dataset?')) {
      return;
    }

    try {
      await api.delete(`/datasets/${datasetId}`);
      alert('Dataset deleted successfully');
      loadDatasets();
      closeModal();
    } catch (error) {
      console.error('Error deleting dataset:', error);
      alert('Failed to delete dataset');
    }
  };

  const handleSaveDataset = async (datasetData, isEdit = false) => {
    try {
      if (isEdit) {
        await api.put(`/datasets/${selectedDataset.id}`, datasetData);
        alert('Dataset updated successfully');
      } else {
        // Create with provided rows
        await api.post('/datasets', datasetData);
        alert('Dataset created successfully');
      }
      
      loadDatasets();
      setShowCreateModal(false);
      setShowEditModal(false);
      setSelectedDataset(null);
    } catch (error) {
      console.error(`Error ${isEdit ? 'updating' : 'creating'} dataset:`, error);
      const message = apiHelpers.formatError(error);
      alert(`Failed to ${isEdit ? 'update' : 'create'} dataset: ${message}`);
    }
  };

  // Bulk selection functions
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedDatasets(new Set(datasets.map(d => d.id)));
    } else {
      setSelectedDatasets(new Set());
    }
  };

  const handleSelectDataset = (datasetId, checked) => {
    const newSelected = new Set(selectedDatasets);
    if (checked) {
      newSelected.add(datasetId);
    } else {
      newSelected.delete(datasetId);
    }
    setSelectedDatasets(newSelected);
  };

  const isAllSelected = datasets.length > 0 && selectedDatasets.size === datasets.length;
  const isIndeterminate = selectedDatasets.size > 0 && selectedDatasets.size < datasets.length;

  const handleBulkDelete = async () => {
    if (selectedDatasets.size === 0) return;
    
    const confirmMessage = `Are you sure you want to delete ${selectedDatasets.size} dataset${selectedDatasets.size > 1 ? 's' : ''}?`;
    if (!window.confirm(confirmMessage)) return;

    try {
      await Promise.all(
        Array.from(selectedDatasets).map(id => api.delete(`/datasets/${id}`))
      );
      alert('Datasets deleted successfully');
      setSelectedDatasets(new Set());
      loadDatasets();
    } catch (error) {
      console.error('Error deleting datasets:', error);
      alert('Failed to delete some datasets');
    }
  };

  const handleBulkExport = () => {
    if (selectedDatasets.size === 0) return;
    
    // Export selected datasets as CSV
    const selectedData = datasets.filter(d => selectedDatasets.has(d.id));
    const csvContent = "data:text/csv;charset=utf-8," + 
      "Name,Type,Records,Description,Created At\n" +
      selectedData.map(d => `"${d.name}","${d.type}",${d.record_count},"${d.description || ''}","${d.created_at || ''}"`).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "datasets_export.csv");
    link.click();
  };

  // Template generation functions for main page
  const generateMainTemplate = (templateType) => {
    const templates = {
      'unified': [
        ['input', 'expected_output', 'expected_outcome', 'context', 'metadata', 'example_type'],
        // Single-turn Q&A
        ['"What is 2+2?"', '"4"', '', '', '{"difficulty": "easy", "topic": "math"}', '"single_turn_qa"'],
        // Conversation
        ['"{""scenario"": ""Customer support chat for password reset"", ""turns"": [{""role"": ""user"", ""content"": ""I forgot my password""}, {""role"": ""assistant"", ""content"": ""I can help you reset your password. What\'s your email address?""}, {""role"": ""user"", ""content"": ""<EMAIL>""}]}"', '', '"Password reset email sent successfully"', '', '{"category": "support", "priority": "medium"}', '"conversation"'],
        // Q&A with Context (RAG)
        ['"What is the capital of France?"', '"Paris"', '', '["France is a country in Western Europe", "Paris is located in the north-central part of France"]', '{"difficulty": "easy", "category": "geography"}', '"qa_with_context"'],
        // Medical Case
        ['"Patient presents with fever, cough, and fatigue"', '"Rest, fluids, and symptomatic treatment"', '"Patient recovers within 3-5 days"', '["Upper respiratory infection symptoms", "Viral illness typical presentation"]', '{"severity": "mild", "specialty": "general_medicine"}', '"medical_case"'],
        // Classification
        ['"This movie was absolutely fantastic! Great acting and plot."', '"positive"', '', '', '{"confidence": "0.95", "domain": "movie_reviews"}', '"classification"'],
        // Generation
        ['"Write a short story about a robot learning to paint"', '"In a quiet studio, R-47 picked up a brush for the first time. The colors seemed foreign, yet somehow familiar..."', '"Creative story with emotional depth"', '', '{"style": "creative", "length": "short", "quality_score": "8.5"}', '"generation"'],
        // Multi-format conversation (separate scenario)
        ['"Technical troubleshooting for app crashes"', '', '"Issue resolved with app restart"', '', '{"category": "technical", "platform": "iOS"}', '"conversation_scenario"'],
        // Educational Q&A
        ['"Explain quantum physics in simple terms"', '"Quantum physics deals with the behavior of matter and energy at the smallest scales, where particles can exist in multiple states simultaneously"', '"Student gains basic understanding"', '["Physics fundamentals", "Quantum mechanics introduction"]', '{"subject": "physics", "level": "beginner"}', '"educational"'],
        // Complex RAG with tools
        ['"How do I calculate compound interest for a $1000 investment at 5% annual rate for 3 years?"', '"Using the compound interest formula A = P(1 + r)^t: A = 1000(1 + 0.05)^3 = $1157.63"', '', '["Compound interest formula", "Financial mathematics", "Investment calculations"]', '{"tools_used": ["calculator"], "domain": "finance", "formula": "A=P(1+r)^t"}', '"rag_with_tools"']
      ],
      'single-turn': [
        ['input', 'expected_output', 'metadata'],
        ['"What is 2+2?"', '"4"', '{"difficulty": "easy", "topic": "math"}'],
        ['"Explain photosynthesis"', '"Photosynthesis is the process by which plants convert light energy into chemical energy"', '{"difficulty": "medium", "topic": "biology"}'],
        ['"Write a haiku about rain"', '"Gentle drops falling\\nOn leaves and earth below us\\nNature\'s quiet song"', '{"difficulty": "medium", "topic": "creative"}']
      ],
      'conversation': [
        ['input', 'expected_outcome', 'metadata'],
        ['"{""scenario"": ""Customer support chat for password reset"", ""turns"": [{""role"": ""user"", ""content"": ""I forgot my password""}, {""role"": ""assistant"", ""content"": ""I can help you reset your password. What\'s your email address?""}, {""role"": ""user"", ""content"": ""<EMAIL>""}]}"', '"Password reset email sent successfully"', '{"category": "support", "priority": "medium"}'],
        ['"{""scenario"": ""Technical troubleshooting conversation"", ""turns"": [{""role"": ""user"", ""content"": ""My app keeps crashing""}, {""role"": ""assistant"", ""content"": ""I\'m sorry to hear that. What device are you using?""}, {""role"": ""user"", ""content"": ""iPhone 12""}, {""role"": ""assistant"", ""content"": ""Try force-closing the app and restarting it""}]}"', '"Issue resolved with app restart"', '{"category": "technical", "platform": "iOS"}'],
        ['"{""scenario"": ""Educational Q&A session"", ""turns"": [{""role"": ""user"", ""content"": ""Can you explain quantum physics?""}, {""role"": ""assistant"", ""content"": ""Quantum physics deals with the behavior of matter and energy at the smallest scales""}, {""role"": ""user"", ""content"": ""Can you give me a simple example?""}]}"', '"Student gains basic understanding of quantum concepts"', '{"subject": "physics", "level": "beginner"}']
      ],
      'qa': [
        ['question', 'answer', 'context', 'metadata'],
        ['"What is the capital of France?"', '"Paris"', '["France is a country in Western Europe", "Paris is located in the north-central part of France"]', '{"difficulty": "easy", "category": "geography"}'],
        ['"How do you make a simple pasta sauce?"', '"Heat olive oil, add garlic, then tomatoes, and season with salt, pepper, and herbs"', '["Pasta sauce basics", "Italian cooking fundamentals"]', '{"difficulty": "easy", "category": "cooking"}'],
        ['"What causes the greenhouse effect?"', '"Greenhouse gases in the atmosphere trap heat from the sun, warming the Earth\'s surface"', '["Climate science basics", "Atmospheric physics"]', '{"difficulty": "medium", "category": "science"}']
      ],
      'medical': [
        ['symptoms', 'diagnosis', 'treatment', 'metadata'],
        ['"Patient presents with fever, cough, and fatigue"', '"Upper respiratory infection"', '"Rest, fluids, and symptomatic treatment"', '{"severity": "mild", "duration": "3-5 days"}'],
        ['"Chest pain, shortness of breath, elevated troponins"', '"Acute myocardial infarction"', '"Emergency cardiac catheterization and PCI"', '{"severity": "critical", "specialty": "cardiology"}'],
        ['"Joint pain, morning stiffness, positive RF"', '"Rheumatoid arthritis"', '"DMARDs and anti-inflammatory therapy"', '{"chronic": true, "specialty": "rheumatology"}']
      ],
      'classification': [
        ['text', 'label', 'confidence', 'metadata'],
        ['"This movie was absolutely fantastic! Great acting and plot."', '"positive"', '"0.95"', '{"domain": "movie_reviews", "length": "short"}'],
        ['"The service was terrible and the food was cold."', '"negative"', '"0.92"', '{"domain": "restaurant_reviews", "length": "short"}'],
        ['"The product works as expected, nothing special."', '"neutral"', '"0.78"', '{"domain": "product_reviews", "length": "short"}']
      ],
      'generation': [
        ['prompt', 'generated_text', 'quality_score', 'metadata'],
        ['"Write a short story about a robot learning to paint"', '"In a quiet studio, R-47 picked up a brush for the first time. The colors seemed foreign, yet somehow familiar..."', '"8.5"', '{"style": "creative", "length": "short"}'],
        ['"Explain quantum computing to a 10-year-old"', '"Imagine a magical computer that can try all possible answers to a puzzle at the same time, instead of trying them one by one..."', '"9.0"', '{"style": "educational", "audience": "children"}'],
        ['"Create a product description for smart headphones"', '"Experience crystal-clear audio with our advanced noise-canceling technology and 30-hour battery life..."', '"7.8"', '{"style": "marketing", "product_type": "electronics"}']
      ]
    };

    return templates[templateType] || templates['single-turn'];
  };

  const downloadMainTemplate = (templateType) => {
    const templateData = generateMainTemplate(templateType);
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', templateType === 'unified' ? 'universal-dataset-template.csv' : `${templateType}-dataset-template.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };



  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (error) {
    return (
      <div className="py-8">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-300">Error loading datasets</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-400">{error}</div>
              <button
                onClick={loadDatasets}
                className="mt-3 bg-red-100 dark:bg-red-800 px-3 py-1 rounded text-sm text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-700"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Dataset Management', href: '/datasets' }
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbItems} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Datasets</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Showing {loading ? '...' : datasets.length} datasets
          </p>
        </div>
        <button 
          onClick={handleCreateDataset}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400">
          <PlusIcon className="h-4 w-4 mr-2" />
          New Dataset
        </button>
      </div>


      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div className="p-4">
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Search datasets..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="block w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400"
              />
            </div>
            
            {/* Type Filter */}
            <select
              value={filters.type}
              onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400"
            >
              <option value="">All Types</option>
              <option value="medical">Medical</option>
              <option value="qa">Q&A</option>
              <option value="classification">Classification</option>
              <option value="generation">Generation</option>
              <option value="conversation">Conversation</option>
              <option value="custom">Custom</option>
            </select>
            
            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="archived">Archived</option>
            </select>
            
            {/* Sort */}
            <select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400">
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="created-desc">Newest</option>
              <option value="created-asc">Oldest</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {selectedDatasets.size > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900 dark:text-blue-300">
                {selectedDatasets.size} dataset{selectedDatasets.size > 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedDatasets(new Set())}
                className="ml-4 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleBulkExport}
                className="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-md text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                <ArrowUpTrayIcon className="h-4 w-4 mr-1" />
                Export
              </button>
              <button
                onClick={handleBulkDelete}
                className="inline-flex items-center px-3 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <XMarkIcon className="h-4 w-4 mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Datasets Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        {loading ? (
          <div className="animate-pulse">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-8">
                      <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {[...Array(5)].map((_, i) => (
                    <tr key={i}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : datasets.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-8">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      ref={(el) => {
                        if (el) el.indeterminate = isIndeterminate;
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    # of Examples
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    # of Experiments
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {datasets.map((dataset) => (
                  <tr
                    key={dataset.id}
                    onClick={() => handleDatasetClick(dataset)}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-150"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedDatasets.has(dataset.id)}
                        onChange={(e) => handleSelectDataset(dataset.id, e.target.checked)}
                        onClick={(e) => e.stopPropagation()}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {dataset.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {dataset.description || 'No description'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(dataset.type)}`}>
                        {getDatasetTypeLabel(dataset.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {dataset.record_count.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {dataset.experiment_count || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {dataset.updated_at || dataset.created_at 
                          ? new Date(dataset.updated_at || dataset.created_at).toLocaleDateString()
                          : 'N/A'
                        }
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {dataset.created_by && (
                          <span className="flex items-center">
                            <UserIcon className="h-3 w-3 mr-1" />
                            {dataset.created_by}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDatasetClick(dataset);
                          }}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
                          title="View Details"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDataset(dataset);
                          }}
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 p-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
                          title="Edit Dataset"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteDataset(dataset.id);
                          }}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20"
                          title="Delete Dataset"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="px-6 py-20 text-center">
            <div className="mx-auto h-24 w-24 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-6">
              <CircleStackIcon className="h-12 w-12 text-gray-400 dark:text-gray-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {filters.search || filters.type || filters.status !== 'active' 
                ? 'No datasets match your filters'
                : 'Create your first dataset'
              }
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-8 max-w-md mx-auto">
              {filters.search || filters.type || filters.status !== 'active' 
                ? 'Try adjusting your search criteria or filters to find the datasets you\'re looking for.'
                : 'Datasets are collections of examples used to evaluate and improve your AI models. Get started by creating your first dataset.'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {(filters.search || filters.type || filters.status !== 'active') ? (
                <button
                  onClick={() => {
                    setFilters({ search: '', type: '', status: 'active' });
                  }}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Clear filters
                </button>
              ) : (
                <button
                  onClick={handleCreateDataset}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Create New Dataset
                </button>
              )}
            </div>
          </div>
        )}
      </div>


      {/* Dataset Details Modal */}
      {showModal && selectedDataset && (
        <DatasetDetailModal
          dataset={selectedDataset}
          onClose={closeModal}
          onEdit={handleEditDataset}
          onDelete={handleDeleteDataset}
          onUploadOpen={null}
        />
      )}

      {/* Create Dataset Modal */}
      {showCreateModal && (
        <DatasetFormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={(data) => handleSaveDataset(data, false)}
          title="Create New Dataset"
        />
      )}

      {/* Edit Dataset Modal */}
      {showEditModal && selectedDataset && (
        <DatasetFormModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={(data) => handleSaveDataset(data, true)}
          dataset={selectedDataset}
          title="Edit Dataset"
        />
      )}

    </div>
  );
}

function DatasetFormModal({ isOpen, onClose, onSave, dataset, title }) {
  const [formData, setFormData] = React.useState({
    name: dataset?.name || '',
    description: dataset?.description || '',
    status: dataset?.status || 'active'
  });
  const [creationMethod, setCreationMethod] = React.useState(dataset ? 'manual' : 'csv'); // If editing, default to manual
  const [uploadedFile, setUploadedFile] = React.useState(null);
  const [fileData, setFileData] = React.useState(null);
  const [isDragOver, setIsDragOver] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const [manualRows, setManualRows] = React.useState([
    { input: { question: '' }, expected_output: '', metadata: {}, rowType: 'single' }
  ]);
  const [existingDataLoading, setExistingDataLoading] = React.useState(false);
  const [existingDataError, setExistingDataError] = React.useState(null);

  // Load existing dataset data when in edit mode
  React.useEffect(() => {
    const loadExistingData = async () => {
      if (dataset && dataset.id) {
        setExistingDataLoading(true);
        setExistingDataError(null);
        try {
          const response = await api.get(`/datasets/${dataset.id}/preview?limit=1000`); // Get all rows for editing
          const existingRows = response.data.preview || [];
          
          if (existingRows.length > 0) {
            setManualRows(existingRows.map(row => ({
              input: row.input || { question: '' },
              expected_output: row.expected_output || '',
              metadata: row.metadata || {}
            })));
          }
        } catch (error) {
          console.error('Error loading existing dataset data:', error);
          setExistingDataError('Failed to load existing dataset data');
          // Keep default empty row if loading fails
        } finally {
          setExistingDataLoading(false);
        }
      }
    };

    if (isOpen && dataset) {
      loadExistingData();
    }
  }, [isOpen, dataset]);


  if (!isOpen) return null;

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (event) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      await processFile(files[0]);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    await processFile(file);
  };

  const parseCSVLine = (line) => {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      const nextChar = line[i + 1];
      
      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  };

  const intelligentFieldMapping = (rawRow, headers) => {
    // Enhanced field mapping patterns for multi-turn support
    const fieldMappings = {
      prompt: ['prompt', 'input_prompt', 'question', 'query', 'text', 'problem', 'instruction', 'user_input'],
      input: ['input', 'turns', 'conversation', 'messages', 'dialogue'],
      expected_output: ['expected_output', 'expected', 'answer', 'response', 'output', 'result', 'solution', 'label'],
      expected_outcome: ['expected_outcome', 'outcome', 'goal', 'target_outcome', 'conversation_outcome'],
      context: ['context', 'background', 'setup', 'additional_context'],
      scenario: ['scenario', 'conversation_scenario', 'description'],
      turns: ['turns', 'messages', 'dialogue', 'conversation_turns', 'chat_history'],
      metadata: ['metadata', 'meta', 'tags', 'category', 'difficulty', 'source'],
      test_case_type: ['test_case_type', 'type', 'example_type', 'case_type']
    };

    const mapped = {
      metadata: {}
    };
    
    // Track what type of test case this is
    let isMultiTurn = false;
    let testCaseType = null;

    const usedHeaders = new Set();
    
    // IMPORTANT: First check if there's an 'input' column with JSON data
    // This handles both single-turn and multi-turn from our CSV format
    if (rawRow.input) {
      try {
        const parsed = JSON.parse(rawRow.input);
        if (parsed && typeof parsed === 'object') {
          // If it has scenario or turns, it's multi-turn
          if (parsed.scenario || parsed.turns) {
            mapped.input = parsed;
            isMultiTurn = true;
            usedHeaders.add('input');
          } else if (parsed.question || parsed.prompt || parsed.text || parsed.context) {
            // Single-turn with structured input
            mapped.input = parsed;
            usedHeaders.add('input');
          }
        }
      } catch {
        // Not JSON, treat as plain text input for single-turn
        if (rawRow.input.trim()) {
          mapped.input = { question: rawRow.input.trim() };
          usedHeaders.add('input');
        }
      }
    }
    
    // First pass: check for test_case_type to determine structure
    for (const header of headers) {
      const normalizedHeader = header.toLowerCase().trim();
      if (fieldMappings.test_case_type.includes(normalizedHeader)) {
        testCaseType = (rawRow[header] || '').toLowerCase();
        isMultiTurn = testCaseType === 'multi_turn' || testCaseType === 'conversational';
        usedHeaders.add(header);
        break;
      }
    }
    
    // Check for turns column to detect multi-turn
    for (const header of headers) {
      const normalizedHeader = header.toLowerCase().trim();
      if (fieldMappings.turns.includes(normalizedHeader) && rawRow[header]) {
        isMultiTurn = true;
        break;
      }
    }

    // Now map fields based on the test case type
    // Skip input field processing if we already parsed it from JSON
    for (const [field, patterns] of Object.entries(fieldMappings)) {
      if (field === 'input' && mapped.input) {
        continue; // Skip input processing if already handled from JSON
      }
      
      for (const header of headers) {
        const normalizedHeader = header.toLowerCase().trim();
        if (patterns.includes(normalizedHeader) && !usedHeaders.has(header)) {
          const value = rawRow[header] || '';
          
          if (field === 'prompt') {
            if (!isMultiTurn) {
              // For single-turn, create input object with prompt
              if (!mapped.input) mapped.input = {};
              mapped.input.prompt = value;
            }
            usedHeaders.add(header);
            break; // Important: break here so we don't fall through to the else clause
          } else if (field === 'input' && isMultiTurn) {
            // For multi-turn, check if this is a turns array
            if (normalizedHeader === 'input' || normalizedHeader === 'turns') {
              try {
                const parsed = JSON.parse(value);
                if (Array.isArray(parsed)) {
                  mapped.turns = parsed;
                }
              } catch {
                // Not valid JSON, skip
              }
            }
          } else if (field === 'scenario' && isMultiTurn) {
            // For multi-turn, scenario is at the top level
            mapped.scenario = value;
          } else if (field === 'turns') {
            // Handle separate turns column for multi-turn
            try {
              const parsed = JSON.parse(value);
              if (Array.isArray(parsed)) {
                mapped.turns = parsed;
                isMultiTurn = true;
              }
            } catch {
              // If not valid JSON, skip turns parsing
            }
          } else if (field === 'expected_outcome') {
            mapped.expected_outcome = value;
          } else if (field === 'metadata') {
            // Try to parse as JSON first, otherwise create simple object
            try {
              const parsed = JSON.parse(value);
              if (typeof parsed === 'object' && parsed !== null) {
                mapped.metadata = parsed;
              } else {
                mapped.metadata = { value: value };
              }
            } catch {
              mapped.metadata = { value: value };
            }
          } else if (field === 'test_case_type') {
            // Don't store test_case_type directly, but use it to help structure the data
            // The backend will determine type based on input structure
            usedHeaders.add(header);
            // Store temporarily for processing, but will be removed before sending
            mapped._test_case_type = value || 'single_turn';
          } else if (field === 'context') {
            if (value) {
              if (!isMultiTurn) {
                // For single-turn, context goes in the input object
                if (!mapped.input) mapped.input = {};
                mapped.input.context = value;
              } else {
                // For multi-turn, context is an array at the top level
                if (Array.isArray(value)) {
                  mapped.context = value;
                } else if (value.includes(',')) {
                  mapped.context = value.split(',').map(v => v.trim());
                } else {
                  mapped.context = [value];
                }
              }
            }
          } else if (field !== 'prompt' && field !== 'test_case_type') {
            // Only set other allowed fields, skip prompt and test_case_type
            mapped[field] = value;
          }
          
          usedHeaders.add(header);
          break;
        }
      }
    }

    // Handle remaining unmapped columns
    const remainingHeaders = headers.filter(h => !usedHeaders.has(h));
    
    // Put remaining columns in metadata
    for (const header of remainingHeaders) {
      const value = rawRow[header] || '';
      const normalizedHeader = header.toLowerCase().trim();
      
      // Handle meta_ prefixed columns for metadata
      if (header.startsWith('meta_') || header.startsWith('meta.')) {
        const metaKey = header.replace(/^meta[_.]/, '');
        if (value) {
          mapped.metadata[metaKey] = value;
        }
      }
      // Special handling for prompt-like fields in single-turn
      else if (!isMultiTurn && !mapped.input && 
          ['question', 'prompt', 'text', 'problem'].some(pattern => normalizedHeader.includes(pattern))) {
        if (!mapped.input) mapped.input = {};
        mapped.input.prompt = value;
      } else if (value) {
        // Add to metadata
        mapped.metadata[header] = value;
      }
    }

    // Final structure validation and cleanup
    // Check if input already has the right structure from JSON parsing
    if (mapped.input && (mapped.input.scenario || mapped.input.turns)) {
      isMultiTurn = true;
      // For multi-turn, use expected_outcome if provided
      if (!mapped.expected_outcome && mapped.expected_output) {
        mapped.expected_outcome = mapped.expected_output;
        delete mapped.expected_output;
      }
    } else if (isMultiTurn) {
      // Multi-turn structure: input object contains scenario and turns
      if (!mapped.turns || mapped.turns.length === 0) {
        // If no turns were found, this isn't really multi-turn
        isMultiTurn = false;
      } else {
        // Transform turns to match backend schema
        const backendTurns = [];
        mapped.turns.forEach(turn => {
          // Add user message
          if (turn.input) {
            backendTurns.push({
              role: 'user',
              content: turn.input
            });
          }
          // Add expected assistant response (if we have it)
          if (turn.expected_output) {
            backendTurns.push({
              role: 'assistant',
              content: turn.expected_output
            });
          }
        });
        
        // Create the input object with scenario and turns
        mapped.input = {
          scenario: mapped.scenario || 'Multi-turn conversation',
          turns: backendTurns
        };
        
        // Clean up - remove top-level turns and scenario
        delete mapped.turns;
        delete mapped.scenario;
        
        // For multi-turn, use expected_outcome if provided
        if (!mapped.expected_outcome && mapped.expected_output) {
          mapped.expected_outcome = mapped.expected_output;
          delete mapped.expected_output;
        }
      }
    }
    
    if (!isMultiTurn) {
      // Single-turn structure: input can be string or object
      if (!mapped.input || (!mapped.input.prompt && Object.keys(mapped.input).length === 0)) {
        // Try to find a prompt from the first non-empty column
        for (const header of headers) {
          const value = rawRow[header] || '';
          if (value && !usedHeaders.has(header)) {
            // For single-turn, input can be just a string
            mapped.input = value;
            break;
          }
        }
      } else if (mapped.input.prompt) {
        // If we have a prompt in the input object, we can simplify to just the prompt string
        // unless there's also context
        if (!mapped.input.context) {
          mapped.input = mapped.input.prompt;
        }
      }
      
      // For single-turn, we need expected_output
      if (!mapped.expected_output && mapped.expected_outcome) {
        mapped.expected_output = mapped.expected_outcome;
      }
      
      // Remove multi-turn specific fields
      delete mapped.turns;
      delete mapped.scenario;
      delete mapped.expected_outcome;
    }

    // Clean up empty fields
    if (mapped.expected_output === '') delete mapped.expected_output;
    if (mapped.expected_outcome === '') delete mapped.expected_outcome;
    if (mapped.context === '' || (Array.isArray(mapped.context) && mapped.context.length === 0)) {
      delete mapped.context;
    }
    if (mapped.scenario === '') delete mapped.scenario;
    if (Object.keys(mapped.metadata || {}).length === 0) delete mapped.metadata;
    
    // Ensure input.context is a string for single-turn if it exists
    if (!isMultiTurn && mapped.input && mapped.input.context && Array.isArray(mapped.input.context)) {
      mapped.input.context = mapped.input.context.join(', ');
    }

    return mapped;
  };

  const processFile = async (file) => {
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please upload a CSV file');
      return;
    }

    setUploadedFile(file);
    setIsProcessing(true);
    
    try {
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        alert('CSV file is empty');
        return;
      }

      const headers = parseCSVLine(lines[0]).map(h => h.trim().replace(/^["']|["']$/g, ''));
      
      // Parse all rows for preview and validation
      const allRows = [];
      const previewRows = [];
      
      for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]);
        const rawRow = {};
        
        headers.forEach((header, index) => {
          rawRow[header] = values[index] || '';
        });
        
        const mappedRow = intelligentFieldMapping(rawRow, headers);
        allRows.push(mappedRow);
        
        // Keep first 5 rows for preview
        if (i <= 5) {
          previewRows.push({
            original: rawRow,
            mapped: mappedRow,
            rowNumber: i
          });
        }
      }

      setFileData({
        headers,
        rows: previewRows,
        totalRows: allRows.length,
        allMappedRows: allRows // Store all processed rows
      });

      // Auto-fill dataset name if not set
      if (!formData.name) {
        const fileName = file.name.replace(/\.[^/.]+$/, '');
        setFormData(prev => ({ ...prev, name: fileName }));
      }
    } catch (error) {
      console.error('Error processing file:', error);
      alert('Error processing CSV file');
    } finally {
      setIsProcessing(false);
    }
  };

  const addManualRow = () => {
    setManualRows([...manualRows, { input: { question: '' }, expected_output: '', metadata: {}, rowType: 'single' }]);
  };

  const addConversationRow = () => {
    setManualRows([...manualRows, { 
      input: { 
        scenario: '',
        turns: [
          { role: 'user', content: '' },
          { role: 'assistant', content: '' }
        ]
      }, 
      expected_outcome: '', 
      metadata: {},
      rowType: 'conversation'
    }]);
  };

  const updateConversationRow = (rowIndex, field, value, turnIndex = null) => {
    const newRows = [...manualRows];
    if (field === 'scenario') {
      newRows[rowIndex].input.scenario = value;
    } else if (field === 'expected_outcome') {
      newRows[rowIndex].expected_outcome = value;
    } else if (field === 'turn_content' && turnIndex !== null) {
      newRows[rowIndex].input.turns[turnIndex].content = value;
    } else if (field === 'turn_role' && turnIndex !== null) {
      newRows[rowIndex].input.turns[turnIndex].role = value;
    }
    setManualRows(newRows);
  };

  const addTurnToConversation = (rowIndex) => {
    const newRows = [...manualRows];
    newRows[rowIndex].input.turns.push({ role: 'user', content: '' });
    setManualRows(newRows);
  };

  const removeTurnFromConversation = (rowIndex, turnIndex) => {
    const newRows = [...manualRows];
    if (newRows[rowIndex].input.turns.length > 1) {
      newRows[rowIndex].input.turns.splice(turnIndex, 1);
      setManualRows(newRows);
    }
  };

  const toggleRowType = (rowIndex) => {
    const newRows = [...manualRows];
    if (newRows[rowIndex].rowType === 'single') {
      // Convert to conversation
      newRows[rowIndex] = {
        input: {
          scenario: '',
          turns: [
            { role: 'user', content: newRows[rowIndex].input.question || '' },
            { role: 'assistant', content: '' }
          ]
        },
        expected_outcome: newRows[rowIndex].expected_output || '',
        metadata: newRows[rowIndex].metadata || {},
        rowType: 'conversation'
      };
    } else {
      // Convert to single
      const firstUserTurn = newRows[rowIndex].input.turns?.find(turn => turn.role === 'user');
      newRows[rowIndex] = {
        input: { question: firstUserTurn?.content || '' },
        expected_output: newRows[rowIndex].expected_outcome || '',
        metadata: newRows[rowIndex].metadata || {},
        rowType: 'single'
      };
    }
    setManualRows(newRows);
  };

  const updateManualRow = (index, field, value) => {
    const newRows = [...manualRows];
    if (field === 'input.question') {
      newRows[index].input.question = value;
    } else {
      newRows[index][field] = value;
    }
    setManualRows(newRows);
  };

  const removeManualRow = (index) => {
    if (manualRows.length > 1) {
      setManualRows(manualRows.filter((_, i) => i !== index));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    let dataToSubmit = [];

    if (creationMethod === 'csv') {
      if (!fileData || !fileData.allMappedRows) {
        alert('Please upload and process a CSV file first');
        return;
      }
      dataToSubmit = fileData.allMappedRows;
    } else {
      // Manual creation - validate and prepare data
      const validRows = manualRows.filter(row => {
        if (row.rowType === 'conversation') {
          return row.input.scenario?.trim() || (row.input.turns && row.input.turns.some(turn => turn.content?.trim()));
        } else {
          return row.input.question?.trim() || Object.keys(row.input).some(key => key !== 'question' && row.input[key]?.trim());
        }
      });
      
      if (validRows.length === 0) {
        alert('Please add at least one row with input data');
        return;
      }
      
      dataToSubmit = validRows;
    }

    console.log('Data to submit:', dataToSubmit);
    console.log('Data to submit type:', typeof dataToSubmit);
    console.log('Data to submit length:', dataToSubmit?.length);
    
    try {
      const datasetData = {
        name: formData.name,
        description: formData.description,
        data: dataToSubmit
      };
      
      if (dataset && dataset.id) {
        // Update existing dataset - don't send created_by
        await api.put(`/datasets/${dataset.id}`, datasetData, {
          headers: { 'Content-Type': 'application/json' }
        });
        alert('Dataset updated successfully');
      } else {
        // Create new dataset - include created_by
        await api.post('/datasets', { ...datasetData, created_by: 'system' }, {
          headers: { 'Content-Type': 'application/json' }
        });
        alert('Dataset created successfully');
      }
      
      onClose();
      // Trigger reload of datasets
      window.location.reload();
    } catch (error) {
      console.error('Error saving dataset:', error);
      console.error('Error response:', error.response?.data);
      const errorMessage = error.response?.data?.details?.join(', ') || 
                          error.response?.data?.error || 
                          error.message || 
                          'Unknown error';
      alert(`Failed to ${dataset ? 'update' : 'create'} dataset: ${errorMessage}`);
    }
  };

  const removeFile = () => {
    setUploadedFile(null);
    setFileData(null);
  };

  // Template generation functions
  const generateTemplate = (templateType) => {
    const templates = {
      'unified': [
        ['input', 'expected_output', 'expected_outcome', 'context', 'metadata', 'example_type'],
        // Single-turn Q&A
        ['"What is 2+2?"', '"4"', '', '', '{"difficulty": "easy", "topic": "math"}', '"single_turn_qa"'],
        // Conversation
        ['"{""scenario"": ""Customer support chat for password reset"", ""turns"": [{""role"": ""user"", ""content"": ""I forgot my password""}, {""role"": ""assistant"", ""content"": ""I can help you reset your password. What\'s your email address?""}, {""role"": ""user"", ""content"": ""<EMAIL>""}]}"', '', '"Password reset email sent successfully"', '', '{"category": "support", "priority": "medium"}', '"conversation"'],
        // Q&A with Context (RAG)
        ['"What is the capital of France?"', '"Paris"', '', '["France is a country in Western Europe", "Paris is located in the north-central part of France"]', '{"difficulty": "easy", "category": "geography"}', '"qa_with_context"'],
        // Medical Case
        ['"Patient presents with fever, cough, and fatigue"', '"Rest, fluids, and symptomatic treatment"', '"Patient recovers within 3-5 days"', '["Upper respiratory infection symptoms", "Viral illness typical presentation"]', '{"severity": "mild", "specialty": "general_medicine"}', '"medical_case"'],
        // Classification
        ['"This movie was absolutely fantastic! Great acting and plot."', '"positive"', '', '', '{"confidence": "0.95", "domain": "movie_reviews"}', '"classification"'],
        // Generation
        ['"Write a short story about a robot learning to paint"', '"In a quiet studio, R-47 picked up a brush for the first time. The colors seemed foreign, yet somehow familiar..."', '"Creative story with emotional depth"', '', '{"style": "creative", "length": "short", "quality_score": "8.5"}', '"generation"'],
        // Multi-format conversation (separate scenario)
        ['"Technical troubleshooting for app crashes"', '', '"Issue resolved with app restart"', '', '{"category": "technical", "platform": "iOS"}', '"conversation_scenario"'],
        // Educational Q&A
        ['"Explain quantum physics in simple terms"', '"Quantum physics deals with the behavior of matter and energy at the smallest scales, where particles can exist in multiple states simultaneously"', '"Student gains basic understanding"', '["Physics fundamentals", "Quantum mechanics introduction"]', '{"subject": "physics", "level": "beginner"}', '"educational"'],
        // Complex RAG with tools
        ['"How do I calculate compound interest for a $1000 investment at 5% annual rate for 3 years?"', '"Using the compound interest formula A = P(1 + r)^t: A = 1000(1 + 0.05)^3 = $1157.63"', '', '["Compound interest formula", "Financial mathematics", "Investment calculations"]', '{"tools_used": ["calculator"], "domain": "finance", "formula": "A=P(1+r)^t"}', '"rag_with_tools"']
      ],
      'single-turn': [
        ['input', 'expected_output', 'metadata'],
        ['"What is 2+2?"', '"4"', '{"difficulty": "easy", "topic": "math"}'],
        ['"Explain photosynthesis"', '"Photosynthesis is the process by which plants convert light energy into chemical energy"', '{"difficulty": "medium", "topic": "biology"}'],
        ['"Write a haiku about rain"', '"Gentle drops falling\\nOn leaves and earth below us\\nNature\'s quiet song"', '{"difficulty": "medium", "topic": "creative"}']
      ],
      'conversation': [
        ['input', 'expected_outcome', 'metadata'],
        ['"{""scenario"": ""Customer support chat for password reset"", ""turns"": [{""role"": ""user"", ""content"": ""I forgot my password""}, {""role"": ""assistant"", ""content"": ""I can help you reset your password. What\'s your email address?""}, {""role"": ""user"", ""content"": ""<EMAIL>""}]}"', '"Password reset email sent successfully"', '{"category": "support", "priority": "medium"}'],
        ['"{""scenario"": ""Technical troubleshooting conversation"", ""turns"": [{""role"": ""user"", ""content"": ""My app keeps crashing""}, {""role"": ""assistant"", ""content"": ""I\'m sorry to hear that. What device are you using?""}, {""role"": ""user"", ""content"": ""iPhone 12""}, {""role"": ""assistant"", ""content"": ""Try force-closing the app and restarting it""}]}"', '"Issue resolved with app restart"', '{"category": "technical", "platform": "iOS"}'],
        ['"{""scenario"": ""Educational Q&A session"", ""turns"": [{""role"": ""user"", ""content"": ""Can you explain quantum physics?""}, {""role"": ""assistant"", ""content"": ""Quantum physics deals with the behavior of matter and energy at the smallest scales""}, {""role"": ""user"", ""content"": ""Can you give me a simple example?""}]}"', '"Student gains basic understanding of quantum concepts"', '{"subject": "physics", "level": "beginner"}']
      ],
      'qa': [
        ['question', 'answer', 'context', 'metadata'],
        ['"What is the capital of France?"', '"Paris"', '["France is a country in Western Europe", "Paris is located in the north-central part of France"]', '{"difficulty": "easy", "category": "geography"}'],
        ['"How do you make a simple pasta sauce?"', '"Heat olive oil, add garlic, then tomatoes, and season with salt, pepper, and herbs"', '["Pasta sauce basics", "Italian cooking fundamentals"]', '{"difficulty": "easy", "category": "cooking"}'],
        ['"What causes the greenhouse effect?"', '"Greenhouse gases in the atmosphere trap heat from the sun, warming the Earth\'s surface"', '["Climate science basics", "Atmospheric physics"]', '{"difficulty": "medium", "category": "science"}']
      ],
      'medical': [
        ['symptoms', 'diagnosis', 'treatment', 'metadata'],
        ['"Patient presents with fever, cough, and fatigue"', '"Upper respiratory infection"', '"Rest, fluids, and symptomatic treatment"', '{"severity": "mild", "duration": "3-5 days"}'],
        ['"Chest pain, shortness of breath, elevated troponins"', '"Acute myocardial infarction"', '"Emergency cardiac catheterization and PCI"', '{"severity": "critical", "specialty": "cardiology"}'],
        ['"Joint pain, morning stiffness, positive RF"', '"Rheumatoid arthritis"', '"DMARDs and anti-inflammatory therapy"', '{"chronic": true, "specialty": "rheumatology"}']
      ],
      'classification': [
        ['text', 'label', 'confidence', 'metadata'],
        ['"This movie was absolutely fantastic! Great acting and plot."', '"positive"', '"0.95"', '{"domain": "movie_reviews", "length": "short"}'],
        ['"The service was terrible and the food was cold."', '"negative"', '"0.92"', '{"domain": "restaurant_reviews", "length": "short"}'],
        ['"The product works as expected, nothing special."', '"neutral"', '"0.78"', '{"domain": "product_reviews", "length": "short"}']
      ],
      'generation': [
        ['prompt', 'generated_text', 'quality_score', 'metadata'],
        ['"Write a short story about a robot learning to paint"', '"In a quiet studio, R-47 picked up a brush for the first time. The colors seemed foreign, yet somehow familiar..."', '"8.5"', '{"style": "creative", "length": "short"}'],
        ['"Explain quantum computing to a 10-year-old"', '"Imagine a magical computer that can try all possible answers to a puzzle at the same time, instead of trying them one by one..."', '"9.0"', '{"style": "educational", "audience": "children"}'],
        ['"Create a product description for smart headphones"', '"Experience crystal-clear audio with our advanced noise-canceling technology and 30-hour battery life..."', '"7.8"', '{"style": "marketing", "product_type": "electronics"}']
      ]
    };

    return templates[templateType] || templates['single-turn'];
  };

  const downloadTemplate = (templateType) => {
    const templateData = generateTemplate(templateType);
    const csvContent = templateData.map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', templateType === 'unified' ? 'universal-dataset-template.csv' : `${templateType}-dataset-template.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };


  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">{title || (dataset ? 'Edit Dataset' : 'New Dataset')}</h2>
            <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="flex min-h-[400px] max-h-[500px]">
              {/* Left Panel - Dataset Info */}
              <div className="w-1/2 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dataset Name</label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="My Dataset"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Describe your dataset..."
                    />
                  </div>

                  {!dataset && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">How would you like to create this dataset?</label>
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="creationMethod"
                            value="csv"
                            checked={creationMethod === 'csv'}
                            onChange={(e) => setCreationMethod(e.target.value)}
                            className="mr-3 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Upload CSV file</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="creationMethod"
                            value="manual"
                            checked={creationMethod === 'manual'}
                            onChange={(e) => setCreationMethod(e.target.value)}
                            className="mr-3 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Create manually</span>
                        </label>
                      </div>
                    </div>
                  )}

                  {creationMethod === 'csv' ? (
                    <div>
                      <div className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Upload a CSV file</label>
                      </div>
                      
                      {!uploadedFile ? (
                        <div
                          className={`border-2 border-dashed rounded-lg p-6 text-center transition-all cursor-pointer ${
                            isDragOver
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                              : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10'
                          }`}
                          onDragOver={handleDragOver}
                          onDragLeave={handleDragLeave}
                          onDrop={handleDrop}
                          onClick={() => document.getElementById('file-input').click()}
                        >
                          <div className="flex flex-col items-center">
                            <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-3">
                              <CloudArrowUpIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                            </div>
                            <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">
                              {isProcessing ? 'Processing file...' : 'Select a csv file or drag and drop here'}
                            </p>
                            <button
                              type="button"
                              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                              disabled={isProcessing}
                            >
                              {isProcessing ? 'Processing...' : 'Select File'}
                            </button>
                          </div>
                          <input
                            id="file-input"
                            type="file"
                            accept=".csv"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                        </div>
                      ) : (
                        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md">
                          <div className="flex items-center">
                            <DocumentTextIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
                            <span className="text-gray-700 dark:text-gray-300 text-sm">{uploadedFile.name}</span>
                          </div>
                          <button
                            type="button"
                            onClick={removeFile}
                            className="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                      
                      {/* Help text */}
                      <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-xs text-blue-700 dark:text-blue-300">
                            <strong>🎯 Universal Template:</strong>
                          </div>
                          <button
                            type="button"
                            className="text-xs px-3 py-1 bg-blue-600 dark:bg-blue-700 text-white rounded hover:bg-blue-700 dark:hover:bg-blue-600"
                            onClick={() => downloadTemplate('unified')}
                          >
                            📥 Download Template
                          </button>
                        </div>
                        <div className="text-xs text-blue-700 dark:text-blue-300">
                          Download the template to see examples of all supported dataset types in one CSV. 
                          Delete the rows you don't need and modify the rest for your use case!
                        </div>
                        <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                          <strong>Smart Detection:</strong> Our system automatically detects single-turn Q&A, conversations, RAG, medical cases, classification, and generation tasks.
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {dataset ? 'Edit dataset rows' : 'Create rows manually'}
                      </label>
                      
                      {existingDataLoading && (
                        <div className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-4">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400 mr-2"></div>
                          <span className="text-blue-700 dark:text-blue-300 text-sm">Loading existing dataset rows...</span>
                        </div>
                      )}
                      
                      {existingDataError && (
                        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                          <p className="text-red-700 dark:text-red-300 text-sm">{existingDataError}</p>
                          <p className="text-red-600 dark:text-red-400 text-xs mt-1">You can still add new rows below.</p>
                        </div>
                      )}
                      
                      <div className="space-y-3 max-h-64 overflow-y-auto">
                        {manualRows.map((row, index) => (
                          <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500 dark:text-gray-400">Row {index + 1}</span>
                                <button
                                  type="button"
                                  onClick={() => toggleRowType(index)}
                                  className="text-xs px-2 py-1 rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
                                  title={`Switch to ${row.rowType === 'single' ? 'conversation' : 'single-turn'} mode`}
                                >
                                  {row.rowType === 'single' ? '💬 Make Conversation' : '❓ Make Single-Turn'}
                                </button>
                              </div>
                              {manualRows.length > 1 && (
                                <button
                                  type="button"
                                  onClick={() => removeManualRow(index)}
                                  className="text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 text-xs"
                                >
                                  Remove
                                </button>
                              )}
                            </div>
                            
                            {row.rowType === 'single' ? (
                              // Single-turn input
                              <div className="space-y-2">
                                <input
                                  type="text"
                                  placeholder="Question or prompt"
                                  value={row.input.question || ''}
                                  onChange={(e) => updateManualRow(index, 'input.question', e.target.value)}
                                  className="w-full px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                />
                                <input
                                  type="text"
                                  placeholder="Expected output (optional)"
                                  value={row.expected_output || ''}
                                  onChange={(e) => updateManualRow(index, 'expected_output', e.target.value)}
                                  className="w-full px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                            ) : (
                              // Multi-turn conversation input
                              <div className="space-y-3">
                                {/* Scenario */}
                                <div>
                                  <label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 block">Scenario:</label>
                                  <input
                                    type="text"
                                    placeholder="Describe the conversation scenario..."
                                    value={row.input.scenario || ''}
                                    onChange={(e) => updateConversationRow(index, 'scenario', e.target.value)}
                                    className="w-full px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                                  />
                                </div>
                                
                                {/* Turns */}
                                <div>
                                  <div className="flex items-center justify-between mb-2">
                                    <label className="text-xs font-medium text-gray-600 dark:text-gray-400">Conversation Turns:</label>
                                    <button
                                      type="button"
                                      onClick={() => addTurnToConversation(index)}
                                      className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800"
                                    >
                                      + Add Turn
                                    </button>
                                  </div>
                                  <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {(row.input.turns || []).map((turn, turnIndex) => (
                                      <div key={turnIndex} className="flex gap-2">
                                        <select
                                          value={turn.role}
                                          onChange={(e) => updateConversationRow(index, 'turn_role', e.target.value, turnIndex)}
                                          className="w-20 px-2 py-1 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                        >
                                          <option value="user">👤 User</option>
                                          <option value="assistant">🤖 AI</option>
                                          <option value="system">⚙️ System</option>
                                        </select>
                                        <input
                                          type="text"
                                          placeholder="What does this role say?"
                                          value={turn.content}
                                          onChange={(e) => updateConversationRow(index, 'turn_content', e.target.value, turnIndex)}
                                          className="flex-1 px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                        {row.input.turns.length > 1 && (
                                          <button
                                            type="button"
                                            onClick={() => removeTurnFromConversation(index, turnIndex)}
                                            className="text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300 text-xs px-2"
                                            title="Remove turn"
                                          >
                                            ×
                                          </button>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                
                                {/* Expected Outcome */}
                                <div>
                                  <label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 block">Expected Outcome:</label>
                                  <input
                                    type="text"
                                    placeholder="What should be the result of this conversation?"
                                    value={row.expected_outcome || ''}
                                    onChange={(e) => updateConversationRow(index, 'expected_outcome', e.target.value)}
                                    className="w-full px-2 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-gray-900 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2 mt-3">
                        <button
                          type="button"
                          onClick={addManualRow}
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 text-sm"
                        >
                          + Add Single-Turn Row
                        </button>
                        <button
                          type="button"
                          onClick={addConversationRow}
                          className="flex-1 px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-md text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 text-sm"
                        >
                          💬 Add Conversation Row
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Panel - Preview */}
              <div className="w-1/2 p-6 bg-gray-50 dark:bg-gray-800 overflow-y-auto">
                {creationMethod === 'csv' ? (
                  fileData ? (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Dataset Structure Preview</h3>
                      <div className="mb-4 text-sm text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/20 px-3 py-2 rounded">
                        Intelligent mapping applied: CSV → Dataset format
                      </div>
                      <div className="overflow-x-auto">
                        <table className="min-w-full">
                          <thead>
                            <tr className="border-b border-gray-300">
                              <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Row</th>
                              <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Input</th>
                              <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Expected Output</th>
                              <th className="text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase px-2 py-2">Metadata</th>
                            </tr>
                          </thead>
                          <tbody className="text-sm">
                            {fileData.rows.map((row, index) => (
                              <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                                <td className="px-2 py-2 text-gray-700 dark:text-gray-300">{row.rowNumber}</td>
                                <td className="px-2 py-2 text-gray-700 dark:text-gray-300 max-w-xs">
                                  <div className="truncate">
                                    {typeof row.mapped.input === 'object' 
                                      ? JSON.stringify(row.mapped.input) 
                                      : row.mapped.input || <span className="text-gray-400 dark:text-gray-500">Empty</span>}
                                  </div>
                                </td>
                                <td className="px-2 py-2 text-gray-700 dark:text-gray-300 max-w-xs">
                                  <div className="truncate">
                                    {row.mapped.expected_output || <span className="text-gray-400 dark:text-gray-500">Empty</span>}
                                  </div>
                                </td>
                                <td className="px-2 py-2 text-gray-700 dark:text-gray-300 max-w-xs">
                                  <div className="truncate">
                                    {Object.keys(row.mapped.metadata).length > 0 
                                      ? JSON.stringify(row.mapped.metadata) 
                                      : <span className="text-gray-400 dark:text-gray-500">Empty</span>}
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <div className="mt-4 space-y-2">
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Showing first 5 rows of {fileData.totalRows} total rows
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded">
                          <strong>Original columns:</strong> {fileData.headers.join(', ')}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
                        <p className="text-gray-500 dark:text-gray-400">Upload a CSV file to preview columns</p>
                      </div>
                    </div>
                  )
                ) : (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Manual Dataset Preview</h3>
                    <div className="mb-4 text-sm text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/20 px-3 py-2 rounded">
                      Manual entry mode: Create rows one by one
                    </div>
                    <div className="space-y-3 max-h-80 overflow-y-auto">
                      {manualRows.map((row, index) => (
                        <div key={index} className="bg-white dark:bg-gray-700 rounded-md p-3 border border-gray-200 dark:border-gray-600">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Record {index + 1}</div>
                            <div className={`text-xs px-2 py-1 rounded-full ${
                              row.rowType === 'conversation' 
                                ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300' 
                                : 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
                            }`}>
                              {row.rowType === 'conversation' ? '💬 Conversation' : '❓ Single-Turn'}
                            </div>
                          </div>
                          
                          {row.rowType === 'conversation' ? (
                            // Conversation preview
                            <div className="space-y-2">
                              {/* Scenario */}
                              {row.input.scenario && (
                                <div>
                                  <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">Scenario:</div>
                                  <div className="text-sm text-gray-700 dark:text-gray-300 bg-purple-50 dark:bg-purple-900/20 p-2 rounded border">
                                    {row.input.scenario || <span className="text-gray-400 dark:text-gray-500">No scenario</span>}
                                  </div>
                                </div>
                              )}
                              
                              {/* Turns */}
                              {row.input.turns && row.input.turns.length > 0 && (
                                <div>
                                  <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">
                                    Conversation ({row.input.turns.length} turns):
                                  </div>
                                  <div className="space-y-1 bg-purple-50 dark:bg-purple-900/20 p-2 rounded border max-h-32 overflow-y-auto">
                                    {row.input.turns.map((turn, turnIndex) => (
                                      <div key={turnIndex} className="text-sm">
                                        <span className={`font-medium ${
                                          turn.role === 'user' ? 'text-blue-600 dark:text-blue-400' : 
                                          turn.role === 'assistant' ? 'text-green-600 dark:text-green-400' : 
                                          'text-gray-600 dark:text-gray-400'
                                        }`}>
                                          {turn.role === 'user' ? '👤' : turn.role === 'assistant' ? '🤖' : '⚙️'} {turn.role}:
                                        </span>
                                        <span className="ml-2 text-gray-700 dark:text-gray-300">
                                          {turn.content || <span className="text-gray-400 dark:text-gray-500">Empty</span>}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                              
                              {/* Expected Outcome */}
                              {row.expected_outcome && (
                                <div>
                                  <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">Expected Outcome:</div>
                                  <div className="text-sm text-gray-700 dark:text-gray-300 bg-green-50 dark:bg-green-900/20 p-2 rounded border">
                                    {row.expected_outcome || <span className="text-gray-400 dark:text-gray-500">No expected outcome</span>}
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : (
                            // Single-turn preview
                            <div className="space-y-2">
                              {/* Input */}
                              <div>
                                <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">Input:</div>
                                <div className="text-sm text-gray-700 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 p-2 rounded border">
                                  {typeof row.input === 'object' ? 
                                    Object.entries(row.input).map(([key, value]) => (
                                      <div key={key} className="mb-1">
                                        <span className="font-medium text-blue-800 dark:text-blue-400">{key}:</span> {String(value) || <span className="text-gray-400 dark:text-gray-500">Empty</span>}
                                      </div>
                                    )) : (String(row.input) || <span className="text-gray-400 dark:text-gray-500">Empty</span>)
                                  }
                                </div>
                              </div>
                              
                              {/* Expected Output */}
                              {row.expected_output && (
                                <div>
                                  <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">Expected Output:</div>
                                  <div className="text-sm text-gray-700 dark:text-gray-300 bg-green-50 dark:bg-green-900/20 p-2 rounded border">
                                    {row.expected_output || <span className="text-gray-400 dark:text-gray-500">No expected output</span>}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-xs text-gray-600 dark:text-gray-400">
                      {manualRows.length} row{manualRows.length !== 1 ? 's' : ''} defined ({manualRows.filter(r => r.rowType === 'conversation').length} conversations, {manualRows.filter(r => r.rowType !== 'conversation').length} single-turn)
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200 space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={
                  isProcessing || 
                  !formData.name ||
                  (creationMethod === 'csv' && (!uploadedFile || !fileData)) ||
                  (creationMethod === 'manual' && manualRows.length === 0)
                }
                className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Saving...' : (dataset ? 'Update Dataset' : 'Save Dataset')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function DatasetDetailModal({ dataset, onClose, onEdit, onDelete }) {
  const [previewData, setPreviewData] = React.useState(null);
  const [previewLoading, setPreviewLoading] = React.useState(true);
  const [previewError, setPreviewError] = React.useState(null);


  // Fetch dataset preview when modal opens
  React.useEffect(() => {
    const fetchPreview = async () => {
      if (!dataset?.id) {
        console.log('No dataset ID available:', dataset);
        setPreviewError('Dataset ID not available');
        setPreviewLoading(false);
        return;
      }
      
      try {
        setPreviewLoading(true);
        setPreviewError(null);
        console.log('Fetching preview for dataset ID:', dataset.id);
        const response = await api.get(`/datasets/${dataset.id}/preview?limit=10`);
        console.log('Preview response:', response.data);
        setPreviewData(response.data);
      } catch (error) {
        console.error('Error fetching dataset preview:', error);
        console.error('Error details:', error.response?.data || error.message);
        setPreviewError(`Failed to load dataset preview: ${error.response?.data?.error || error.message}`);
      } finally {
        setPreviewLoading(false);
      }
    };

    fetchPreview();
  }, [dataset?.id]);

  const tags = (() => {
    const name = dataset.name || '';
    if (name.includes('X-Ray')) return ['Radiology', 'Imaging'];
    if (name.includes('Clinical')) return ['Clinical', 'Text'];
    if (name.includes('Laboratory')) return ['Laboratory', 'Results'];
    return ['Dataset'];
  })();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-screen overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{dataset.name}</h2>
            <p className="text-gray-600 dark:text-gray-300 mt-1">{dataset.description || 'No description available'}</p>
          </div>
          <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 text-2xl">✕</button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left column - Essential Info & Actions */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Dataset Information</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Type:</span>
                  <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border ${getTypeColor(dataset.type)}`}>
                    <span className="mr-1">{getDatasetIcon(dataset.type)}</span>
                    {getDatasetTypeLabel(dataset.type)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Records:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{(dataset.record_count || 0).toLocaleString()}</span>
                </div>
                {dataset.created_at && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Created:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{new Date(dataset.created_at).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 mt-8">Actions</h3>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={() => onEdit && onEdit(dataset)}
                  className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-lg"
                >
                  ✏️ Edit Dataset
                </button>
                <button
                  onClick={() => onDelete && onDelete(dataset.id)}
                  className="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700"
                >
                  🗑️ Delete
                </button>
              </div>
            </div>

            {/* Right column - Dataset Preview */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Dataset Preview</h3>
              
              {previewLoading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600 dark:text-gray-300">Loading preview...</span>
                </div>
              ) : previewError ? (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
                  <p className="text-red-700 dark:text-red-300 text-sm">{previewError}</p>
                </div>
              ) : previewData ? (
                <div className="space-y-4">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    Showing {previewData.preview_count} of {previewData.total_records} records
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                    {previewData.preview && previewData.preview.length > 0 ? (
                      <div className="space-y-3">
                        {previewData.preview.map((row, index) => (
                          <div key={index} className="bg-white dark:bg-gray-800 rounded-md p-3 border border-gray-200 dark:border-gray-600">
                            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Record {index + 1}</div>
                            
                            {/* Input - Handle both single-turn and conversation */}
                            <div className="mb-2">
                              <div className="text-xs font-medium text-blue-700 dark:text-blue-400 mb-1">Input:</div>
                              <div className="text-sm text-gray-700 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 p-2 rounded border border-gray-200 dark:border-blue-700">
                                {typeof row.input === 'object' ? (
                                  // Check if it's a conversation
                                  row.input.scenario || row.input.turns ? (
                                    <div className="space-y-2">
                                      {/* Conversation format */}
                                      {row.input.scenario && (
                                        <div>
                                          <span className="font-medium text-purple-800 dark:text-purple-300">Scenario:</span> {row.input.scenario}
                                        </div>
                                      )}
                                      {row.input.turns && row.input.turns.length > 0 && (
                                        <div>
                                          <span className="font-medium text-purple-800 dark:text-purple-300">Conversation ({row.input.turns.length} turns):</span>
                                          <div className="mt-1 space-y-1 pl-2 border-l-2 border-purple-200 dark:border-purple-600">
                                            {row.input.turns.map((turn, turnIndex) => (
                                              <div key={turnIndex} className="text-xs">
                                                <span className={`font-medium ${
                                                  turn.role === 'user' ? 'text-blue-600 dark:text-blue-400' : 
                                                  turn.role === 'assistant' ? 'text-green-600 dark:text-green-400' : 
                                                  'text-gray-600 dark:text-gray-400'
                                                }`}>
                                                  {turn.role === 'user' ? '👤' : turn.role === 'assistant' ? '🤖' : '⚙️'} {turn.role}:
                                                </span>
                                                <span className="ml-1 text-gray-700 dark:text-gray-300">{turn.content}</span>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    // Regular object format
                                    Object.entries(row.input).map(([key, value]) => (
                                      <div key={key} className="mb-1">
                                        <span className="font-medium text-blue-800 dark:text-blue-300">{key}:</span> {String(value)}
                                      </div>
                                    ))
                                  )
                                ) : String(row.input)}
                              </div>
                            </div>
                            
                            {/* Expected Output or Expected Outcome */}
                            {(row.expected_output || row.expected_outcome) && (
                              <div className="mb-2">
                                <div className="text-xs font-medium text-green-700 dark:text-green-400 mb-1">
                                  {row.expected_outcome ? 'Expected Outcome:' : 'Expected Output:'}
                                </div>
                                <div className="text-sm text-gray-700 dark:text-gray-300 bg-green-50 dark:bg-green-900/20 p-2 rounded border border-gray-200 dark:border-green-700">
                                  {String(row.expected_outcome || row.expected_output)}
                                </div>
                              </div>
                            )}
                            
                            {/* Metadata */}
                            {row.metadata && Object.keys(row.metadata).length > 0 && (
                              <div>
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Metadata:</div>
                                <div className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600">
                                  {Object.entries(row.metadata).map(([key, value]) => (
                                    <div key={key} className="mb-1">
                                      <span className="font-medium text-gray-900 dark:text-white">{key}:</span> {String(value)}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-gray-500 dark:text-gray-400 text-center py-4">
                        No preview data available
                      </div>
                    )}
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button onClick={onClose} className="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600">Close</button>
        </div>
      </div>
    </div>
  );
}

export default DatasetManagement;