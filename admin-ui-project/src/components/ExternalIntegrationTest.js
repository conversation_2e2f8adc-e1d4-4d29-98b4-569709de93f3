// Quick Test Component for External Integration
// Add this to your existing components to test the integration immediately

import React, { useState } from 'react';
import { ToggleLeft, ToggleRight, Trash2, User } from 'lucide-react';
import ExternalIntegrationDialog from './ExternalIntegrationDialog';
import useExternalIntegration from '../hooks/useExternalIntegration';
import '../styles/ExternalIntegration.css';

const ExternalIntegrationTest = () => {
  const [policies, setPolicies] = useState([
    {
      policy_id: '5ce919b4-7fb2-4ecf-af56-ac103f3701d6',
      name: 'DRG - Policy 1',
      is_active: true,
      category: 'Data Privacy',
      severity: 'medium'
    },
    {
      policy_id: '597df3ba-ae0d-4e38-ba40-aab0f84beb88', 
      name: 'DRG - Policy 2',
      is_active: true,
      category: 'Data Privacy',
      severity: 'medium'
    },
    {
      policy_id: '1cad0241-8fff-4fa2-aa2c-bed60f28d67f',
      name: 'DRG - Policy 3', 
      is_active: false,
      category: 'Data Privacy',
      severity: 'medium'
    }
  ]);

  const [testAgent] = useState({
    agent_id: '89c0d4ee-b4f0-4494-a8fc-3fe6e79de729',
    name: 'CDI Agent',
    is_active: true
  });

  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handlePolicyToggle,
    handleAgentPolicyDelete,
    handleAgentToggle
  } = useExternalIntegration();

  // Simulate policy update in Vitea (replace with real API call)
  const updatePolicyInVitea = async (policyId, isActive) => {
    console.log(`[VITEA] Updating policy ${policyId} to active: ${isActive}`);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  };

  // Simulate agent update in Vitea (replace with real API call)
  const updateAgentInVitea = async (agentId, isActive) => {
    console.log(`[VITEA] Updating agent ${agentId} to active: ${isActive}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  };

  // Test policy toggle
  const onPolicyToggleClick = (policy) => {
    const newStatus = !policy.is_active;
    
    handlePolicyToggle(
      policy.policy_id,
      newStatus,
      policy.name,
      async () => {
        await updatePolicyInVitea(policy.policy_id, newStatus);
        setPolicies(prev => prev.map(p => 
          p.policy_id === policy.policy_id 
            ? { ...p, is_active: newStatus }
            : p
        ));
      }
    );
  };

  // Test agent policy deletion
  const onDeleteAgentPolicy = (policy) => {
    handleAgentPolicyDelete(
      testAgent.agent_id,
      policy.policy_id,
      testAgent.name,
      policy.name,
      async () => {
        console.log(`[VITEA] Removing policy ${policy.policy_id} from agent ${testAgent.agent_id}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      },
      async () => {
        // Return remaining active policy IDs
        return policies
          .filter(p => p.is_active && p.policy_id !== policy.policy_id)
          .map(p => p.policy_id);
      }
    );
  };

  // Test agent toggle
  const onAgentToggle = () => {
    const newStatus = !testAgent.is_active;
    
    handleAgentToggle(
      testAgent.agent_id,
      newStatus,
      testAgent.name,
      async () => {
        await updateAgentInVitea(testAgent.agent_id, newStatus);
      },
      async () => {
        // Return current active policy IDs
        return policies.filter(p => p.is_active).map(p => p.policy_id);
      }
    );
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="bg-blue-600 text-white p-4">
          <h1 className="text-2xl font-bold">🔗 External Integration Test</h1>
          <p className="mt-2 opacity-90">Test external system integration with mock data</p>
        </div>

        <div className="p-6 space-y-8">
          {/* Policy Configuration Test */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-900">📋 Policy Configuration</h2>
            <p className="text-gray-600 mb-4">Toggle policies to test global policy updates</p>
            
            <div className="space-y-3">
              {policies.map(policy => (
                <div key={policy.policy_id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">{policy.name}</h3>
                    <p className="text-sm text-gray-500">
                      Category: {policy.category} | Severity: {policy.severity}
                    </p>
                  </div>
                  
                  <button
                    onClick={() => onPolicyToggleClick(policy)}
                    className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      policy.is_active
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {policy.is_active ? (
                      <>
                        <ToggleRight className="w-4 h-4" />
                        Active
                      </>
                    ) : (
                      <>
                        <ToggleLeft className="w-4 h-4" />
                        Inactive
                      </>
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Agent Management Test */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-900">👤 Agent Management</h2>
            <p className="text-gray-600 mb-4">Test agent policy deletion and agent status changes</p>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <User className="w-6 h-6 text-blue-500" />
                  <div>
                    <h3 className="font-medium text-gray-900">{testAgent.name}</h3>
                    <p className="text-sm text-gray-500">Test Agent for Integration</p>
                  </div>
                </div>
                
                <button
                  onClick={onAgentToggle}
                  className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    testAgent.is_active
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {testAgent.is_active ? (
                    <>
                      <ToggleRight className="w-4 h-4" />
                      Active
                    </>
                  ) : (
                    <>
                      <ToggleLeft className="w-4 h-4" />
                      Inactive
                    </>
                  )}
                </button>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Active Policies:</h4>
                <div className="space-y-2">
                  {policies.filter(p => p.is_active).map(policy => (
                    <div key={policy.policy_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm text-gray-700">{policy.name}</span>
                      <button
                        onClick={() => onDeleteAgentPolicy(policy)}
                        className="flex items-center gap-1 px-2 py-1 text-red-600 hover:bg-red-50 rounded text-sm transition-colors"
                      >
                        <Trash2 className="w-3 h-3" />
                        Remove
                      </button>
                    </div>
                  ))}
                  {policies.filter(p => p.is_active).length === 0 && (
                    <p className="text-sm text-gray-500 italic">No active policies assigned</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Debug Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">🔧 Debug Information</h3>
            <p className="text-sm text-gray-600">
              Check browser console for detailed logs. 
              Environment debug mode: <strong>{process.env.REACT_APP_EXTERNAL_INTEGRATION_DEBUG || 'false'}</strong>
            </p>
            <p className="text-sm text-gray-600 mt-1">
              External system URL: <strong>{process.env.REACT_APP_EXTERNAL_SYSTEM_BASE_URL || 'Not configured'}</strong>
            </p>
          </div>
        </div>
      </div>

      {/* External Integration Dialog */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </div>
  );
};

export default ExternalIntegrationTest;