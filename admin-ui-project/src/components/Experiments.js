import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import {
  PlusIcon,
  BeakerIcon,
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  DocumentArrowUpIcon,
  ComputerDesktopIcon,
  CloudArrowUpIcon,
  DocumentTextIcon,
  ArrowUpTrayIcon,
  XMarkIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  TrashIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { api } from '../utils/evaluationApi';
import Breadcrumb from './testing/Breadcrumb';
import EvaluationModal from './testing/EvaluationModal';
import EvidenceModal from './testing/EvidenceModal';

function Experiments() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [experimentToDelete, setExperimentToDelete] = useState(null);

  useEffect(() => {
    loadSessions();
  }, []);

  // Only poll for updates if there are running sessions
  useEffect(() => {
    const hasRunningSessions = sessions.some(session => 
      session.status === 'running' || session.status === 'in_progress'
    );

    if (hasRunningSessions) {
      const interval = setInterval(loadSessions, 5000);
      return () => clearInterval(interval);
    }
  }, [sessions]);

  // Check URL parameters to auto-open create modal
  useEffect(() => {
    const shouldCreate = searchParams.get('create');
    const datasetId = searchParams.get('dataset');
    
    if (shouldCreate === 'true') {
      setShowCreateModal(true);
      // Clear URL parameters after opening modal
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  const loadSessions = async () => {
    try {
      // For now, using experiments endpoint as test sessions
      const response = await api.get('/experiments');
      const sessionsData = response.data.experiments || [];
      
      setSessions(sessionsData);
    } catch (error) {
      console.error('Error loading test sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getExecutionModeIcon = (mode) => {
    switch (mode) {
      case 'automated':
        return <ComputerDesktopIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
      case 'manual':
        return <DocumentArrowUpIcon className="h-5 w-5 text-orange-600 dark:text-orange-400" />;
      case 'hybrid':
        return <CloudArrowUpIcon className="h-5 w-5 text-purple-600 dark:text-purple-400" />;
      default:
        return <BeakerIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300';
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300';
      case 'paused':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getExecutionModeLabel = (session) => {
    // Determine execution mode from session data
    if (session.agent_config?.type === 'manual') return 'Manual';
    if (session.agent_config?.type === 'hybrid') return 'Hybrid';
    return 'Automated';
  };

  const handleViewEvidence = async (session) => {
    setSelectedSession(session);
    setShowEvidenceModal(true);
  };

  const handleEvaluate = async (session) => {
    setSelectedSession(session);
    setShowEvaluationModal(true);
  };

  const handleDeleteClick = (experiment) => {
    setExperimentToDelete(experiment);
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!experimentToDelete) return;
    
    try {
      await api.delete(`/experiments/${experimentToDelete.id}`);
      // Refresh the list
      loadSessions();
      // Close the dialog
      setShowDeleteConfirm(false);
      setExperimentToDelete(null);
    } catch (error) {
      console.error('Error deleting experiment:', error);
      alert('Failed to delete experiment: ' + (error.response?.data?.error || error.message));
    }
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Experiments', href: '/experiments' }
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <Breadcrumb items={breadcrumbItems} />
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Experiments</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            Run experiments on vendor agents through various execution modes
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Test Session
          </button>
        </div>
      </div>


      {/* Test Sessions List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Active Experiments</h2>
            <div className="flex space-x-3">
              <select className="block px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500">
                <option value="">All Modes</option>
                <option value="automated">Automated</option>
                <option value="manual">Manual</option>
                <option value="hybrid">Hybrid</option>
                <option value="import">Import</option>
              </select>
              <select className="block px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500">
                <option value="">All Status</option>
                <option value="planning">Planning</option>
                <option value="running">Running</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>
        </div>
        
        {loading ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="px-6 py-4 animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {sessions.length > 0 ? (
              sessions.map((session) => (
                <div key={session.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                          {session.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {session.description || 'Test session for vendor agent evaluation'}
                        </p>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>Mode: {getExecutionModeLabel(session)}</span>
                          <span>Dataset: {session.dataset_name || 'None'}</span>
                          <span>Vendor: {session.agent_config?.name || 'Multiple'}</span>
                          {session.evidence_count && (
                            <span>Evidence: {session.evidence_count}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      {session.status === 'running' && (
                        <div className="flex-shrink-0">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {session.progress || 0}%
                          </div>
                          <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${session.progress || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                      <div className="flex flex-col items-end space-y-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                          {session.status}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(session.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        {session.status === 'planning' && (
                          <button className="text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300 text-sm font-medium">
                            Start
                          </button>
                        )}
                        {session.status === 'running' && (
                          <button className="text-orange-600 dark:text-orange-400 hover:text-orange-500 dark:hover:text-orange-300 text-sm font-medium">
                            <PauseIcon className="h-4 w-4" />
                          </button>
                        )}
                        {session.status === 'paused' && (
                          <button className="text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300 text-sm font-medium">
                            <PlayIcon className="h-4 w-4" />
                          </button>
                        )}
                        {(session.status === 'completed' || session.evidence_count > 0) && (
                          <button 
                            onClick={() => handleViewEvidence(session)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium"
                          >
                            View Evidence
                          </button>
                        )}
                        <button 
                          onClick={() => handleEvaluate(session)}
                          className="text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 text-sm font-medium"
                        >
                          Evaluate
                        </button>
                        <button 
                          onClick={() => handleDeleteClick(session)}
                          className="text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
                          title="Delete experiment"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-12 text-center">
                <BeakerIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No experiments found</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first experiment.
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Experiment
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create Test Session Modal */}
      {showCreateModal && (
        <CreateTestSessionModal 
          onClose={() => setShowCreateModal(false)}
          onSave={() => {
            setShowCreateModal(false);
            loadSessions();
          }}
          preSelectedDatasetId={searchParams.get('dataset')}
        />
      )}

      {/* Evidence Modal */}
      {showEvidenceModal && selectedSession && (
        <EvidenceModal 
          session={selectedSession}
          onClose={() => {
            setShowEvidenceModal(false);
            setSelectedSession(null);
          }}
        />
      )}

      {/* Evaluation Modal */}
      {showEvaluationModal && selectedSession && (
        <EvaluationModal 
          session={selectedSession}
          onClose={() => {
            setShowEvaluationModal(false);
            setSelectedSession(null);
          }}
          onEvaluationComplete={() => {
            loadSessions(); // Refresh sessions after evaluation
            setShowEvaluationModal(false);
            setSelectedSession(null);
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && experimentToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white text-center mb-2">
                Delete Experiment
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                Are you sure you want to delete "{experimentToDelete.name}"? This action cannot be undone.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setExperimentToDelete(null);
                  }}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteConfirm}
                  className="flex-1 px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function CreateTestSessionModal({ onClose, onSave, preSelectedDatasetId }) {
  const [mode, setMode] = useState('');
  const [datasets, setDatasets] = useState([]);
  const [loadingDatasets, setLoadingDatasets] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    dataset_id: preSelectedDatasetId || '',
    vendor: '',
    execution_mode: ''
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const [manualTestingData, setManualTestingData] = useState([]);
  const [showManualTestingModal, setShowManualTestingModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showTemplateHelp, setShowTemplateHelp] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [csvFileToImport, setCsvFileToImport] = useState(null);

  // Load available datasets when modal opens
  useEffect(() => {
    const loadDatasets = async () => {
      try {
        setLoadingDatasets(true);
        const response = await api.get('/datasets?status=active');
        setDatasets(response.data.datasets || []);
      } catch (error) {
        console.error('Error loading datasets:', error);
      } finally {
        setLoadingDatasets(false);
      }
    };

    loadDatasets();
  }, []);

  const executionModes = [
    {
      id: 'automated',
      name: 'Automated Testing',
      description: 'API-based testing with full automation',
      icon: <ComputerDesktopIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />,
      requirements: 'Requires API access and credentials'
    },
    {
      id: 'manual',
      name: 'Manual Testing',
      description: 'Manual testing with guided entry, templates, or result imports',
      icon: <DocumentArrowUpIcon className="h-8 w-8 text-orange-600 dark:text-orange-400" />,
      requirements: 'No integration required - flexible data input options'
    },
    {
      id: 'hybrid',
      name: 'Hybrid Testing',
      description: 'Mix of automated and manual testing',
      icon: <CloudArrowUpIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />,
      requirements: 'Partial API access'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Create New Experiment</h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            Choose how you want to test your vendor agents
          </p>
        </div>

        <div className="p-6">
          {!mode ? (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Execution Mode</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {executionModes.map((execMode) => (
                  <button
                    key={execMode.id}
                    onClick={() => setMode(execMode.id)}
                    className="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all text-left"
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0">{execMode.icon}</div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">{execMode.name}</h4>
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{execMode.description}</p>
                        <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">{execMode.requirements}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <div>
              <div className="mb-4">
                <button
                  onClick={() => setMode('')}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300"
                >
                  ← Back to mode selection
                </button>
              </div>

              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Configure {executionModes.find(m => m.id === mode)?.name}
              </h3>

              {/* Info box explaining the flow */}
              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 text-sm text-blue-700 dark:text-blue-300">
                    <p className="font-medium">How it works:</p>
                    <p className="mt-1">
                      <strong>Dataset</strong> = Your test cases (inputs + expected outputs)<br/>
                      <strong>Experiment</strong> = Running those test cases through a model to get actual outputs
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {/* Only show name/description for non-manual modes */}
                {mode !== 'manual' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Experiment Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md"
                        placeholder="e.g., GPT-4 Medical Q&A Test"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md"
                        rows="3"
                        placeholder="Describe the test objectives..."
                      />
                    </div>
                  </>
                )}

                {/* Dataset selection - only for non-manual modes */}
                {mode !== 'manual' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Test Dataset <span className="text-red-500">*</span>
                    </label>
                    {loadingDatasets ? (
                      <div className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-md">
                        <span className="text-gray-500 dark:text-gray-400">Loading datasets...</span>
                      </div>
                    ) : (
                      <select
                        value={formData.dataset_id}
                        onChange={(e) => setFormData({ ...formData, dataset_id: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md"
                        required
                      >
                        <option value="">Select test cases to run...</option>
                        {datasets.map(dataset => (
                          <option key={dataset.id} value={dataset.id}>
                            {dataset.name} ({dataset.record_count} test cases)
                          </option>
                        ))}
                      </select>
                    )}
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Choose the test cases you want to execute in this experiment.
                      {' '}
                      <Link to="/datasets?create=true" className="text-blue-500 hover:text-blue-600 inline-block mt-1">
                        Create new dataset →
                      </Link>
                    </p>
                  </div>
                )}

                {mode === 'automated' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Vendor Configuration
                    </label>
                    <select
                      value={formData.vendor}
                      onChange={(e) => setFormData({ ...formData, vendor: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md"
                    >
                      <option value="">Select vendor...</option>
                      <option value="openai">OpenAI GPT-4</option>
                      <option value="anthropic">Anthropic Claude</option>
                      <option value="custom">Custom API</option>
                    </select>
                  </div>
                )}


                {mode === 'manual' && (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Choose Your Input Method</h4>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Select how you want to provide test results:
                      </p>
                      <ul className="mt-2 text-xs text-blue-600 dark:text-blue-400 space-y-1">
                        <li>• <strong>Guided Entry:</strong> Test against a dataset step-by-step</li>
                        <li>• <strong>Import Results:</strong> Upload pre-computed results (no dataset needed)</li>
                      </ul>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Guided Manual Entry */}
                      <button
                        type="button"
                        onClick={() => {
                          // Will ask for dataset selection in the next step
                          setShowManualTestingModal(true);
                        }}
                        className="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors text-left"
                      >
                        <div className="flex items-start">
                          <DocumentTextIcon className="h-8 w-8 text-orange-600 dark:text-orange-400 mt-1" />
                          <div className="ml-4">
                            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Guided Entry</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              Test your model against a dataset step-by-step with guided forms.
                            </p>
                          </div>
                        </div>
                      </button>
                      
                      {/* Import Results with Drag & Drop */}
                      <div className="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-lg">
                        <div className="flex items-start">
                          <CloudArrowUpIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mt-1" />
                          <div className="ml-4 w-full">
                            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Import Results</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                              Upload test results from any CSV, JSON, or Excel file. Our smart parser will automatically detect your column format.
                            </p>
                            
                            {/* Drag & Drop Area */}
                            <div
                              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                                isDragOver
                                  ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/10'
                                  : isProcessingFile
                                  ? 'border-blue-300 bg-blue-50 dark:bg-blue-900/10'
                                  : 'border-gray-300 dark:border-gray-600 hover:border-purple-400 dark:hover:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/10'
                              }`}
                              onDragOver={handleDragOver}
                              onDragLeave={handleDragLeave}
                              onDrop={handleDrop}
                            >
                              {isProcessingFile ? (
                                <div className="space-y-2">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto"></div>
                                  <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">Analyzing your file...</p>
                                </div>
                              ) : (
                                <>
                                  <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                                  <div className="mt-4">
                                    <p className="text-lg font-medium text-gray-900 dark:text-white">
                                      {isDragOver ? 'Drop your file here' : 'Drag & drop your file here'}
                                    </p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">or</p>
                                    <label className="mt-2 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 cursor-pointer">
                                      Choose File
                                      <input 
                                        type="file" 
                                        className="sr-only" 
                                        accept=".csv,.json,.xlsx,.xls"
                                        onChange={handleFileUpload}
                                        disabled={isProcessingFile}
                                      />
                                    </label>
                                  </div>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">
                                    CSV, JSON, Excel files up to 10MB
                                  </p>
                                </>
                              )}
                            </div>

                            {/* File Upload Status */}
                            {uploadedFile && !isProcessingFile && (
                              <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                                <div className="flex items-center">
                                  <span className="inline-block w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
                                  <span className="text-sm text-green-700 dark:text-green-300 font-medium">
                                    {uploadedFile.name} ({Math.round(uploadedFile.size / 1024)}KB)
                                  </span>
                                  <button
                                    onClick={() => setShowPreviewModal(true)}
                                    className="ml-auto text-sm text-green-600 dark:text-green-400 hover:text-green-500 dark:hover:text-green-300 underline"
                                  >
                                    Review Mapping →
                                  </button>
                                </div>
                              </div>
                            )}

                            {/* Template Help (Hidden by default) */}
                            <div className="mt-4 pt-4 border-t border-gray-200">
                              {!showTemplateHelp ? (
                                <button
                                  onClick={() => setShowTemplateHelp(true)}
                                  className="text-sm text-gray-500 hover:text-gray-700 flex items-center"
                                >
                                  <span>Need help with file format?</span>
                                  <ChevronRightIcon className="h-4 w-4 ml-1" />
                                </button>
                              ) : (
                                <div className="space-y-2">
                                  <button
                                    onClick={() => setShowTemplateHelp(false)}
                                    className="text-sm text-gray-500 hover:text-gray-700 flex items-center mb-2"
                                  >
                                    <ChevronLeftIcon className="h-4 w-4 mr-1" />
                                    <span>Hide help</span>
                                  </button>
                                  <div className="text-sm text-gray-600 space-y-1">
                                    <p><strong>Don't have the right format?</strong> Download our template:</p>
                                    <button
                                      type="button"
                                      onClick={downloadTemplate}
                                      className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-sm text-blue-600 hover:bg-blue-50 transition-colors"
                                    >
                                      <ArrowUpTrayIcon className="h-4 w-4 mr-1" />
                                      Download CSV Template
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 mr-3"
          >
            Cancel
          </button>
          {mode && (
            <button
              onClick={handleCreateSession}
              disabled={!formData.dataset_id || !formData.name}
              className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Create Experiment
            </button>
          )}
        </div>
      </div>
      
      {/* Manual Testing Modal */}
      {showManualTestingModal && formData.dataset_id && (
        <ManualTestingModal
          onClose={() => setShowManualTestingModal(false)}
          onSave={(data) => {
            setManualTestingData(data);
            setShowManualTestingModal(false);
          }}
          dataset={{ id: formData.dataset_id }}
          experimentId={null}  // Will be set after experiment creation
        />
      )}

      {/* Import Preview Modal */}
      {showPreviewModal && previewData && (
        <ImportPreviewModal
          data={previewData}
          experimentName={formData.name}
          description={formData.description}
          datasetId={formData.dataset_id}
          onClose={() => setShowPreviewModal(false)}
          onConfirmImport={(name, desc, datasetId) => {
            handleImportResults({ 
              name: name || formData.name, 
              description: desc || formData.description,
              dataset_id: datasetId 
            });
            setShowPreviewModal(false);
          }}
        />
      )}

      {/* CSV Import Modal with Dataset Selection */}
      {showImportModal && csvFileToImport && (
        <EvaluationModal
          importMode={true}
          csvFile={csvFileToImport}
          onClose={() => {
            setShowImportModal(false);
            setCsvFileToImport(null);
            setUploadedFile(null);
          }}
          onEvaluationComplete={() => {
            onClose();
            window.location.reload(); // Refresh to show imported experiment
          }}
          shouldNavigate={true}
        />
      )}
    </div>
  );

  // Helper functions
  async function processFile(file) {
    if (!file) return;
    
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    // Check file type
    const validExtensions = ['.csv', '.json', '.xlsx', '.xls'];
    const hasValidExtension = validExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!hasValidExtension) {
      alert('Please upload a CSV, JSON, or Excel file');
      return;
    }

    setUploadedFile(file);
    setCsvFileToImport(file);
    setIsProcessingFile(true);
    
    // For CSV imports, directly show the import modal with dataset selection
    if (file.name.toLowerCase().endsWith('.csv')) {
      setShowImportModal(true);
      setIsProcessingFile(false);
    } else {
      // For other formats, use the existing preview flow
      try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await api.post('/experiments/preview-import', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        
        setPreviewData(response.data);
        setShowPreviewModal(true);
      } catch (error) {
        console.error('Error previewing file:', error);
        alert('Error analyzing file. Please check the format and try again.');
      } finally {
        setIsProcessingFile(false);
      }
    }
  }

  async function handleFileUpload(event) {
    const file = event.target.files[0];
    await processFile(file);
  }

  function handleDragOver(event) {
    event.preventDefault();
    setIsDragOver(true);
  }

  function handleDragLeave(event) {
    event.preventDefault();
    setIsDragOver(false);
  }

  async function handleDrop(event) {
    event.preventDefault();
    setIsDragOver(false);
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      await processFile(files[0]);
    }
  }

  function downloadTemplate() {
    // Generate CSV template based on selected dataset/metrics
    const csvContent = `prompt,expected_output,actual_output,notes\n"What is the capital of France?","Paris","","Example row - fill in actual output"\n`;
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'manual_testing_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  }

  async function handleCreateSession() {
    // Validate required fields
    if (mode !== 'manual' && !formData.dataset_id) {
      alert('Please select a dataset');
      return;
    }
    // For manual mode, either dataset_id OR uploadedFile is required
    if (mode === 'manual' && !formData.dataset_id && !uploadedFile) {
      alert('Please either select a dataset or upload a file with results');
      return;
    }
    if (!formData.name) {
      alert('Please enter an experiment name');
      return;
    }

    // Enhanced session creation logic
    const sessionData = {
      name: formData.name,
      description: formData.description,
      agent_config: {
        name: formData.vendor || 'Manual Testing',
        type: mode === 'manual' ? 'manual' : formData.vendor || 'mock',
        config: {}
      },
      execution_mode: mode,
      uploaded_file: uploadedFile,
      manual_data: manualTestingData
    };
    
    // Only include dataset_id if it's provided
    if (formData.dataset_id) {
      sessionData.dataset_id = formData.dataset_id;
    }

    // For manual mode with file upload, handle as import workflow
    if (mode === 'manual' && uploadedFile) {
      // This will trigger the import results API endpoint
      handleImportResults(sessionData);
    } else {
      // Regular session creation
      try {
        const response = await api.post('/experiments', sessionData);
        if (response.data) {
          onSave();
        }
      } catch (error) {
        console.error('Error creating experiment:', error);
        alert('Failed to create experiment: ' + (error.response?.data?.error || error.message));
      }
    }
  }

  async function handleImportResults(sessionData) {
    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);
      formData.append('experiment_name', sessionData.name);
      formData.append('description', sessionData.description);

      const response = await api.post('/experiments/import-csv', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data) {
        // Success - close modal and refresh
        onSave();
      }
    } catch (error) {
      console.error('Error importing results:', error);
      alert('Error importing file. Please check the file format and try again.');
    }
  }
}

// Manual Testing Modal Component
function ManualTestingModal({ onClose, onSave, dataset, experimentId }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [tests, setTests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load test cases from dataset
  useEffect(() => {
    const loadTestCases = async () => {
      if (!dataset) {
        setError('No dataset provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await api.get(`/datasets/${dataset.id}/preview?limit=1000`);
        const testCases = response.data.preview || [];
        
        // Convert dataset test cases to manual testing format
        const formattedTests = testCases.map((testCase, index) => ({
          id: index + 1,
          test_case_id: testCase.test_id || `test_${index}`,
          input: testCase.input,
          expected_output: testCase.expected_output || testCase.expected_outcome || '',
          actual_output: '',
          notes: '',
          metadata: testCase.metadata || {}
        }));
        
        setTests(formattedTests);
      } catch (error) {
        console.error('Error loading test cases:', error);
        setError('Failed to load test cases');
      } finally {
        setLoading(false);
      }
    };

    loadTestCases();
  }, [dataset]);

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading test cases...</p>
        </div>
      </div>
    );
  }

  if (error || tests.length === 0) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8">
          <p className="text-red-600 dark:text-red-400">{error || 'No test cases found in dataset'}</p>
          <button onClick={onClose} className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
            Close
          </button>
        </div>
      </div>
    );
  }

  const currentTest = tests[currentIndex];
  const isConversational = currentTest.input?.scenario || currentTest.input?.turns;

  const updateCurrentTest = (field, value) => {
    const updatedTests = tests.map((test, index) => 
      index === currentIndex ? { ...test, [field]: value } : test
    );
    setTests(updatedTests);
  };

  const saveProgress = async () => {
    try {
      // Save the current progress to the experiment
      const resultsData = {
        test_results: tests.map(test => ({
          test_case_id: test.test_case_id,
          input: test.input,
          expected_output: test.expected_output,
          actual_output: test.actual_output,
          notes: test.notes,
          metadata: test.metadata,
          status: test.actual_output ? 'completed' : 'pending'
        }))
      };

      if (experimentId) {
        await api.put(`/experiments/${experimentId}/results`, resultsData);
      }
      
      return true;
    } catch (error) {
      console.error('Error saving progress:', error);
      return false;
    }
  };

  // Remove add and delete functions since we're working with fixed dataset test cases

  const goToPrevious = () => {
    setCurrentIndex(Math.max(0, currentIndex - 1));
  };

  const goToNext = () => {
    setCurrentIndex(Math.min(tests.length - 1, currentIndex + 1));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">Manual Test Entry</h2>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                Test Case {currentIndex + 1} of {tests.length}
              </p>
            </div>
            <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-300">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{Math.round(((currentIndex + 1) / tests.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentIndex + 1) / tests.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Test Case Form */}
          <div className="space-y-6">
            {/* Display Input (Read-only) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isConversational ? 'Conversation Scenario' : 'Test Input'}
              </label>
              <div className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 dark:text-gray-300 rounded-md">
                {isConversational ? (
                  <div className="space-y-2">
                    {currentTest.input.scenario && (
                      <div>
                        <span className="font-medium text-purple-600 dark:text-purple-400">Scenario:</span>
                        <p className="mt-1">{currentTest.input.scenario}</p>
                      </div>
                    )}
                    {currentTest.input.turns && (
                      <div>
                        <span className="font-medium text-purple-600 dark:text-purple-400">Conversation:</span>
                        <div className="mt-1 space-y-1">
                          {currentTest.input.turns.map((turn, idx) => (
                            <div key={idx} className="pl-2">
                              <span className="font-medium">{turn.role}:</span> {turn.content}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>{typeof currentTest.input === 'object' ? currentTest.input.question || JSON.stringify(currentTest.input) : currentTest.input}</div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Expected {isConversational ? 'Outcome' : 'Output'}
                </label>
                <div className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 dark:text-gray-300 rounded-md" style={{ minHeight: '96px' }}>
                  {currentTest.expected_output || <span className="text-gray-400">No expected output defined</span>}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Actual Output <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={currentTest.actual_output}
                  onChange={(e) => updateCurrentTest('actual_output', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="4"
                  placeholder="Paste the actual response from the agent..."
                  autoFocus
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes (Optional)
              </label>
              <textarea
                value={currentTest.notes}
                onChange={(e) => updateCurrentTest('notes', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="Any additional observations or notes..."
              />
            </div>
          </div>

          {/* Navigation and Actions */}
          <div className="mt-8 flex justify-between items-center">
            <div className="flex space-x-3">
              <button
                onClick={goToPrevious}
                disabled={currentIndex === 0}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                Previous
              </button>
              
              <button
                onClick={goToNext}
                disabled={currentIndex === tests.length - 1}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <ChevronRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {tests.filter(t => t.actual_output).length} of {tests.length} completed
              </span>
              <button
                onClick={saveProgress}
                className="px-4 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-md hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30"
              >
                💾 Save Progress
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 mr-3"
          >
            Cancel
          </button>
          <button
            onClick={async () => {
              const saved = await saveProgress();
              if (saved) {
                onSave(tests);
              }
            }}
            className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-md"
          >
            Save & Close ({tests.filter(t => t.actual_output).length}/{tests.length} completed)
          </button>
        </div>
      </div>
    </div>
  );
}

// Import Preview Modal Component
function ImportPreviewModal({ data, experimentName: initialName, description: initialDescription, datasetId: initialDatasetId, onClose, onConfirmImport }) {
  const [experimentName, setExperimentName] = useState(initialName || '');
  const [description, setDescription] = useState(initialDescription || '');
  const [selectedDatasetId, setSelectedDatasetId] = useState(initialDatasetId || '');
  const [datasets, setDatasets] = useState([]);
  const [loadingDatasets, setLoadingDatasets] = useState(true);
  
  // Load available datasets
  useEffect(() => {
    const loadDatasets = async () => {
      try {
        const response = await api.get('/datasets');
        setDatasets(response.data.datasets || []);
      } catch (error) {
        console.error('Error loading datasets:', error);
      } finally {
        setLoadingDatasets(false);
      }
    };
    loadDatasets();
  }, []);

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceLabel = (confidence) => {
    if (confidence >= 0.8) return 'High Confidence';
    if (confidence >= 0.5) return 'Medium Confidence';
    return 'Low Confidence';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-screen overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Import Preview</h2>
              <p className="mt-1 text-sm text-gray-600">
                Review how your data will be mapped before importing
              </p>
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* File Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">File Information</h3>
            <div className="text-sm text-gray-600 grid grid-cols-2 md:grid-cols-4 gap-4">
              <div><span className="font-medium">Name:</span> {data.file_info.name}</div>
              <div><span className="font-medium">Size:</span> {Math.round(data.file_info.size / 1024)}KB</div>
              <div><span className="font-medium">Type:</span> {data.file_info.type}</div>
              <div><span className="font-medium">Rows:</span> {data.file_info.total_rows}</div>
            </div>
          </div>

          {/* Mapping Confidence */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Mapping Analysis</h3>
            <div className="flex items-center mb-2">
              <span className="text-sm text-gray-600 mr-2">Confidence:</span>
              <span className={`text-sm font-medium ${getConfidenceColor(data.mapping_analysis.confidence)}`}>
                {getConfidenceLabel(data.mapping_analysis.confidence)} ({Math.round(data.mapping_analysis.confidence * 100)}%)
              </span>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
              <div>Prompts: {data.mapping_analysis.stats.prompts_found}</div>
              <div>Expected: {data.mapping_analysis.stats.expected_found}</div>
              <div>Actual: {data.mapping_analysis.stats.actual_found}</div>
            </div>

            {data.mapping_analysis.issues.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-1">Issues:</h4>
                <ul className="text-sm text-yellow-700 list-disc list-inside">
                  {data.mapping_analysis.issues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Column Mapping */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Column Mapping Detection</h3>
            <div className="bg-gray-50 p-3 rounded-lg space-y-2">
              <div className="text-sm">
                <span className="font-medium text-gray-700">Detected columns:</span>
                <span className="ml-2 text-gray-600">{data.original_columns.join(', ')}</span>
              </div>
              <div className="text-xs text-gray-500">
                Our intelligent parser automatically mapped these to: Prompt, Expected Output, Actual Output, and Notes
              </div>
            </div>
          </div>

          {/* Preview Table */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900">Data Preview</h3>
              <div className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                Showing first 5 of {data.file_info.total_rows} rows
              </div>
            </div>
            <div className="mb-2 text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-md">
              📋 This is just a preview to verify the mapping looks correct. All {data.file_info.total_rows} rows will be imported.
            </div>
            <div className="overflow-x-auto border rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Prompt</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Expected</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actual</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Notes</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {data.preview_rows.map((row, index) => (
                    <tr key={index} className="bg-white">
                      <td className="px-3 py-2 text-sm text-gray-900 max-w-xs truncate">
                        {row.mapped.prompt || <span className="text-gray-400">Empty</span>}
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-900 max-w-xs truncate">
                        {row.mapped.expected_output || <span className="text-gray-400">Empty</span>}
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-900 max-w-xs truncate">
                        {row.mapped.actual_output || <span className="text-gray-400">Empty</span>}
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-900">
                      </td>
                      <td className="px-3 py-2 text-sm text-gray-900 max-w-xs truncate">
                        {row.mapped.notes || <span className="text-gray-400">Empty</span>}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Experiment Details */}
          <div className="border-t pt-6">
            <h3 className="font-medium text-gray-900 mb-3">Experiment Details</h3>
            
            {/* If name wasn't provided upfront (manual mode), ask for it now */}
            {!initialName ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Experiment Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={experimentName}
                    onChange={(e) => setExperimentName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., GPT-4 Q&A Results"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Brief description of the test results"
                    rows="2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Link to Dataset (Optional)
                  </label>
                  {loadingDatasets ? (
                    <div className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-md">
                      <span className="text-gray-500 dark:text-gray-400">Loading datasets...</span>
                    </div>
                  ) : (
                    <select
                      value={selectedDatasetId}
                      onChange={(e) => setSelectedDatasetId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white rounded-md"
                    >
                      <option value="">No dataset - Basic evaluators only</option>
                      {datasets.map(dataset => (
                        <option key={dataset.id} value={dataset.id}>
                          {dataset.name} ({dataset.record_count} test cases)
                        </option>
                      ))}
                    </select>
                  )}
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <span className="block"><strong>With dataset:</strong> All evaluators (accuracy, similarity, hallucination)</span>
                    <span className="block"><strong>Without:</strong> Basic evaluators only (PII, toxicity)</span>
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
                  <span className="text-sm text-gray-900 dark:text-gray-100">{experimentName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Description:</span>
                  <span className="text-sm text-gray-900 dark:text-gray-100">{description || 'Not specified'}</span>
                </div>
              </div>
            )}
            
            <div className="mt-4 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Rows to import:</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">{data.file_info.total_rows}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between p-6 border-t border-gray-200">
          <div className="text-sm text-gray-500 space-y-1">
            <div>
              {data.can_proceed ? 
                '✅ Ready to import' : 
                '⚠️ Low confidence - please review mapping'
              }
            </div>
            <div className="text-xs">
              Preview shows {Math.min(5, data.file_info.total_rows)} rows • Full import: {data.file_info.total_rows} rows
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={() => onConfirmImport(experimentName, description, selectedDatasetId)}
              disabled={!experimentName}
              className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Import All {data.file_info.total_rows} Rows
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Experiments;
