import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, useParams } from 'react-router-dom';
import {
  ChartBarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentChartBarIcon,
  FunnelIcon,
  ArrowPathIcon,
  TrashIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { api } from '../utils/evaluationApi';
import Breadcrumb from './testing/Breadcrumb';
import EvaluationModal from './testing/EvaluationModal';

function EvaluationResults() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { id } = useParams();
  const [results, setResults] = useState([]);
  const [allResults, setAllResults] = useState([]); // Store unfiltered results
  const [loading, setLoading] = useState(true);
  const [selectedResult, setSelectedResult] = useState(null);
  const [showEvaluateModal, setShowEvaluateModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    metric: '',
    session: ''
  });
  const [skipFilterUpdate, setSkipFilterUpdate] = useState(false);

  useEffect(() => {
    // Always load on mount
    loadResults();
    
    // If we have an ID parameter, load that specific evaluation
    if (id) {
      loadSpecificEvaluation(id);
    }
  }, [id]);

  // Listen for evaluation completion events
  useEffect(() => {
    const handleEvaluationCompleted = (event) => {
      const { evaluationId, results } = event.detail;
      console.log('EvaluationResults - Received completion event:', evaluationId, results);
      
      // Transform the completed results and update the running evaluation
      const completedTimestamp = new Date(results.completed_at || new Date()).toLocaleString();
      const transformedResult = {
        id: evaluationId,
        session_name: `${results.experiment_name || 'Evaluation'} - ${completedTimestamp}`,
        session_id: evaluationId,
        vendor: results.agent_name || 'Unknown Agent',
        dataset: results.dataset_name || 'Standalone Evaluation',
        evaluations: results.evaluations?.map(evaluation => ({
          metric: evaluation.metric_name,
          score: evaluation.score,
          status: evaluation.status === 'completed' ? 'passed' : 
                 evaluation.status === 'failed' ? 'failed' : 'warning',
          threshold: 80,
          unit: evaluation.score > 1000 ? 'ms' : evaluation.score > 100 ? 'tokens' : '%'
        })) || [],
        overall_score: results.evaluations?.length > 0 ? 
          Math.round(results.evaluations.reduce((acc, e) => acc + (e.score || 0), 0) / results.evaluations.length) : null,
        status: 'passed',
        evaluated_at: results.completed_at || new Date().toISOString(),
        evidence_count: 0
      };
      
      // Update BOTH results and allResults at the same time to prevent re-filtering
      console.log('EvaluationResults - Updating running evaluation to completed:', evaluationId);
      
      // Clean up localStorage for this evaluation
      localStorage.removeItem(`evaluation_${evaluationId}_running`);
      localStorage.removeItem(`evaluation_${evaluationId}`);
      localStorage.removeItem(`evaluation_${evaluationId}_completed`);
      
      // Temporarily skip filter updates to prevent race condition
      setSkipFilterUpdate(true);
      
      // Update allResults first
      setAllResults(prev => {
        const updated = prev.map(result => 
          result.id === evaluationId ? transformedResult : result
        );
        // If the evaluation wasn't in allResults, add it
        if (!updated.some(r => r.id === evaluationId)) {
          return [transformedResult, ...updated];
        }
        return updated;
      });
      
      // Update results
      setResults(prev => {
        console.log('EvaluationResults - Before completion update:', prev.length, prev.map(r => r.id));
        const updated = prev.map(result => 
          result.id === evaluationId ? transformedResult : result
        );
        console.log('EvaluationResults - After completion update:', updated.length, updated.map(r => r.id));
        return updated;
      });
      
      // Re-enable filter updates after a brief delay
      setTimeout(() => {
        setSkipFilterUpdate(false);
      }, 100);
      
      // Don't reload results immediately - let the user see the completed state
      // The database sync can happen later without affecting the UI
    };

    const handleEvaluationFailed = (event) => {
      const { evaluationId, error } = event.detail;
      console.log('EvaluationResults - Received failure event:', evaluationId, error);
      
      // Temporarily skip filter updates
      setSkipFilterUpdate(true);
      
      // Update the running evaluation to failed
      setAllResults(prev => prev.map(result => 
        result.id === evaluationId ? { ...result, status: 'failed', vendor: `Error: ${error}` } : result
      ));
      setResults(prev => prev.map(result => 
        result.id === evaluationId ? { ...result, status: 'failed', vendor: `Error: ${error}` } : result
      ));
      
      // Re-enable filter updates after a brief delay
      setTimeout(() => {
        setSkipFilterUpdate(false);
      }, 100);
    };

    // Add event listeners
    window.addEventListener('evaluationCompleted', handleEvaluationCompleted);
    window.addEventListener('evaluationFailed', handleEvaluationFailed);

    // Cleanup
    return () => {
      window.removeEventListener('evaluationCompleted', handleEvaluationCompleted);
      window.removeEventListener('evaluationFailed', handleEvaluationFailed);
    };
  }, []);

  // Check for URL parameters and poll for evaluation if needed  
  useEffect(() => {
    const evaluationId = searchParams.get('evaluation');
    const status = searchParams.get('status');
    
    console.log('EvaluationResults - URL params check:', { evaluationId, status });
    
    if (evaluationId && evaluationId !== 'undefined' && status === 'running') {
      // Add a running evaluation to the results immediately
      const runningEvaluation = {
        id: evaluationId,
        session_name: `Evaluation ${evaluationId.substring(0, 8)}`,
        session_id: evaluationId,
        vendor: 'Running...',
        dataset: 'Running...',
        evaluations: [],
        overall_score: null,
        status: 'running',
        evaluated_at: new Date().toISOString(),
        evidence_count: 0
      };
      
      // Add to results immediately so user sees it
      setResults(prev => {
        // Check if already exists
        if (!prev.some(r => r.id === evaluationId)) {
          return [runningEvaluation, ...prev];
        }
        return prev;
      });
      setAllResults(prev => {
        if (!prev.some(r => r.id === evaluationId)) {
          return [runningEvaluation, ...prev];
        }
        return prev;
      });
      
      // Start polling for completion
      console.log('Starting to poll for evaluation:', evaluationId);
      pollForEvaluationCompletion(evaluationId);
      
      // Delay the refresh to avoid overwriting the running state
      setTimeout(() => {
        loadResults();
      }, 1000);
    } else if (evaluationId && evaluationId !== 'undefined' && status === 'completed') {
      // For completed evaluations, check if we have stored results or fetch the experiment
      console.log('Loading completed evaluation:', evaluationId);
      
      // Check if we have stored results from the standalone evaluation
      const storedResults = localStorage.getItem(`evaluation_${evaluationId}`);
      console.log('EvaluationResults - Checking localStorage for key:', `evaluation_${evaluationId}`);
      console.log('EvaluationResults - Stored results found:', !!storedResults);
      console.log('EvaluationResults - All localStorage keys:', Object.keys(localStorage));
      
      if (storedResults) {
        try {
          const evaluationData = JSON.parse(storedResults);
          console.log('Found stored evaluation results:', evaluationData);
          
          // Transform stored evaluation data to match our UI expectations
          const storedTimestamp = new Date(evaluationData.completed_at || new Date()).toLocaleString();
          const transformedExperiment = {
            id: evaluationId,
            session_name: `${evaluationData.experiment_name || 'Evaluation'} - ${storedTimestamp}`,
            session_id: evaluationId,
            vendor: evaluationData.agent_name || 'Unknown Agent',
            dataset: evaluationData.dataset_name || 'Standalone Evaluation',
            evaluations: evaluationData.evaluations?.map(evaluation => ({
              metric: evaluation.metric_name,
              score: evaluation.score,
              status: evaluation.status === 'completed' ? 'passed' : 
                     evaluation.status === 'failed' ? 'failed' : 'warning',
              threshold: 80,
              unit: evaluation.score > 1000 ? 'ms' : evaluation.score > 100 ? 'tokens' : '%'
            })) || [],
            overall_score: evaluationData.evaluations?.length > 0 ? 
              Math.round(evaluationData.evaluations.reduce((acc, e) => acc + (e.score || 0), 0) / evaluationData.evaluations.length) : null,
            status: 'passed', // Assume completed evaluations are successful
            evaluated_at: evaluationData.completed_at || new Date().toISOString(),
            evidence_count: 0
          };
          
          console.log('Transformed stored evaluation:', transformedExperiment);
          
          // Add to the beginning of results
          setResults(prev => {
            const newResults = [transformedExperiment, ...prev.filter(r => r.id !== evaluationId)];
            console.log('EvaluationResults - Updated results after adding evaluation:', newResults);
            return newResults;
          });
          setAllResults(prev => [transformedExperiment, ...prev.filter(r => r.id !== evaluationId)]);
          
          // Clean up stored results after use
          localStorage.removeItem(`evaluation_${evaluationId}`);
          
        } catch (error) {
          console.error('Error parsing stored evaluation results:', error);
        }
      } else {
        // Try to fetch just this specific experiment instead of reloading all
        console.log('No stored results found, fetching specific experiment');
        api.get(`/experiments/${evaluationId}`)
          .then(response => {
            const experiment = response.data;
            
            if (experiment) {
              const completedResult = {
                id: evaluationId,
                session_name: experiment.name,
                session_id: evaluationId,
                vendor: experiment.agent_config?.name || 'Unknown Agent',
                dataset: experiment.dataset_name || 'No Dataset',
                evaluations: experiment.evaluations?.map(e => ({
                  metric: e.metric_name,
                  score: e.score,
                  status: e.status === 'completed' ? 'passed' : 
                         e.status === 'failed' ? 'failed' : 'warning',
                  threshold: 80,
                  unit: e.score > 1000 ? 'ms' : e.score > 100 ? 'tokens' : '%'
                })) || [],
                overall_score: experiment.evaluations?.length > 0 ? 
                  Math.round(experiment.evaluations.reduce((acc, e) => acc + (e.score || 0), 0) / experiment.evaluations.length) : null,
                status: 'passed',
                evaluated_at: experiment.completed_at || new Date().toISOString(),
                evidence_count: 0
              };
              
              // Add to results instead of replacing everything
              setResults(prev => {
                const exists = prev.some(r => r.id === evaluationId);
                if (exists) {
                  return prev.map(r => r.id === evaluationId ? completedResult : r);
                } else {
                  return [completedResult, ...prev];
                }
              });
              setAllResults(prev => {
                const exists = prev.some(r => r.id === evaluationId);
                if (exists) {
                  return prev.map(r => r.id === evaluationId ? completedResult : r);
                } else {
                  return [completedResult, ...prev];
                }
              });
            }
          })
          .catch(error => {
            console.error('Error fetching specific experiment:', error);
          });
      }
      
      // Keep params; harmless and avoids early removal during refresh
    }
  }, [searchParams, setSearchParams]);

  const pollForEvaluationCompletion = async (evaluationId) => {
    const maxAttempts = 150; // Poll for up to 5 minutes (150 * 2s = 300s)
    let attempts = 0;
    
    const poll = async () => {
      try {
        console.log(`Polling evaluation ${evaluationId}, attempt ${attempts + 1}`);
        // Fetch the evaluation status from the evaluations endpoint
        const response = await api.get(`/experiments/evaluations/${evaluationId}`);
        const evaluation = response.data;
        console.log('Evaluation status:', evaluation.status, 'Has evaluations:', evaluation.evaluations?.length);
        
        if (evaluation.status === 'completed' && evaluation.evaluations && evaluation.evaluations.length > 0) {
          // Evaluation completed, update the specific result instead of reloading everything
          const completedResult = {
            id: evaluationId,
            session_name: evaluation.experiment_name || `Evaluation ${evaluationId.substring(0, 8)}`,
            session_id: evaluationId,
            vendor: evaluation.agent_name || 'Unknown Agent',
            dataset: evaluation.dataset_name || 'No Dataset',
            evaluations: evaluation.evaluations.map(e => ({
              metric: e.metric_name || e.evaluator_id,
              score: e.score || 0,
              status: e.status === 'completed' ? 'passed' : 
                     e.status === 'failed' ? 'failed' : 'warning',
              threshold: 80,
              unit: e.score > 1000 ? 'ms' : e.score > 100 ? 'tokens' : '%'
            })),
            overall_score: evaluation.evaluations.length > 0 ? 
              Math.round(evaluation.evaluations.reduce((acc, e) => acc + (e.score || 0), 0) / evaluation.evaluations.length) : null,
            status: 'passed',
            evaluated_at: evaluation.completed_at || new Date().toISOString(),
            evidence_count: 0
          };
          
          // Update both results and allResults
          setResults(prev => prev.map(r => 
            r.id === evaluationId ? completedResult : r
          ));
          setAllResults(prev => prev.map(r => 
            r.id === evaluationId ? completedResult : r
          ));
        } else if (evaluation.status === 'failed') {
          // Update the evaluation status to failed
          setResults(prev => prev.map(r => 
            r.id === evaluationId ? { ...r, status: 'failed' } : r
          ));
          setAllResults(prev => prev.map(r => 
            r.id === evaluationId ? { ...r, status: 'failed' } : r
          ));
        } else if (attempts < maxAttempts) {
          // Still running, continue polling
          attempts++;
          setTimeout(poll, 2000); // Poll every 2 seconds
        } else {
          // Timeout - mark as unknown status
          setResults(prev => prev.map(r => 
            r.id === evaluationId ? { ...r, status: 'timeout' } : r
          ));
        }
      } catch (error) {
        console.error('Error polling evaluation:', error);
        
        // If evaluation doesn't exist (404), it might still be initializing
        if (error.response?.status === 404) {
          console.log(`Evaluation ${evaluationId} not found yet, will retry`);
          // Don't mark as failed immediately, give it more attempts
          if (attempts >= 5) {
            // After 5 attempts (10 seconds), mark as failed
            setResults(prev => prev.map(r => 
              r.id === evaluationId ? { ...r, status: 'failed', evaluations: [] } : r
            ));
            setAllResults(prev => prev.map(r => 
              r.id === evaluationId ? { ...r, status: 'failed', evaluations: [] } : r
            ));
            // Clean up localStorage
            localStorage.removeItem(`evaluation_${evaluationId}_running`);
            return; // Stop polling
          }
        }
        
        if (attempts < maxAttempts) {
          attempts++;
          setTimeout(poll, 2000);
        } else {
          // Max attempts reached, mark as timeout
          setResults(prev => prev.map(r => 
            r.id === evaluationId ? { ...r, status: 'timeout' } : r
          ));
          setAllResults(prev => prev.map(r => 
            r.id === evaluationId ? { ...r, status: 'timeout' } : r
          ));
        }
      }
    };
    
    poll();
  };

  useEffect(() => {
    // Apply filters when filters change
    if (allResults.length > 0 && !skipFilterUpdate) {
      console.log('EvaluationResults - Applying filters due to filters/allResults change');
      const filteredResults = applyFilters(allResults);
      setResults(filteredResults);
    }
  }, [filters, allResults, skipFilterUpdate]);

  const loadResultsWithoutLoading = async () => {
    try {
      console.log('EvaluationResults - loadResultsWithoutLoading() called');
      
      // Fetch experiments that have completed evaluation
      const [experimentsResponse, evaluationsResponse] = await Promise.all([
        api.get('/experiments'),
        api.get('/experiments/all-evaluations')
      ]);
      
      const allExperiments = experimentsResponse.data.experiments || [];
      const allEvaluations = evaluationsResponse.data.evaluations || [];
      
      console.log('All experiments from API:', allExperiments.length, allExperiments);
      
      // Filter for experiments that have completed evaluations OR are currently running evaluations
      const evaluatedExperiments = allExperiments.filter(exp => 
        (exp.status === 'completed' && exp.evaluations && exp.evaluations.length > 0) || // Already evaluated experiments
        (exp.status === 'running' || exp.status === 'in_progress') // Running evaluation experiments  
      );
      
      console.log('Filtered evaluated experiments:', evaluatedExperiments.length, evaluatedExperiments);
      
      // Transform the data to match our UI expectations
      const transformedResults = evaluatedExperiments.map(experiment => {
        const evaluations = experiment.evaluations || [];
        
        // For running experiments, set appropriate status and scores
        if (experiment.status === 'running' || experiment.status === 'in_progress') {
          return {
            id: experiment.id,
            session_name: experiment.name,
            session_id: experiment.id,
            vendor: experiment.agent_config?.name || 'Unknown Agent',
            dataset: experiment.dataset_name || 'No Dataset',
            evaluations: [], // Running experiments don't have completed evaluations yet
            overall_score: null,
            status: 'running',
            evaluated_at: experiment.created_at,
            evidence_count: experiment.test_results_count || 0
          };
        }
        
        
        // Calculate overall score from individual metric scores (for completed experiments)
        const validScores = evaluations.filter(e => e.score !== null && e.score !== undefined);
        const overallScore = validScores.length > 0 
          ? Math.round(validScores.reduce((acc, e) => acc + e.score, 0) / validScores.length)
          : null;
        
        // Determine overall status based on evaluation statuses
        const hasFailedEvals = evaluations.some(e => e.status === 'failed');
        const hasWarningEvals = evaluations.some(e => e.status === 'warning' || e.status === 'partial');
        const overallStatus = hasFailedEvals ? 'failed' : 
                             hasWarningEvals ? 'partial' : 'passed';
        
        return {
          id: experiment.id,
          session_name: experiment.name,
          session_id: experiment.id,
          vendor: experiment.agent_config?.name || 'Unknown Agent',
          dataset: experiment.dataset_name || 'No Dataset',
          evaluations: evaluations.map(evaluation => ({
            metric: evaluation.metric_name,
            score: evaluation.score,
            status: evaluation.status === 'completed' ? 'passed' : 
                   evaluation.status === 'failed' ? 'failed' : 'warning',
            threshold: 80, // Default threshold - could be made configurable
            unit: evaluation.score > 1000 ? 'ms' : evaluation.score > 100 ? 'tokens' : '%'
          })),
          overall_score: overallScore,
          status: overallStatus,
          evaluated_at: experiment.completed_at || experiment.created_at,
          evidence_count: experiment.test_results_count || 0
        };
      });

      // Transform standalone evaluations from database
      const transformedStandalone = allEvaluations.map(standaloneEval => {
        // Handle both string and object formats for evaluations and summary
        const evaluations = typeof standaloneEval.evaluations === 'string' 
          ? JSON.parse(standaloneEval.evaluations || '[]')
          : standaloneEval.evaluations || [];
        const summary = typeof standaloneEval.summary === 'string'
          ? JSON.parse(standaloneEval.summary || '{}')
          : standaloneEval.summary || {};
        
        const normalizedStatus = (standaloneEval.status || '').toLowerCase();
        const mappedStatus = normalizedStatus === 'running' ? 'running' :
                             normalizedStatus === 'failed' ? 'failed' :
                             normalizedStatus === 'partial' ? 'partial' : 
                             normalizedStatus === 'completed' ? 'passed' : 'running';
        return {
          id: standaloneEval.evaluation_id,
          session_name: standaloneEval.status === 'running' 
            ? `${standaloneEval.experiment_name} - Running...`
            : `${standaloneEval.experiment_name} - ${standaloneEval.completed_at ? new Date(standaloneEval.completed_at).toLocaleString() : 'In Progress'}`,
          session_id: standaloneEval.evaluation_id,
          vendor: standaloneEval.agent_name || 'Unknown Agent',
          dataset: standaloneEval.dataset_name || 'Standalone Evaluation',
          evaluations: evaluations.map(evalResult => ({
            metric: evalResult.metric_name,
            score: evalResult.score,
            status: evalResult.status === 'completed' ? 'passed' : 
                   evalResult.status === 'failed' ? 'failed' : 'warning',
            threshold: 80,
            unit: evalResult.score > 1000 ? 'ms' : evalResult.score > 100 ? 'tokens' : '%'
          })),
          overall_score: summary.average_score || null,
          status: mappedStatus,
          evaluated_at: standaloneEval.completed_at || standaloneEval.created_at,
          evidence_count: 0
        };
      });

      // Combine experiment evaluations and standalone evaluations
      const allTransformedResults = [...transformedResults, ...transformedStandalone];
      // Preserve any manually added results (like running standalone items) in allResults too
      const manuallyAddedResultsFromCurrent = results.filter(prevResult => 
        !allTransformedResults.some(apiResult => apiResult.id === prevResult.id)
      );

      // Temporarily skip filter updates to avoid clobbering results with an intermediate state
      setSkipFilterUpdate(true);

      // Store all results (unfiltered), including manually added ones
      setAllResults([...manuallyAddedResultsFromCurrent, ...allTransformedResults]);
      
      // Apply filters to combined results
      const filteredResults = applyFilters([...manuallyAddedResultsFromCurrent, ...allTransformedResults]);
      
      // Preserve any manually added evaluations that might not be in the API response yet
      setResults(prev => {
        console.log('EvaluationResults - loadResultsWithoutLoading preservation logic');
        console.log('EvaluationResults - Previous results:', prev.length, prev.map(r => ({ id: r.id, status: r.status })));
        console.log('EvaluationResults - Combined API results:', allTransformedResults.length, allTransformedResults.map(r => ({ id: r.id, status: r.status })));
        
        // Keep any results that were manually added (like from localStorage) that aren't in the API response
        // BUT if the API has the same ID, use the API version UNLESS the prev version is 'running' and API version is not
        const finalResults = filteredResults.map(apiResult => {
          const prevResult = prev.find(p => p.id === apiResult.id);
          // If we had it as running before, and API says it's not running yet, keep it as running
          if (prevResult && prevResult.status === 'running' && apiResult.status !== 'running' && !apiResult.evaluations?.length) {
            console.log(`Preserving running status for ${apiResult.id}`);
            return prevResult;
          }
          return apiResult;
        });
        
        // Add any results from prev that aren't in the API results
        const missingFromApi = prev.filter(prevResult => 
          !filteredResults.some(apiResult => apiResult.id === prevResult.id)
        );
        
        const combined = [...missingFromApi, ...finalResults];
        console.log('EvaluationResults - Final results after preservation:', combined.length, combined.map(r => ({ id: r.id, status: r.status })));
        return combined;
      });

      // Re-enable filter updates shortly after state settles
      setTimeout(() => setSkipFilterUpdate(false), 50);
    } catch (error) {
      console.error('Error loading evaluation results without loading state:', error);
      // Don't set empty results on error for this version
    }
  };

  const loadSpecificEvaluation = async (evaluationId) => {
    try {
      console.log('Loading specific evaluation:', evaluationId);
      setLoading(true);
      
      // Try to fetch the specific evaluation
      const response = await api.get(`/experiments/${evaluationId}`);
      const evaluation = response.data;
      
      if (evaluation) {
        setSelectedResult(evaluation);
        // Optionally, still load all results in the background
        loadResults();
      }
    } catch (error) {
      console.error('Error loading specific evaluation:', error);
      // Fall back to loading all results
      loadResults();
    }
  };

  const loadResults = async () => {
    try {
      console.log('EvaluationResults - loadResults() called');
      setLoading(true);
      
      // Fetch experiments that have completed evaluation
      const [experimentsResponse, evaluationsResponse] = await Promise.all([
        api.get('/experiments'),
        api.get('/experiments/all-evaluations')
      ]);
      
      const allExperiments = experimentsResponse.data.experiments || [];
      const allEvaluations = evaluationsResponse.data.evaluations || [];
      
      console.log('All experiments from API:', allExperiments.length, allExperiments);
      
      // Filter for experiments that have completed evaluations OR are currently running evaluations
      const evaluatedExperiments = allExperiments.filter(exp => 
        (exp.status === 'completed' && exp.evaluations && exp.evaluations.length > 0) || // Already evaluated experiments
        (exp.status === 'running' || exp.status === 'in_progress') // Running evaluation experiments  
      );
      
      console.log('Filtered evaluated experiments:', evaluatedExperiments.length, evaluatedExperiments);
      
      // Transform the data to match our UI expectations
      const transformedResults = evaluatedExperiments.map(experiment => {
        const evaluations = experiment.evaluations || [];
        
        // For running experiments, set appropriate status and scores
        if (experiment.status === 'running' || experiment.status === 'in_progress') {
          return {
            id: experiment.id,
            session_name: experiment.name,
            session_id: experiment.id,
            vendor: experiment.agent_config?.name || 'Unknown Agent',
            dataset: experiment.dataset_name || 'No Dataset',
            evaluations: [], // Running experiments don't have completed evaluations yet
            overall_score: null,
            status: 'running',
            evaluated_at: experiment.created_at,
            evidence_count: experiment.test_results_count || 0
          };
        }
        
        
        // Calculate overall score from individual metric scores (for completed experiments)
        const validScores = evaluations.filter(e => e.score !== null && e.score !== undefined);
        const overallScore = validScores.length > 0 
          ? Math.round(validScores.reduce((acc, e) => acc + e.score, 0) / validScores.length)
          : null;
        
        // Determine overall status based on evaluation statuses
        const hasFailedEvals = evaluations.some(e => e.status === 'failed');
        const hasWarningEvals = evaluations.some(e => e.status === 'warning' || e.status === 'partial');
        const overallStatus = hasFailedEvals ? 'failed' : 
                             hasWarningEvals ? 'partial' : 'passed';
        
        return {
          id: experiment.id,
          session_name: experiment.name,
          session_id: experiment.id,
          vendor: experiment.agent_config?.name || 'Unknown Agent',
          dataset: experiment.dataset_name || 'No Dataset',
          evaluations: evaluations.map(evaluation => ({
            metric: evaluation.metric_name,
            score: evaluation.score,
            status: evaluation.status === 'completed' ? 'passed' : 
                   evaluation.status === 'failed' ? 'failed' : 'warning',
            threshold: 80, // Default threshold - could be made configurable
            unit: evaluation.score > 1000 ? 'ms' : evaluation.score > 100 ? 'tokens' : '%'
          })),
          overall_score: overallScore,
          status: overallStatus,
          evaluated_at: experiment.completed_at || experiment.created_at,
          evidence_count: experiment.test_results_count || 0
        };
      });

      // Transform standalone evaluations from database
      const transformedStandalone = allEvaluations.map(standaloneEval => {
        // Handle both string and object formats for evaluations and summary
        const evaluations = typeof standaloneEval.evaluations === 'string' 
          ? JSON.parse(standaloneEval.evaluations || '[]')
          : standaloneEval.evaluations || [];
        const summary = typeof standaloneEval.summary === 'string'
          ? JSON.parse(standaloneEval.summary || '{}')
          : standaloneEval.summary || {};
        
        // Determine status based on the evaluation's actual status
        const normalizedStatus = (standaloneEval.status || '').toLowerCase();
        const mappedStatus = normalizedStatus === 'running' ? 'running' :
                           normalizedStatus === 'failed' ? 'failed' :
                           normalizedStatus === 'partial' ? 'partial' : 
                           normalizedStatus === 'completed' ? 'passed' : 'passed';
        
        return {
          id: standaloneEval.evaluation_id,
          session_name: standaloneEval.status === 'running' 
            ? `${standaloneEval.experiment_name} - Running...`
            : `${standaloneEval.experiment_name} - ${standaloneEval.completed_at ? new Date(standaloneEval.completed_at).toLocaleString() : 'In Progress'}`,
          session_id: standaloneEval.evaluation_id,
          vendor: standaloneEval.agent_name || 'Unknown Agent',
          dataset: standaloneEval.dataset_name || 'Standalone Evaluation',
          evaluations: evaluations.map(evalResult => ({
            metric: evalResult.metric_name,
            score: evalResult.score,
            status: evalResult.status === 'completed' ? 'passed' : 
                   evalResult.status === 'failed' ? 'failed' : 'warning',
            threshold: 80,
            unit: evalResult.score > 1000 ? 'ms' : evalResult.score > 100 ? 'tokens' : '%'
          })),
          overall_score: summary.average_score || null,
          status: mappedStatus,
          evaluated_at: standaloneEval.completed_at || standaloneEval.created_at,
          evidence_count: 0
        };
      });

      // Combine experiment evaluations and standalone evaluations
      const allTransformedResults = [...transformedResults, ...transformedStandalone];
      // Preserve any manually added results (like running standalone items) in allResults too
      const manuallyAddedResultsFromCurrent = results.filter(prevResult => 
        !allTransformedResults.some(apiResult => apiResult.id === prevResult.id)
      );

      // Temporarily skip filter updates to avoid clobbering results with an intermediate state
      setSkipFilterUpdate(true);

      // Store all results (unfiltered), including manually added ones
      setAllResults([...manuallyAddedResultsFromCurrent, ...allTransformedResults]);
      
      // Apply filters to combined results
      const filteredResults = applyFilters([...manuallyAddedResultsFromCurrent, ...allTransformedResults]);
      
      // Preserve any manually added evaluations that might not be in the API response yet
      setResults(prev => {
        console.log('EvaluationResults - loadResults preservation logic');
        console.log('EvaluationResults - Previous results:', prev.length, prev.map(r => ({ id: r.id, status: r.status })));
        console.log('EvaluationResults - Combined API results:', allTransformedResults.length, allTransformedResults.map(r => ({ id: r.id, status: r.status })));
        
        // Keep any results that were manually added (like from localStorage) that aren't in the API response
        // BUT if the API has the same ID, use the API version UNLESS the prev version is 'running' and API version is not
        const finalResults = filteredResults.map(apiResult => {
          const prevResult = prev.find(p => p.id === apiResult.id);
          // If we had it as running before, and API says it's not running yet, keep it as running
          if (prevResult && prevResult.status === 'running' && apiResult.status !== 'running' && !apiResult.evaluations?.length) {
            console.log(`Preserving running status for ${apiResult.id}`);
            return prevResult;
          }
          return apiResult;
        });
        
        // Add any results from prev that aren't in the API results
        const missingFromApi = prev.filter(prevResult => 
          !filteredResults.some(apiResult => apiResult.id === prevResult.id)
        );
        
        const combined = [...missingFromApi, ...finalResults];
        console.log('EvaluationResults - Final results after preservation:', combined.length, combined.map(r => ({ id: r.id, status: r.status })));
        return combined;
      });
    } catch (error) {
      console.error('Error loading evaluation results:', error);
      // Set empty results on error
      setResults([]);
    } finally {
      // Re-enable filter updates shortly after state settles
      setTimeout(() => setSkipFilterUpdate(false), 50);
      setLoading(false);
    }
  };

  const handleDeleteEvaluation = async (evaluationId, evaluationName, event) => {
    // Stop event propagation to prevent triggering the card's onClick
    if (event) {
      event.stopPropagation();
    }
    
    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${evaluationName}"? This action cannot be undone.`
    );
    
    if (!confirmDelete) return;
    
    try {
      // Delete any evaluation - unified approach
      await api.delete(`/experiments/all-evaluations/${evaluationId}`);
      
      // Remove from local state
      setResults(prev => prev.filter(result => result.id !== evaluationId));
      setAllResults(prev => prev.filter(result => result.id !== evaluationId));
      
      console.log(`Deleted evaluation ${evaluationId}`);
    } catch (error) {
      console.error('Error deleting evaluation:', error);
      alert('Failed to delete evaluation. Please try again.');
    }
  };

  const handleViewDetails = async (result) => {
    console.log('HandleViewDetails - Navigating to evaluation details:', result.id);
    // Simply navigate to the dedicated evaluation details screen
    navigate(`/evaluation-results/${result.id}`);
  };

  const applyFilters = (resultsToFilter) => {
    return resultsToFilter.filter(result => {
      // Always include running evaluations - don't filter them out
      if (result.status === 'running') {
        return true;
      }
      
      // Session filter
      if (filters.session && result.session_id !== filters.session) {
        return false;
      }
      
      // Status filter
      if (filters.status && result.status !== filters.status) {
        return false;
      }
      
      // Metric filter - check if result has evaluation for this metric category
      if (filters.metric) {
        const hasMetric = result.evaluations.some(evaluation => {
          if (filters.metric === 'accuracy') return ['accuracy', 'precision', 'recall', 'f1-score'].some(m => evaluation.metric.toLowerCase().includes(m));
          if (filters.metric === 'safety') return ['pii', 'toxicity', 'safety', 'bias', 'moderation'].some(m => evaluation.metric.toLowerCase().includes(m));
          if (filters.metric === 'performance') return ['latency', 'response time', 'token', 'cost'].some(m => evaluation.metric.toLowerCase().includes(m));
          return false;
        });
        if (!hasMetric) return false;
      }
      
      return true;
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />;
      case 'warning':
      case 'partial':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />;
      case 'running':
        return <ArrowPathIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 animate-spin" />;
      case 'ready':
        return <DocumentChartBarIcon className="h-5 w-5 text-purple-600 dark:text-purple-400" />;
      default:
        return <ChartBarIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300';
      case 'warning':
      case 'partial':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300';
      case 'running':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300';
      case 'ready':
        return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getScoreColor = (score, threshold) => {
    if (score >= threshold) return 'text-green-600 dark:text-green-400';
    if (score >= threshold * 0.8) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Evaluation Results', href: '/evaluation-results' }
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <Breadcrumb items={breadcrumbItems} />
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Evaluation Results</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            View and analyze evaluation results from test sessions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowEvaluateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <DocumentChartBarIcon className="h-4 w-4 mr-2" />
            Import Results
          </button>
          <button
            onClick={() => {
              // Open evaluation modal to choose experiment and evaluator
              setShowEvaluateModal(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Run Evaluation
          </button>
        </div>
      </div>


      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 p-4">
        <div className="flex items-center space-x-4">
          <FunnelIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          <select
            value={filters.session}
            onChange={(e) => setFilters({ ...filters, session: e.target.value })}
            className="block px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Sessions</option>
            {[...new Set(allResults.map(r => r.session_id))].map(sessionId => {
              const session = allResults.find(r => r.session_id === sessionId);
              return (
                <option key={sessionId} value={sessionId}>
                  {session?.session_name || sessionId}
                </option>
              );
            })}
          </select>
          <select
            value={filters.metric}
            onChange={(e) => setFilters({ ...filters, metric: e.target.value })}
            className="block px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Metrics</option>
            <option value="accuracy">Accuracy Metrics</option>
            <option value="safety">Safety Metrics</option>
            <option value="performance">Performance Metrics</option>
          </select>
          <select
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            className="block px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 dark:text-white"
          >
            <option value="">All Status</option>
            <option value="passed">Passed</option>
            <option value="failed">Failed</option>
            <option value="partial">Partial Pass</option>
          </select>
        </div>
      </div>

      {/* Results List */}
      <div className="space-y-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-flex items-center">
              <ArrowPathIcon className="animate-spin h-5 w-5 mr-3 text-gray-400" />
              Loading evaluation results...
            </div>
          </div>
        ) : (
          results.map((result) => (
            <div 
              key={result.id} 
              className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
                result.status !== 'running' ? 'cursor-pointer' : 'cursor-default'
              }`}
              onClick={result.status !== 'running' ? () => handleViewDetails(result) : undefined}
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {getStatusIcon(result.status)}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {result.session_name}
                      </h3>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                        Vendor: {result.vendor} • Dataset: {result.dataset}
                      </p>
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        <span>Evidence: {result.evidence_count} items</span>
                        <span>Evaluated: {new Date(result.evaluated_at).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        {result.status === 'running' ? (
                          <span className="animate-pulse">Running...</span>
                        ) : (
                          `${result.overall_score || 0}%`
                        )}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Overall Score</div>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      result.status === 'running' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 animate-pulse' : getStatusColor(result.status)
                    }`}>
                      {result.status === 'running' ? 'Running' : result.status}
                    </span>
                  </div>
                </div>

                {/* Evaluation Metrics */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {result.status === 'running' && result.evaluations.length > 0 ? (
                    // Show skeleton loaders for running evaluations
                    result.evaluations.map((evaluation, idx) => (
                      <div key={idx} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 animate-pulse">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {evaluation.metric}
                          </span>
                          <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                        </div>
                        <div className="flex items-baseline">
                          <div className="h-8 w-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
                          <div className="ml-2 h-4 w-8 bg-gray-200 dark:bg-gray-600 rounded"></div>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5"></div>
                        </div>
                      </div>
                    ))
                  ) : (
                    result.evaluations.map((evaluation, idx) => (
                      <div key={idx} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {evaluation.metric}
                        </span>
                        {getStatusIcon(evaluation.status)}
                      </div>
                      <div className="flex items-baseline">
                        <span className={`text-2xl font-bold ${getScoreColor(evaluation.score, evaluation.threshold)}`}>
                          {evaluation.score}
                        </span>
                        {evaluation.unit && (
                          <span className="ml-1 text-sm text-gray-500 dark:text-gray-400">{evaluation.unit}</span>
                        )}
                        <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                          / {evaluation.threshold}{evaluation.unit ? ` ${evaluation.unit}` : '%'}
                        </span>
                      </div>
                      <div className="mt-1">
                        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full ${
                              evaluation.status === 'passed' ? 'bg-green-600' :
                              evaluation.status === 'warning' ? 'bg-yellow-600' : 'bg-red-600'
                            }`}
                            style={{ 
                              width: `${Math.min((evaluation.score / evaluation.threshold) * 100, 100)}%` 
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    ))
                  )}
                </div>

                {/* Actions */}
                <div className="mt-6 flex justify-end space-x-3">
                  {result.status !== 'running' ? (
                    <>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewDetails(result);
                        }}
                        className="inline-flex items-center text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                      >
                        View Details
                        <ChevronRightIcon className="h-4 w-4 ml-1" />
                      </button>
                      <button 
                        onClick={(e) => e.stopPropagation()}
                        className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                        Compare Results
                      </button>
                      <button 
                        onClick={(e) => e.stopPropagation()}
                        className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300">
                        Re-evaluate
                      </button>
                      <button 
                        onClick={(e) => e.stopPropagation()}
                        className="text-sm text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">
                        Export Report
                      </button>
                    </>
                  ) : (
                    <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                      Evaluation in progress...
                    </div>
                  )}
                  
                  {/* Delete/Cancel button - always visible */}
                  <button 
                    onClick={(e) => handleDeleteEvaluation(result.id, result.session_name, e)}
                    className="inline-flex items-center text-sm text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    {result.status === 'running' ? 'Cancel' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {results.length === 0 && !loading && (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No evaluation results</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Run evaluations on your experiments to see results here.
          </p>
          <div className="mt-6">
            <button
              onClick={() => {
                // Open evaluation modal to choose experiment and evaluator
                setShowEvaluateModal(true);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:blue-600"
            >
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Run First Evaluation
            </button>
          </div>
        </div>
      )}

      {/* Evaluation Modal */}
      {showEvaluateModal && (
        <EvaluationModal 
          session={selectedSession} // Will be null when opened from "Run Evaluation" button
          onClose={() => {
            setShowEvaluateModal(false);
            setSelectedSession(null);
          }}
          onEvaluationComplete={() => {
            // No need to loadResults() anymore - evaluations are handled via events
            setShowEvaluateModal(false);
            setSelectedSession(null);
          }}
        />
      )}

    </div>
  );
}

export default EvaluationResults;