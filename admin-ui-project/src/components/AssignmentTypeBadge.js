import React from 'react';

export default function AssignmentTypeBadge({ type }) {
  if (!type || type === '—') {
    return <span className="text-gray-400">—</span>;
  }
  const isDirect = type === 'Direct';
  const cls = isDirect
    ? 'bg-blue-700 text-white'
    : 'bg-purple-700 text-white';
  return (
    <span className={`inline-block text-xs px-2 py-0.5 rounded-full ${cls}`}>{type}</span>
  );
}
