import React, { useEffect, useRef } from 'react';
import { Editor } from '@monaco-editor/react';
import { getAllEnumFields } from '../utils/schemaUtils';

const JsonEditorWithAutocomplete = ({ 
  value, 
  onChange, 
  policyType, 
  height = "300px",
  options = {},
  theme = "vs-light"
}) => {
  const editorRef = useRef(null);
  const monacoRef = useRef(null);

  useEffect(() => {
    if (monacoRef.current && policyType) {
      const enumFields = getAllEnumFields(policyType);
      
      // Register completion provider
      const disposable = monacoRef.current.languages.registerCompletionItemProvider('json', {
        provideCompletionItems: (model, position) => {
          const suggestions = [];
          const lineContent = model.getLineContent(position.lineNumber);
          const word = model.getWordUntilPosition(position);
          const range = {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn,
          };

          // Add enum suggestions based on context
          Object.keys(enumFields).forEach(fieldPath => {
            const enumValues = enumFields[fieldPath];
            
            // Check if we're in the right context for this field
            const isInContext = lineContent.includes(`"${fieldPath}"`) || 
                               lineContent.includes(`"${fieldPath.split('.').pop()}"`);
            
            if (isInContext || !fieldPath.includes('.')) {
              enumValues.forEach(value => {
                suggestions.push({
                  label: `"${value}"`,
                  kind: monacoRef.current.languages.CompletionItemKind.Value,
                  insertText: `"${value}"`,
                  range: range,
                  detail: `Enum value for ${fieldPath}`,
                  documentation: `Available value for ${fieldPath} field`
                });
              });
            }
          });

          // Add common JSON structure suggestions
          suggestions.push(
            {
              label: '"type"',
              kind: monacoRef.current.languages.CompletionItemKind.Property,
              insertText: '"type"',
              range: range,
              detail: 'Policy type field',
              documentation: 'Required field specifying the policy type'
            },
            {
              label: '"severity"',
              kind: monacoRef.current.languages.CompletionItemKind.Property,
              insertText: '"severity"',
              range: range,
              detail: 'Policy severity field',
              documentation: 'Required field specifying the policy severity level'
            },
            {
              label: '"allowed_roles"',
              kind: monacoRef.current.languages.CompletionItemKind.Property,
              insertText: '"allowed_roles"',
              range: range,
              detail: 'Allowed roles array',
              documentation: 'Array of roles that have access to this data'
            },
            {
              label: '"protected_fields"',
              kind: monacoRef.current.languages.CompletionItemKind.Property,
              insertText: '"protected_fields"',
              range: range,
              detail: 'Protected fields array',
              documentation: 'Array of fields that require special protection'
            }
          );

          return { suggestions };
        }
      });

      return () => disposable.dispose();
    }
  }, [policyType]);

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
  };

  return (
    <Editor
      height={height}
      language="json"
      value={value}
      onChange={onChange}
      onMount={handleEditorDidMount}
      options={{
        minimap: { enabled: false },
        fontSize: 14,
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        automaticLayout: true,
        suggestOnTriggerCharacters: true,
        quickSuggestions: true,
        ...options
      }}
      theme={theme}
    />
  );
};

export default JsonEditorWithAutocomplete; 