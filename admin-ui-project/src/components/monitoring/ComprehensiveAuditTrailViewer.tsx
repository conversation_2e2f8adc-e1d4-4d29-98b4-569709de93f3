import React, { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  Calendar, 
  Download, 
  Eye, 
  Filter, 
  Info, 
  Lock, 
  RefreshCw, 
  Search, 
  Shield, 
  User, 
  Users, 
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface AuditEvent {
  id: string;
  timestamp: string;
  eventType: 'access' | 'modification' | 'creation' | 'deletion' | 'security' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId: string;
  username: string;
  userRole: string;
  resource: string;
  action: string;
  details: string;
  ipAddress: string;
  outcome: 'success' | 'failure' | 'pending';
  complianceStatus: 'compliant' | 'non-compliant' | 'pending';
}

interface AuditMetrics {
  totalEvents: number;
  securityEvents: number;
  complianceEvents: number;
  criticalEvents: number;
  failedEvents: number;
  uniqueUsers: number;
  complianceRate: number;
}

const ComprehensiveAuditTrailViewer: React.FC = () => {
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([]);
  const [metrics, setMetrics] = useState<AuditMetrics>({
    totalEvents: 0,
    securityEvents: 0,
    complianceEvents: 0,
    criticalEvents: 0,
    failedEvents: 0,
    uniqueUsers: 0,
    complianceRate: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AuditEvent | null>(null);

  // Generate realistic audit trail data
  const generateAuditEvents = (): AuditEvent[] => {
    const eventTypes: Array<'access' | 'modification' | 'creation' | 'deletion' | 'security' | 'compliance'> = [
      'access', 'modification', 'creation', 'deletion', 'security', 'compliance'
    ];
    const severities: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical'];
    const outcomes: Array<'success' | 'failure' | 'pending'> = ['success', 'failure', 'pending'];
    const complianceStatuses: Array<'compliant' | 'non-compliant' | 'pending'> = ['compliant', 'non-compliant', 'pending'];
    
    const resources = [
      'Agent Registry', 'Guardrails Library', 'Audit Trail', 'User Management',
      'Policy Configuration', 'Security Settings', 'Compliance Dashboard'
    ];
    
    const actions = [
      'viewed', 'created', 'updated', 'deleted', 'exported', 'imported',
      'configured', 'authenticated', 'authorized', 'validated'
    ];
    
    const users = [
      { id: 'user-1', name: '<EMAIL>', role: 'Administrator' },
      { id: 'user-2', name: '<EMAIL>', role: 'Security Analyst' },
      { id: 'user-3', name: '<EMAIL>', role: 'Compliance Officer' },
      { id: 'user-4', name: '<EMAIL>', role: 'Developer' },
      { id: 'user-5', name: '<EMAIL>', role: 'Operations Manager' }
    ];
    
    const events: AuditEvent[] = [];
    const now = new Date();
    
    for (let i = 0; i < 50; i++) {
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      const severity = severities[Math.floor(Math.random() * severities.length)];
      const outcome = outcomes[Math.floor(Math.random() * outcomes.length)];
      const complianceStatus = complianceStatuses[Math.floor(Math.random() * complianceStatuses.length)];
      const user = users[Math.floor(Math.random() * users.length)];
      const resource = resources[Math.floor(Math.random() * resources.length)];
      const action = actions[Math.floor(Math.random() * actions.length)];
      
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      
      events.push({
        id: `event-${i + 1}`,
        timestamp: timestamp.toISOString(),
        eventType,
        severity,
        userId: user.id,
        username: user.name,
        userRole: user.role,
        resource,
        action,
        details: `${user.name} ${action} ${resource} at ${timestamp.toLocaleString()}`,
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        outcome,
        complianceStatus
      });
    }
    
    return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const calculateMetrics = (events: AuditEvent[]): AuditMetrics => {
    const totalEvents = events.length;
    const securityEvents = events.filter(e => e.eventType === 'security').length;
    const complianceEvents = events.filter(e => e.eventType === 'compliance').length;
    const criticalEvents = events.filter(e => e.severity === 'critical').length;
    const failedEvents = events.filter(e => e.outcome === 'failure').length;
    const uniqueUsers = new Set(events.map(e => e.userId)).size;
    const complianceRate = events.filter(e => e.complianceStatus === 'compliant').length / events.length * 100;

    return {
      totalEvents,
      securityEvents,
      complianceEvents,
      criticalEvents,
      failedEvents,
      uniqueUsers,
      complianceRate: Math.round(complianceRate)
    };
  };

  useEffect(() => {
    const events = generateAuditEvents();
    const metricsData = calculateMetrics(events);
    
    setAuditEvents(events);
    setMetrics(metricsData);
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      const events = generateAuditEvents();
      const metricsData = calculateMetrics(events);
      
      setAuditEvents(events);
      setMetrics(metricsData);
      setIsLoading(false);
    }, 1000);
  };

  const filteredEvents = auditEvents.filter(event => {
    const matchesSearch = 
      event.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.action.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEventType = eventTypeFilter === 'all' || event.eventType === eventTypeFilter;
    const matchesSeverity = severityFilter === 'all' || event.severity === severityFilter;
    
    return matchesSearch && matchesEventType && matchesSeverity;
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failure': return <XCircle className="w-4 h-4 text-red-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      default: return <Info className="w-4 h-4 text-gray-600" />;
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'security': return <Shield className="w-4 h-4" />;
      case 'compliance': return <AlertTriangle className="w-4 h-4" />;
      case 'access': return <Eye className="w-4 h-4" />;
      case 'modification': return <Zap className="w-4 h-4" />;
      case 'creation': return <CheckCircle className="w-4 h-4" />;
      case 'deletion': return <XCircle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Comprehensive Audit Trail Viewer</h1>
          <p className="text-gray-600 dark:text-gray-400">Complete audit trail monitoring and analysis</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-white">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Events</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{metrics.totalEvents}</p>
            </div>
            <Users className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Security Events</p>
              <p className="text-2xl font-bold text-red-600">{metrics.securityEvents}</p>
            </div>
            <Shield className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Critical Events</p>
              <p className="text-2xl font-bold text-red-600">{metrics.criticalEvents}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance Rate</p>
              <p className="text-2xl font-bold text-green-600">{metrics.complianceRate}%</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={eventTypeFilter}
              onChange={(e) => setEventTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Event Types</option>
              <option value="access">Access</option>
              <option value="modification">Modification</option>
              <option value="creation">Creation</option>
              <option value="deletion">Deletion</option>
              <option value="security">Security</option>
              <option value="compliance">Compliance</option>
            </select>
            <select
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Severities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
        </div>
      </div>

      {/* Audit Events Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Audit Events ({filteredEvents.length})</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Event</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Resource</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Action</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Severity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Outcome</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Timestamp</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredEvents.slice(0, 20).map((event) => (
                <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getEventTypeIcon(event.eventType)}
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white capitalize">{event.eventType}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">#{event.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{event.username}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{event.userRole}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {event.resource}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white capitalize">
                    {event.action}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                      {event.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getOutcomeIcon(event.outcome)}
                      <span className="text-sm text-gray-900 dark:text-white capitalize">{event.outcome}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(event.timestamp).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setSelectedEvent(event)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1"
                    >
                      <Eye className="w-4 h-4" />
                      <span>View</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Event Details</h3>
              <button
                onClick={() => setSelectedEvent(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Event ID</p>
                  <p className="text-sm text-gray-900 dark:text-white">{selectedEvent.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Event Type</p>
                  <p className="text-sm text-gray-900 dark:text-white capitalize">{selectedEvent.eventType}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Severity</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(selectedEvent.severity)}`}>
                    {selectedEvent.severity}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Outcome</p>
                  <p className="text-sm text-gray-900 dark:text-white capitalize">{selectedEvent.outcome}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">User</p>
                  <p className="text-sm text-gray-900 dark:text-white">{selectedEvent.username}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Role</p>
                  <p className="text-sm text-gray-900 dark:text-white">{selectedEvent.userRole}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Resource</p>
                  <p className="text-sm text-gray-900 dark:text-white">{selectedEvent.resource}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Action</p>
                  <p className="text-sm text-gray-900 dark:text-white capitalize">{selectedEvent.action}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">IP Address</p>
                  <p className="text-sm text-gray-900 dark:text-white">{selectedEvent.ipAddress}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Timestamp</p>
                  <p className="text-sm text-gray-900 dark:text-white">{new Date(selectedEvent.timestamp).toLocaleString()}</p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Details</p>
                <p className="text-sm text-gray-900 dark:text-white mt-1">{selectedEvent.details}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ComprehensiveAuditTrailViewer; 