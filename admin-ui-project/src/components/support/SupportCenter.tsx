import React, { useState, useMemo } from 'react';
import { Search, MessageCircle, Phone, Mail, Clock, User, AlertTriangle, CheckCircle, X, Plus, Filter, Star, FileText, Video, ExternalLink } from 'lucide-react';

// Define data types for support tickets and resources
interface SupportTicket {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'billing' | 'feature_request' | 'bug_report' | 'training' | 'general';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignedTo: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastResponse: string;
  responseTime: number; // in hours
  tags: string[];
}

interface SupportResource {
  id: string;
  title: string;
  type: 'faq' | 'guide' | 'video' | 'article';
  category: string;
  description: string;
  url: string;
  views: number;
  helpful: number;
  tags: string[];
}

// Create realistic healthcare-focused support data
const DUMMY_TICKETS: SupportTicket[] = [
  {
    id: 'ticket_001',
    title: 'Policy Configuration Error',
    description: 'Getting an error when trying to configure HIPAA compliance policies for our new AI agent',
    category: 'technical',
    priority: 'high',
    status: 'in_progress',
    assignedTo: '<PERSON> <PERSON>',
    createdBy: 'Dr. Michael Rodriguez',
    createdAt: '2024-12-15T09:30:00Z',
    updatedAt: '2024-12-15T14:45:00Z',
    lastResponse: 'We are investigating the issue with policy configuration. This appears to be related to the recent platform update.',
    responseTime: 5,
    tags: ['policy', 'hipaa', 'configuration']
  },
  {
    id: 'ticket_002',
    title: 'Request for Additional Training Materials',
    description: 'Our team needs more training materials for advanced risk assessment techniques',
    category: 'training',
    priority: 'medium',
    status: 'open',
    assignedTo: 'Training Team',
    createdBy: 'Dr. Emily Watson',
    createdAt: '2024-12-14T16:20:00Z',
    updatedAt: '2024-12-14T16:20:00Z',
    lastResponse: 'Thank you for your request. We will review and create additional training materials.',
    responseTime: 2,
    tags: ['training', 'risk-assessment', 'materials']
  },
  {
    id: 'ticket_003',
    title: 'Agent Monitoring Dashboard Not Loading',
    description: 'The live agent monitoring dashboard is not displaying data for the past 2 hours',
    category: 'bug_report',
    priority: 'urgent',
    status: 'open',
    assignedTo: 'Technical Support',
    createdBy: 'Dr. James Wilson',
    createdAt: '2024-12-15T11:15:00Z',
    updatedAt: '2024-12-15T11:15:00Z',
    lastResponse: 'We are aware of the issue and working on a fix. This affects multiple users.',
    responseTime: 1,
    tags: ['monitoring', 'dashboard', 'urgent']
  },
  {
    id: 'ticket_004',
    title: 'Billing Question - Enterprise Plan',
    description: 'Need clarification on billing for the enterprise plan and additional user licenses',
    category: 'billing',
    priority: 'low',
    status: 'resolved',
    assignedTo: 'Billing Team',
    createdBy: 'Admin User',
    createdAt: '2024-12-13T10:45:00Z',
    updatedAt: '2024-12-14T09:30:00Z',
    lastResponse: 'Your billing question has been resolved. Please check your email for detailed information.',
    responseTime: 23,
    tags: ['billing', 'enterprise', 'licenses']
  },
  {
    id: 'ticket_005',
    title: 'Feature Request: Advanced Analytics',
    description: 'Would like to request advanced analytics features for policy enforcement tracking',
    category: 'feature_request',
    priority: 'medium',
    status: 'open',
    assignedTo: 'Product Team',
    createdBy: 'Dr. Sarah Chen',
    createdAt: '2024-12-12T14:30:00Z',
    updatedAt: '2024-12-12T14:30:00Z',
    lastResponse: 'Thank you for your feature request. We will evaluate this for future releases.',
    responseTime: 4,
    tags: ['feature-request', 'analytics', 'policy']
  },
  {
    id: 'ticket_006',
    title: 'General Platform Questions',
    description: 'Have several questions about platform capabilities and best practices',
    category: 'general',
    priority: 'low',
    status: 'closed',
    assignedTo: 'Support Team',
    createdBy: 'New User',
    createdAt: '2024-12-10T08:15:00Z',
    updatedAt: '2024-12-11T16:20:00Z',
    lastResponse: 'All your questions have been answered. Please refer to our documentation for more details.',
    responseTime: 32,
    tags: ['general', 'questions', 'platform']
  }
];

const DUMMY_RESOURCES: SupportResource[] = [
  {
    id: 'resource_001',
    title: 'Getting Started Guide',
    type: 'guide',
    category: 'onboarding',
    description: 'Complete guide to getting started with the Vitea Governance Platform',
    url: '/docs/getting-started',
    views: 1247,
    helpful: 89,
    tags: ['onboarding', 'basics', 'guide']
  },
  {
    id: 'resource_002',
    title: 'Common Issues and Solutions',
    type: 'faq',
    category: 'troubleshooting',
    description: 'Frequently asked questions and common troubleshooting solutions',
    url: '/docs/faq',
    views: 2156,
    helpful: 156,
    tags: ['faq', 'troubleshooting', 'solutions']
  },
  {
    id: 'resource_003',
    title: 'Policy Configuration Tutorial',
    type: 'video',
    category: 'training',
    description: 'Step-by-step video tutorial for configuring governance policies',
    url: '/videos/policy-configuration',
    views: 892,
    helpful: 67,
    tags: ['video', 'policy', 'tutorial']
  },
  {
    id: 'resource_004',
    title: 'HIPAA Compliance Checklist',
    type: 'article',
    category: 'compliance',
    description: 'Comprehensive checklist for ensuring HIPAA compliance in AI systems',
    url: '/docs/hipaa-checklist',
    views: 567,
    helpful: 45,
    tags: ['hipaa', 'compliance', 'checklist']
  },
  {
    id: 'resource_005',
    title: 'API Documentation',
    type: 'guide',
    category: 'technical',
    description: 'Complete API documentation for developers and integrators',
    url: '/docs/api',
    views: 743,
    helpful: 34,
    tags: ['api', 'technical', 'documentation']
  }
];

const CATEGORIES = [
  { id: 'all', name: 'All Categories', icon: MessageCircle },
  { id: 'technical', name: 'Technical', icon: AlertTriangle },
  { id: 'billing', name: 'Billing', icon: Star },
  { id: 'feature_request', name: 'Feature Request', icon: Plus },
  { id: 'bug_report', name: 'Bug Report', icon: AlertTriangle },
  { id: 'training', name: 'Training', icon: FileText },
  { id: 'general', name: 'General', icon: MessageCircle }
];

export const SupportCenter: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [showMyTickets, setShowMyTickets] = useState(false);
  const [sortBy, setSortBy] = useState<'recent' | 'priority' | 'status'>('recent');
  const [activeTab, setActiveTab] = useState<'tickets' | 'resources' | 'contact'>('tickets');

  // Filter and sort tickets
  const filteredTickets = useMemo(() => {
    let filtered = DUMMY_TICKETS.filter(ticket => {
      const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          ticket.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || ticket.category === selectedCategory;
      const matchesMyTickets = !showMyTickets || ticket.createdBy === 'Dr. Sarah Chen'; // Simulate current user
      
      return matchesSearch && matchesCategory && matchesMyTickets;
    });

    // Sort the filtered results
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
        break;
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        filtered.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
        break;
      case 'status':
        const statusOrder = { open: 1, in_progress: 2, resolved: 3, closed: 4 };
        filtered.sort((a, b) => statusOrder[a.status] - statusOrder[b.status]);
        break;
    }

    return filtered;
  }, [searchTerm, selectedCategory, showMyTickets, sortBy]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'high': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'low': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'in_progress': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'resolved': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'closed': return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Support Center & Help Desk</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Get help with technical support, issue reporting, and assistance requests
          </p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowMyTickets(!showMyTickets)}
            className={`px-4 py-2 rounded-lg transition-colors ${
              showMyTickets 
                ? 'bg-primary text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <User size={16} className="inline mr-2" />
            My Tickets
          </button>
          <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus size={16} className="inline mr-2" />
            New Ticket
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setActiveTab('tickets')}
          className={`px-4 py-2 rounded-t-lg transition-colors ${
            activeTab === 'tickets'
              ? 'bg-primary text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
        >
          <MessageCircle size={16} className="inline mr-2" />
          Support Tickets
        </button>
        <button
          onClick={() => setActiveTab('resources')}
          className={`px-4 py-2 rounded-t-lg transition-colors ${
            activeTab === 'resources'
              ? 'bg-primary text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
        >
          <FileText size={16} className="inline mr-2" />
          Help Resources
        </button>
        <button
          onClick={() => setActiveTab('contact')}
          className={`px-4 py-2 rounded-t-lg transition-colors ${
            activeTab === 'contact'
              ? 'bg-primary text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
        >
          <Phone size={16} className="inline mr-2" />
          Contact Support
        </button>
      </div>

      {activeTab === 'tickets' && (
        <>
          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
            >
              <option value="recent">Most Recent</option>
              <option value="priority">Priority</option>
              <option value="status">Status</option>
            </select>
          </div>

          {/* Category Tabs */}
          <div className="flex flex-wrap gap-2">
            {CATEGORIES.map(category => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  <Icon size={16} className="mr-2" />
                  {category.name}
                </button>
              );
            })}
          </div>

          {/* Results Count */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {filteredTickets.length} tickets found
          </div>

          {/* Tickets List */}
          <div className="space-y-4">
            {filteredTickets.map(ticket => (
              <div 
                key={ticket.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-all cursor-pointer"
                onClick={() => setSelectedTicket(ticket)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ')}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {ticket.category.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(ticket.updatedAt)}
                  </div>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {ticket.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                  {ticket.description}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>By {ticket.createdBy}</span>
                  <span>Assigned to {ticket.assignedTo}</span>
                </div>

                <div className="flex flex-wrap gap-1 mt-3">
                  {ticket.tags.slice(0, 3).map(tag => (
                    <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {activeTab === 'resources' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {DUMMY_RESOURCES.map(resource => (
            <div key={resource.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-all">
              <div className="flex items-center space-x-2 mb-3">
                {resource.type === 'video' && <Video size={16} className="text-blue-500" />}
                {resource.type === 'guide' && <FileText size={16} className="text-green-500" />}
                {resource.type === 'faq' && <MessageCircle size={16} className="text-purple-500" />}
                {resource.type === 'article' && <FileText size={16} className="text-orange-500" />}
                <span className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  {resource.type}
                </span>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {resource.title}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                {resource.description}
              </p>

              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                <span>{resource.views} views</span>
                <span>{resource.helpful} found helpful</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {resource.tags.slice(0, 2).map(tag => (
                    <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                      {tag}
                    </span>
                  ))}
                </div>
                <button className="flex items-center px-3 py-1 text-sm text-primary hover:text-blue-700 transition-colors">
                  <ExternalLink size={14} className="mr-1" />
                  View
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'contact' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Phone size={24} className="text-blue-500" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Phone Support</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Get immediate assistance from our technical support team
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">Available: Mon-Fri 8AM-6PM EST</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">1-800-VITEA-AI</p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Mail size={24} className="text-green-500" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Email Support</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Send us a detailed message and we'll respond within 24 hours
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">Response time: Within 24 hours</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100"><EMAIL></p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <MessageCircle size={24} className="text-purple-500" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Live Chat</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Chat with our support team in real-time for quick questions
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">Available: Mon-Fri 9AM-5PM EST</p>
              <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                Start Chat
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Clock size={24} className="text-orange-500" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Emergency Support</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              For critical issues affecting patient care or system security
            </p>
            <div className="space-y-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">24/7 Emergency Hotline</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">1-800-EMERGENCY</p>
            </div>
          </div>
        </div>
      )}

      {/* Ticket Detail Modal */}
      {selectedTicket && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`px-3 py-1 text-sm rounded-full ${getPriorityColor(selectedTicket.priority)}`}>
                      {selectedTicket.priority}
                    </span>
                    <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(selectedTicket.status)}`}>
                      {selectedTicket.status.replace('_', ' ')}
                    </span>
                    <span className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">
                      {selectedTicket.category.replace('_', ' ')}
                    </span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {selectedTicket.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {selectedTicket.description}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedTicket(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Created By</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedTicket.createdBy}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Assigned To</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedTicket.assignedTo}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Created</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{formatDate(selectedTicket.createdAt)}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Response Time</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedTicket.responseTime} hours</p>
                </div>
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Latest Response</h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedTicket.lastResponse}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex space-x-3">
                  <button className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <MessageCircle size={16} className="mr-2" />
                    Reply
                  </button>
                  <button className="flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <Plus size={16} className="mr-2" />
                    Add Note
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="flex items-center px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                    <Star size={16} className="mr-2" />
                    Escalate
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 