import React, { useState, useMemo } from 'react';
import { Search, BookOpen, Video, Play, Clock, User, Star, Award, Calendar, CheckCircle, ChevronRight, Download, Bookmark, Share2, X, Filter, Plus } from 'lucide-react';

// Define data types for training materials
interface TrainingCourse {
  id: string;
  title: string;
  category: 'governance_basics' | 'policy_management' | 'risk_assessment' | 'compliance' | 'security' | 'advanced_topics' | 'certification';
  type: 'course' | 'workshop' | 'webinar' | 'certification';
  description: string;
  instructor: string;
  duration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  enrolledCount: number;
  maxCapacity: number;
  startDate: string;
  endDate: string;
  isEnrolled: boolean;
  progress: number;
  certificate: boolean;
  price: number;
  tags: string[];
}

// Create realistic healthcare-focused training data
const DUMMY_COURSES: TrainingCourse[] = [
  {
    id: 'course_001',
    title: 'AI Governance Fundamentals',
    category: 'governance_basics',
    type: 'course',
    description: 'Essential principles and practices for governing AI systems in healthcare environments',
    instructor: 'Dr. <PERSON>',
    duration: 480,
    difficulty: 'beginner',
    rating: 4.8,
    enrolledCount: 156,
    maxCapacity: 200,
    startDate: '2024-12-20',
    endDate: '2025-01-20',
    isEnrolled: true,
    progress: 65,
    certificate: true,
    price: 299,
    tags: ['basics', 'governance', 'healthcare']
  },
  {
    id: 'course_002',
    title: 'Advanced Policy Configuration Workshop',
    category: 'policy_management',
    type: 'workshop',
    description: 'Hands-on workshop for creating complex governance policies and guardrails',
    instructor: 'Dr. Michael Rodriguez',
    duration: 360,
    difficulty: 'advanced',
    rating: 4.9,
    enrolledCount: 23,
    maxCapacity: 30,
    startDate: '2024-12-25',
    endDate: '2024-12-27',
    isEnrolled: false,
    progress: 0,
    certificate: true,
    price: 599,
    tags: ['advanced', 'policies', 'workshop']
  },
  {
    id: 'course_003',
    title: 'Risk Assessment Certification',
    category: 'certification',
    type: 'certification',
    description: 'Comprehensive certification program for AI risk assessment professionals',
    instructor: 'Dr. Emily Watson',
    duration: 1200,
    difficulty: 'advanced',
    rating: 4.7,
    enrolledCount: 45,
    maxCapacity: 100,
    startDate: '2024-12-15',
    endDate: '2025-03-15',
    isEnrolled: true,
    progress: 25,
    certificate: true,
    price: 1299,
    tags: ['certification', 'risk-assessment', 'professional']
  },
  {
    id: 'course_004',
    title: 'HIPAA Compliance for AI Systems',
    category: 'compliance',
    type: 'course',
    description: 'Comprehensive guide to ensuring AI systems comply with HIPAA regulations',
    instructor: 'Legal Team',
    duration: 240,
    difficulty: 'intermediate',
    rating: 4.6,
    enrolledCount: 89,
    maxCapacity: 150,
    startDate: '2024-12-18',
    endDate: '2025-01-18',
    isEnrolled: false,
    progress: 0,
    certificate: true,
    price: 199,
    tags: ['hipaa', 'compliance', 'legal']
  },
  {
    id: 'course_005',
    title: 'AI Security Best Practices',
    category: 'security',
    type: 'webinar',
    description: 'Live webinar on securing AI systems in healthcare environments',
    instructor: 'Security Team',
    duration: 120,
    difficulty: 'intermediate',
    rating: 4.5,
    enrolledCount: 67,
    maxCapacity: 100,
    startDate: '2024-12-22',
    endDate: '2024-12-22',
    isEnrolled: true,
    progress: 0,
    certificate: false,
    price: 99,
    tags: ['security', 'webinar', 'best-practices']
  }
];

const CATEGORIES = [
  { id: 'all', name: 'All Courses', icon: BookOpen },
  { id: 'governance_basics', name: 'Governance Basics', icon: BookOpen },
  { id: 'policy_management', name: 'Policy Management', icon: Filter },
  { id: 'risk_assessment', name: 'Risk Assessment', icon: Award },
  { id: 'compliance', name: 'Compliance', icon: CheckCircle },
  { id: 'security', name: 'Security', icon: Star },
  { id: 'advanced_topics', name: 'Advanced Topics', icon: Star },
  { id: 'certification', name: 'Certification', icon: Award }
];

export const TrainingMaterialsHub: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedCourse, setSelectedCourse] = useState<TrainingCourse | null>(null);
  const [showEnrolled, setShowEnrolled] = useState(false);
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'rating' | 'duration'>('recent');

  // Filter and sort courses
  const filteredCourses = useMemo(() => {
    let filtered = DUMMY_COURSES.filter(course => {
      const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          course.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
      const matchesEnrolled = !showEnrolled || course.isEnrolled;
      
      return matchesSearch && matchesCategory && matchesEnrolled;
    });

    // Sort the filtered results
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());
        break;
      case 'popular':
        filtered.sort((a, b) => b.enrolledCount - a.enrolledCount);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'duration':
        filtered.sort((a, b) => a.duration - b.duration);
        break;
    }

    return filtered;
  }, [searchTerm, selectedCategory, showEnrolled, sortBy]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'intermediate': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'advanced': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Training Materials Hub</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Comprehensive training resources for governance best practices and platform usage
          </p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowEnrolled(!showEnrolled)}
            className={`px-4 py-2 rounded-lg transition-colors ${
              showEnrolled 
                ? 'bg-primary text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <User size={16} className="inline mr-2" />
            My Courses
          </button>
          <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus size={16} className="inline mr-2" />
            Create Course
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search training courses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="recent">Most Recent</option>
          <option value="popular">Most Popular</option>
          <option value="rating">Highest Rated</option>
          <option value="duration">Shortest Duration</option>
        </select>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2">
        {CATEGORIES.map(category => {
          const Icon = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <Icon size={16} className="mr-2" />
              {category.name}
            </button>
          );
        })}
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {filteredCourses.length} training courses found
      </div>

      {/* Course Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCourses.map(course => (
          <div 
            key={course.id}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-all cursor-pointer"
            onClick={() => setSelectedCourse(course)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  {course.category.replace('_', ' ')}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {course.certificate && <Award size={16} className="text-yellow-500" />}
              </div>
            </div>

            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
              {course.title}
            </h3>
            
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
              {course.description}
            </p>

            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(course.difficulty)}`}>
                  {course.difficulty}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDuration(course.duration)}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Star size={14} className="text-yellow-400" fill="currentColor" />
                <span className="text-xs text-gray-600 dark:text-gray-400">{course.rating}</span>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
              <span>By {course.instructor}</span>
              <span>{course.enrolledCount}/{course.maxCapacity} enrolled</span>
            </div>

            {course.isEnrolled && (
              <div className="mb-3">
                <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                  <span>Progress</span>
                  <span>{course.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all" 
                    style={{ width: `${course.progress}%` }}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {course.tags.slice(0, 2).map(tag => (
                  <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                    {tag}
                  </span>
                ))}
              </div>
              <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                ${course.price}
              </div>
            </div>

            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Starts {formatDate(course.startDate)}</span>
                <span>Ends {formatDate(course.endDate)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Course Detail Modal */}
      {selectedCourse && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="px-3 py-1 text-sm bg-primary text-white rounded-full">
                      {selectedCourse.category.replace('_', ' ')}
                    </span>
                    <span className={`px-3 py-1 text-sm rounded-full ${getDifficultyColor(selectedCourse.difficulty)}`}>
                      {selectedCourse.difficulty}
                    </span>
                    {selectedCourse.certificate && (
                      <span className="px-3 py-1 text-sm bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full">
                        Certificate
                      </span>
                    )}
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {selectedCourse.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {selectedCourse.description}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedCourse(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Instructor</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedCourse.instructor}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Duration</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{formatDuration(selectedCourse.duration)}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Star size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Rating</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedCourse.rating}/5.0</p>
                </div>
              </div>

              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex space-x-3">
                  {selectedCourse.isEnrolled ? (
                    <button className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                      <Play size={16} className="mr-2" />
                      Continue Learning
                    </button>
                  ) : (
                    <button className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                      <Plus size={16} className="mr-2" />
                      Enroll Now
                    </button>
                  )}
                  <button className="flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <Share2 size={16} className="mr-2" />
                    Share
                  </button>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  ${selectedCourse.price}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 