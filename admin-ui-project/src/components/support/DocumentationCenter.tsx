import React, { useState, useMemo } from 'react';
import { Search, BookOpen, FileText, Video, Download, Star, Clock, User, Tag, ChevronRight, ExternalLink, Bookmark, Share2, X } from 'lucide-react';

// Define data types for documentation
interface DocumentationItem {
  id: string;
  title: string;
  category: 'user_guides' | 'best_practices' | 'api_reference' | 'troubleshooting' | 'security' | 'compliance' | 'training';
  type: 'article' | 'video' | 'pdf' | 'interactive';
  description: string;
  author: string;
  lastUpdated: string;
  readTime: number; // in minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  rating: number;
  views: number;
  isBookmarked: boolean;
  content: string;
}

// Create realistic healthcare-focused documentation data
const DUMMY_DOCUMENTATION: DocumentationItem[] = [
  {
    id: 'doc_001',
    title: 'Getting Started with AI Agent Governance',
    category: 'user_guides',
    type: 'article',
    description: 'Complete beginner guide to setting up and managing AI agents in healthcare environments',
    author: 'Dr. <PERSON>',
    lastUpdated: '2024-12-10',
    readTime: 15,
    difficulty: 'beginner',
    tags: ['onboarding', 'basics', 'healthcare'],
    rating: 4.8,
    views: 1247,
    isBookmarked: false,
    content: 'This comprehensive guide walks you through the essential steps to get started with AI agent governance in healthcare...'
  },
  {
    id: 'doc_002',
    title: 'HIPAA Compliance for AI Agents',
    category: 'compliance',
    type: 'pdf',
    description: 'Detailed compliance guidelines for ensuring AI agents meet HIPAA requirements',
    author: 'Legal Team',
    lastUpdated: '2024-12-08',
    readTime: 25,
    difficulty: 'intermediate',
    tags: ['hipaa', 'compliance', 'privacy', 'legal'],
    rating: 4.9,
    views: 892,
    isBookmarked: true,
    content: 'This document provides comprehensive guidelines for ensuring your AI agents comply with HIPAA regulations...'
  },
  {
    id: 'doc_003',
    title: 'Advanced Policy Configuration',
    category: 'best_practices',
    type: 'video',
    description: 'Video tutorial on creating complex governance policies for high-risk AI applications',
    author: 'Dr. Michael Rodriguez',
    lastUpdated: '2024-12-05',
    readTime: 45,
    difficulty: 'advanced',
    tags: ['policies', 'advanced', 'governance'],
    rating: 4.7,
    views: 567,
    isBookmarked: false,
    content: 'Learn advanced techniques for configuring governance policies that protect patient data while enabling AI innovation...'
  },
  {
    id: 'doc_004',
    title: 'API Reference: Guardrails Library',
    category: 'api_reference',
    type: 'interactive',
    description: 'Interactive API documentation for the guardrails library with live examples',
    author: 'Engineering Team',
    lastUpdated: '2024-12-12',
    readTime: 30,
    difficulty: 'intermediate',
    tags: ['api', 'guardrails', 'technical'],
    rating: 4.6,
    views: 743,
    isBookmarked: false,
    content: 'Complete API reference for the guardrails library including authentication, endpoints, and code examples...'
  },
  {
    id: 'doc_005',
    title: 'Troubleshooting Common Issues',
    category: 'troubleshooting',
    type: 'article',
    description: 'Quick reference guide for resolving common platform issues and errors',
    author: 'Support Team',
    lastUpdated: '2024-12-15',
    readTime: 10,
    difficulty: 'beginner',
    tags: ['troubleshooting', 'support', 'issues'],
    rating: 4.5,
    views: 1567,
    isBookmarked: true,
    content: 'This guide covers the most common issues users encounter and provides step-by-step solutions...'
  },
  {
    id: 'doc_006',
    title: 'Security Best Practices',
    category: 'security',
    type: 'pdf',
    description: 'Comprehensive security guidelines for healthcare AI deployments',
    author: 'Security Team',
    lastUpdated: '2024-12-01',
    readTime: 35,
    difficulty: 'intermediate',
    tags: ['security', 'healthcare', 'best-practices'],
    rating: 4.9,
    views: 678,
    isBookmarked: true,
    content: 'Essential security practices for protecting patient data and ensuring AI system integrity...'
  },
  {
    id: 'doc_007',
    title: 'Training: Risk Assessment Framework',
    category: 'training',
    type: 'video',
    description: 'Interactive training module on conducting AI risk assessments',
    author: 'Dr. Emily Watson',
    lastUpdated: '2024-12-03',
    readTime: 60,
    difficulty: 'advanced',
    tags: ['training', 'risk-assessment', 'framework'],
    rating: 4.8,
    views: 445,
    isBookmarked: false,
    content: 'Comprehensive training on the risk assessment framework used for evaluating AI agent safety...'
  }
];

const CATEGORIES = [
  { id: 'all', name: 'All Documentation', icon: BookOpen },
  { id: 'user_guides', name: 'User Guides', icon: FileText },
  { id: 'best_practices', name: 'Best Practices', icon: Star },
  { id: 'api_reference', name: 'API Reference', icon: ExternalLink },
  { id: 'troubleshooting', name: 'Troubleshooting', icon: Clock },
  { id: 'security', name: 'Security', icon: Tag },
  { id: 'compliance', name: 'Compliance', icon: User },
  { id: 'training', name: 'Training', icon: Video }
];

export const DocumentationCenter: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDoc, setSelectedDoc] = useState<DocumentationItem | null>(null);
  const [showBookmarked, setShowBookmarked] = useState(false);
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'rating'>('recent');

  // Filter and sort documentation
  const filteredDocs = useMemo(() => {
    let filtered = DUMMY_DOCUMENTATION.filter(doc => {
      const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
      const matchesBookmarked = !showBookmarked || doc.isBookmarked;
      
      return matchesSearch && matchesCategory && matchesBookmarked;
    });

    // Sort the filtered results
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());
        break;
      case 'popular':
        filtered.sort((a, b) => b.views - a.views);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
    }

    return filtered;
  }, [searchTerm, selectedCategory, showBookmarked, sortBy]);

  const getCategoryIcon = (category: string) => {
    const categoryObj = CATEGORIES.find(cat => cat.id === category);
    return categoryObj ? categoryObj.icon : BookOpen;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'intermediate': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'advanced': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return Video;
      case 'pdf': return FileText;
      case 'interactive': return ExternalLink;
      default: return FileText;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Documentation Center</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Comprehensive guides, best practices, and reference materials for AI governance
          </p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setShowBookmarked(!showBookmarked)}
            className={`px-4 py-2 rounded-lg transition-colors ${
              showBookmarked 
                ? 'bg-primary text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <Bookmark size={16} className="inline mr-2" />
            Bookmarks
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search documentation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="recent">Most Recent</option>
          <option value="popular">Most Popular</option>
          <option value="rating">Highest Rated</option>
        </select>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2">
        {CATEGORIES.map(category => {
          const Icon = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <Icon size={16} className="mr-2" />
              {category.name}
            </button>
          );
        })}
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {filteredDocs.length} documentation items found
      </div>

      {/* Documentation Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDocs.map(doc => {
          const TypeIcon = getTypeIcon(doc.type);
          const CategoryIcon = getCategoryIcon(doc.category);
          
          return (
            <div 
              key={doc.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-all cursor-pointer"
              onClick={() => setSelectedDoc(doc)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <CategoryIcon size={16} className="text-gray-500 dark:text-gray-400" />
                  <span className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    {doc.category.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <TypeIcon size={16} className="text-gray-400" />
                  {doc.isBookmarked && <Bookmark size={16} className="text-primary" fill="currentColor" />}
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                {doc.title}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                {doc.description}
              </p>

              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(doc.difficulty)}`}>
                    {doc.difficulty}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {doc.readTime} min read
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star size={14} className="text-yellow-400" fill="currentColor" />
                  <span className="text-xs text-gray-600 dark:text-gray-400">{doc.rating}</span>
                </div>
              </div>

              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>By {doc.author}</span>
                <span>{doc.views} views</span>
              </div>

              <div className="flex flex-wrap gap-1 mt-3">
                {doc.tags.slice(0, 3).map(tag => (
                  <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Documentation Detail Modal */}
      {selectedDoc && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="px-3 py-1 text-sm bg-primary text-white rounded-full">
                      {selectedDoc.category.replace('_', ' ')}
                    </span>
                    <span className={`px-3 py-1 text-sm rounded-full ${getDifficultyColor(selectedDoc.difficulty)}`}>
                      {selectedDoc.difficulty}
                    </span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {selectedDoc.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {selectedDoc.description}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedDoc(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Author</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedDoc.author}</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Read Time</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedDoc.readTime} minutes</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Star size={16} className="text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Rating</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">{selectedDoc.rating}/5.0</p>
                </div>
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Content Preview</h3>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedDoc.content}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex space-x-3">
                  <button className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <Download size={16} className="mr-2" />
                    Download
                  </button>
                  <button className="flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    <Share2 size={16} className="mr-2" />
                    Share
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="flex items-center px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                    <Bookmark size={16} className="mr-2" />
                    {selectedDoc.isBookmarked ? 'Bookmarked' : 'Bookmark'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 