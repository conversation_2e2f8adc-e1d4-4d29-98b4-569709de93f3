import React, { useEffect, useMemo, useState } from 'react';
import { X, Loader2 } from 'lucide-react';
import { getApiBaseUrl } from '../api/getApiBase';

export default function PolicyAssignmentsModal({ policyId, onClose }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState({ policy: null, assignments: [] });

  // Prevent background scrolling when modal is open
  useEffect(() => {
    // Save current overflow style
    const originalOverflow = document.body.style.overflow;
    // Prevent scrolling on mount
    document.body.style.overflow = 'hidden';
    
    // Re-enable scrolling on unmount
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  useEffect(() => {
    const controller = new AbortController();
    async function fetchAssignments() {
      try {
        setLoading(true);
        setError(null);
        const res = await fetch(`${getApiBaseUrl()}/api/v1/policies/${policyId}/assignments`, {
          headers: { Authorization: 'Bearer admin-token' },
          signal: controller.signal,
        });
        if (!res.ok) {
          const text = await res.text();
          throw new Error(text || `Failed to fetch assignments (${res.status})`);
        }
        const json = await res.json();
        setData(json);
      } catch (err) {
        if (err.name !== 'AbortError') setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchAssignments();
    return () => controller.abort();
  }, [policyId]);

  const { agentCount, roleCount } = useMemo(() => {
    const uniqueAgents = new Set();
    const uniqueRoles = new Set();
    for (const row of data.assignments || []) {
      if (row.agent_id) uniqueAgents.add(row.agent_id);
      if (row.role_id) uniqueRoles.add(row.role_id);
    }
    return { agentCount: uniqueAgents.size, roleCount: uniqueRoles.size };
  }, [data.assignments]);

  const statusBadge = (isActive) => (
    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${isActive ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'}`}>
      {isActive ? 'Active' : 'Inactive'}
    </span>
  );

  const severityBadge = (severity) => {
    const map = {
      critical: 'bg-red-100 text-red-700',
      high: 'bg-orange-100 text-orange-700',
      medium: 'bg-yellow-100 text-yellow-700',
      low: 'bg-green-100 text-green-700',
    };
    return <span className={`px-2 py-1 rounded-full text-xs font-semibold ${map[severity] || 'bg-gray-100 text-gray-700'}`}>{severity || '—'}</span>;
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl ring-1 ring-black/10 dark:ring-white/10 w-full max-w-6xl max-h-[85vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex-shrink-0 flex items-start justify-between p-5 border-b dark:border-gray-700">
          <div className="space-y-1">
            <div className="text-xl md:text-2xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
              {data.policy?.name || 'Policy'}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 max-w-[60ch]">
              {data.policy?.description || '—'}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-700"
            aria-label="Close"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Meta row */}
        <div className="flex-shrink-0 flex items-center justify-between px-5 py-3 border-b dark:border-gray-800 bg-white dark:bg-gray-800">
          <div className="flex items-center gap-2 text-sm">
            {statusBadge(Boolean(data.policy?.is_active))}
            {severityBadge(data.policy?.severity)}
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-300 flex items-center gap-4">
            <span>
              <span className="text-gray-500">Version:</span> {data.policy?.version ?? '—'}
            </span>
            <span>
              <span className="text-gray-500">Assigned:</span> {agentCount} agents, {roleCount} roles
            </span>
          </div>
        </div>

        {/* Body */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-10 text-gray-500">
              <Loader2 className="h-5 w-5 animate-spin mr-2" /> Loading assignments…
            </div>
          ) : error ? (
            <div className="p-5 text-red-600 text-sm">{error}</div>
          ) : (
            <div className="p-5 pt-4">
              <div className="overflow-x-auto rounded-lg ring-1 ring-gray-200 dark:ring-gray-700">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-900/40">
                    <tr>
                      {['Agent Name', 'Description', 'Agent Type', 'Active', 'Role', 'Role Description', 'Assignment'].map((h) => (
                        <th key={h} className="px-4 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                          {h}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {(data.assignments || []).map((row, idx) => (
                      <tr key={`${row.agent_id || idx}-${row.role_id || 'no-role'}`} className="hover:bg-gray-50 dark:hover:bg-gray-800/60">
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{row.agent_name}</td>
                        <td className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300">{row.agent_description || '—'}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-200">{row.agent_type || '—'}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${row.agent_is_active ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'}`}>
                            {row.agent_is_active ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-200">{row.role_name || '—'}</td>
                        <td className="px-4 py-2 text-sm text-gray-600 dark:text-gray-300">{row.role_description || '—'}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-200">{row.assignment_type || '—'}</td>
                      </tr>
                    ))}
                    {data.assignments?.length === 0 && (
                      <tr>
                        <td colSpan={7} className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400">
                          No assignments found for this policy.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}