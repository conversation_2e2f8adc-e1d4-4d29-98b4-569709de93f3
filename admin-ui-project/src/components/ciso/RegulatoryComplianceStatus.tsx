import React, { useState } from "react"; import { <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, Clock, TrendingUp, TrendingDown } from "lucide-react"; export const RegulatoryComplianceStatus: React.FC = () => { const [selectedTimeframe, setSelectedTimeframe] = useState("30d"); return (<div className="space-y-6"><div className="flex justify-between items-center"><div><h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Regulatory Compliance Status</h1><p className="text-gray-600 dark:text-gray-400 mt-1">Executive compliance dashboard providing real-time visibility into regulatory adherence</p></div><select value={selectedTimeframe} onChange={(e) => setSelectedTimeframe(e.target.value)} className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"><option value="30d">Last 30 Days</option><option value="90d">Last 90 Days</option><option value="1y">Last Year</option></select></div><div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">HIPAA Compliance</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">98%</span><CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" /></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">HITECH Compliance</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">95%</span><CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" /></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">FDA Compliance</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">87%</span><AlertTriangle className="w-8 h-8 text-yellow-600 dark:text-yellow-400" /></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">State Regulations</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">92%</span><CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" /></div></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Compliance Overview</h2><p className="text-gray-700 dark:text-gray-300">Comprehensive regulatory compliance monitoring for healthcare AI systems. All major compliance frameworks are being tracked with automated reporting and alerting.</p></div></div>); };
