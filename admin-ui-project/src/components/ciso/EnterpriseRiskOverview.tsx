import React, { useState, useMemo } from 'react';
import { TrendingUp, TrendingDown, AlertTriangle, Shield, Activity, CheckCircle, XCircle } from 'lucide-react';

interface RiskCategory {
  id: string;
  name: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  score: number;
  trend: 'up' | 'down' | 'stable';
  description: string;
  affectedAgents: number;
  complianceImpact: string[];
}

interface RiskMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'good' | 'warning' | 'critical';
}

const DUMMY_RISK_CATEGORIES: RiskCategory[] = [
  {
    id: 'data_privacy',
    name: 'Data Privacy & PHI Protection',
    riskLevel: 'high',
    score: 7.8,
    trend: 'up',
    description: 'Risks related to Protected Health Information (PHI) exposure and HIPAA compliance',
    affectedAgents: 12,
    complianceImpact: ['HIPAA', 'HITECH', 'State Privacy Laws']
  },
  {
    id: 'cybersecurity',
    name: 'Cybersecurity Threats',
    riskLevel: 'critical',
    score: 9.1,
    trend: 'up',
    description: 'External cyber threats targeting healthcare systems and patient data',
    affectedAgents: 15,
    complianceImpact: ['HIPAA', 'NIST', 'ISO 27001']
  },
  {
    id: 'ai_bias',
    name: 'AI Bias & Fairness',
    riskLevel: 'medium',
    score: 5.2,
    trend: 'down',
    description: 'Potential bias in AI algorithms affecting patient care decisions',
    affectedAgents: 8,
    complianceImpact: ['Civil Rights', 'Healthcare Equity']
  },
  {
    id: 'regulatory_compliance',
    name: 'Regulatory Compliance',
    riskLevel: 'high',
    score: 6.9,
    trend: 'up',
    description: 'Compliance with healthcare regulations and AI governance requirements',
    affectedAgents: 18,
    complianceImpact: ['FDA', 'HIPAA', 'State Regulations']
  }
];

const DUMMY_RISK_METRICS: RiskMetric[] = [
  {
    id: 'overall_risk_score',
    name: 'Overall Risk Score',
    value: 6.3,
    previousValue: 6.1,
    unit: '/10',
    trend: 'up',
    status: 'warning'
  },
  {
    id: 'active_incidents',
    name: 'Active Incidents',
    value: 8,
    previousValue: 12,
    unit: '',
    trend: 'down',
    status: 'good'
  },
  {
    id: 'compliance_score',
    name: 'Compliance Score',
    value: 87,
    previousValue: 85,
    unit: '%',
    trend: 'up',
    status: 'good'
  },
  {
    id: 'security_score',
    name: 'Security Score',
    value: 78,
    previousValue: 82,
    unit: '%',
    trend: 'down',
    status: 'warning'
  }
];

export const EnterpriseRiskOverview: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d'>('30d');
  const [selectedRiskCategory, setSelectedRiskCategory] = useState<RiskCategory | null>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const riskSummary = useMemo(() => {
    const criticalRisks = DUMMY_RISK_CATEGORIES.filter(r => r.riskLevel === 'critical').length;
    const highRisks = DUMMY_RISK_CATEGORIES.filter(r => r.riskLevel === 'high').length;
    const mediumRisks = DUMMY_RISK_CATEGORIES.filter(r => r.riskLevel === 'medium').length;
    
    return { criticalRisks, highRisks, mediumRisks };
  }, []);

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'high': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'low': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-green-600 dark:text-green-400" />;
      default: return <Activity className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Enterprise Risk Overview</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Executive-level risk assessment and portfolio-wide risk visibility
          </p>
        </div>
        <select
          value={selectedTimeframe}
          onChange={(e) => setSelectedTimeframe(e.target.value as '7d' | '30d' | '90d')}
          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="7d">Last 7 Days</option>
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
        </select>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {DUMMY_RISK_METRICS.map(metric => (
          <div key={metric.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{metric.name}</h3>
              {getTrendIcon(metric.trend)}
            </div>
            <div className="flex items-baseline">
              <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">{metric.value}</span>
              <span className="text-lg text-gray-500 dark:text-gray-400 ml-1">{metric.unit}</span>
            </div>
            <div className="flex items-center mt-2">
              <span className={`text-sm ${
                metric.trend === 'up' ? 'text-red-600 dark:text-red-400' :
                metric.trend === 'down' ? 'text-green-600 dark:text-green-400' :
                'text-gray-600 dark:text-gray-400'
              }`}>
                {metric.trend === 'up' ? '+' : metric.trend === 'down' ? '-' : ''}
                {Math.abs(metric.value - metric.previousValue).toFixed(1)}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">vs previous period</span>
            </div>
          </div>
        ))}
      </div>

      {/* Risk Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Critical Risks</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{riskSummary.criticalRisks}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">High Risks</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{riskSummary.highRisks}</p>
            </div>
            <Shield className="w-8 h-8 text-orange-600 dark:text-orange-400" />
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Medium Risks</p>
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{riskSummary.mediumRisks}</p>
            </div>
            <Activity className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Risk Categories */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Risk Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {DUMMY_RISK_CATEGORIES.map(category => (
            <div 
              key={category.id}
              onClick={() => setSelectedRiskCategory(category)}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900 dark:text-gray-100">{category.name}</h3>
                <span className={`px-2 py-1 text-xs rounded-full ${getRiskLevelColor(category.riskLevel)}`}>
                  {category.riskLevel}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{category.description}</p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">
                  Score: <span className="font-medium text-gray-900 dark:text-gray-100">{category.score}</span>
                </span>
                <span className="text-gray-500 dark:text-gray-400">
                  {category.affectedAgents} agents affected
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Category Detail Modal */}
      {selectedRiskCategory && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">{selectedRiskCategory.name}</h2>
                  <span className={`px-3 py-1 rounded-full text-sm ${getRiskLevelColor(selectedRiskCategory.riskLevel)}`}>
                    {selectedRiskCategory.riskLevel} Risk
                  </span>
                </div>
                <button 
                  onClick={() => setSelectedRiskCategory(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Description</h3>
                  <p className="text-gray-700 dark:text-gray-300">{selectedRiskCategory.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Risk Assessment</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Risk Score:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">{selectedRiskCategory.score}/10</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Affected Agents:</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">{selectedRiskCategory.affectedAgents}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Compliance Impact</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedRiskCategory.complianceImpact.map(compliance => (
                        <span key={compliance} className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm rounded">
                          {compliance}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
