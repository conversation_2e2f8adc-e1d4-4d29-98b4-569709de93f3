import React, { useState } from "react"; import { AlertTriangle, Shield, Activity, Clock, MapPin, TrendingUp, TrendingDown } from "lucide-react"; export const ThreatIntelligenceCenter: React.FC = () => { const [selectedTimeframe, setSelectedTimeframe] = useState("7d"); return (<div className="space-y-6"><div className="flex justify-between items-center"><div><h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Threat Intelligence Center</h1><p className="text-gray-600 dark:text-gray-400 mt-1">Centralized threat intelligence hub aggregating security incidents</p></div><select value={selectedTimeframe} onChange={(e) => setSelectedTimeframe(e.target.value)} className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"><option value="24h">Last 24 Hours</option><option value="7d">Last 7 Days</option><option value="30d">Last 30 Days</option></select></div><div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Active Threats</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-red-600 dark:text-red-400">8</span><AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" /></div><div className="flex items-center mt-2"><TrendingDown className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" /><span className="text-sm text-green-600 dark:text-green-400">-4 from last week</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Critical Threats</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-red-600 dark:text-red-400">2</span><AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-red-600 dark:text-red-400 mr-1" /><span className="text-sm text-red-600 dark:text-red-400">+1 from last week</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Avg Response Time</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-blue-600 dark:text-blue-400">2.3h</span><Clock className="w-8 h-8 text-blue-600 dark:text-blue-400" /></div><div className="flex items-center mt-2"><TrendingDown className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" /><span className="text-sm text-green-600 dark:text-green-400">-0.8h from last week</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Detection Rate</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">94%</span><Shield className="w-8 h-8 text-green-600 dark:text-green-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" /><span className="text-sm text-green-600 dark:text-green-400">+3% from last week</span></div></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent Threat Incidents</h2><div className="space-y-4"><div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"><div className="flex items-center justify-between mb-2"><h3 className="font-medium text-gray-900 dark:text-gray-100">Suspicious API Access Pattern</h3><div className="flex items-center gap-2"><span className="px-2 py-1 text-xs rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200">High</span><span className="px-2 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200">Investigating</span></div></div><p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Unusual API access patterns detected from external IP address targeting clinical data endpoints</p><div className="flex items-center justify-between text-sm"><div className="flex items-center gap-4"><span className="text-gray-500 dark:text-gray-400"><MapPin className="w-4 h-4 inline mr-1" />United States</span><span className="text-gray-500 dark:text-gray-400"><Clock className="w-4 h-4 inline mr-1" />2024-12-15</span></div><span className="text-gray-500 dark:text-gray-400">Confidence: 85%</span></div></div><div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"><div className="flex items-center justify-between mb-2"><h3 className="font-medium text-gray-900 dark:text-gray-100">Potential Prompt Injection Attack</h3><div className="flex items-center gap-2"><span className="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200">Critical</span><span className="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200">Active</span></div></div><p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Attempted prompt injection detected in patient communication system</p><div className="flex items-center justify-between text-sm"><div className="flex items-center gap-4"><span className="text-gray-500 dark:text-gray-400"><MapPin className="w-4 h-4 inline mr-1" />Internal Network</span><span className="text-gray-500 dark:text-gray-400"><Clock className="w-4 h-4 inline mr-1" />2024-12-15</span></div><span className="text-gray-500 dark:text-gray-400">Confidence: 92%</span></div></div></div></div></div>); };
