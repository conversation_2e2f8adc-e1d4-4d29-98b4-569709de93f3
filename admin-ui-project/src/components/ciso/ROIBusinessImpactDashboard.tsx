import React, { useState } from "react"; import { TrendingUp, DollarSign, BarChart3, Target, CheckCircle } from "lucide-react"; export const ROIBusinessImpactDashboard: React.FC = () => { const [selectedTimeframe, setSelectedTimeframe] = useState("1y"); return (<div className="space-y-6"><div className="flex justify-between items-center"><div><h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">ROI & Business Impact Dashboard</h1><p className="text-gray-600 dark:text-gray-400 mt-1">Executive dashboard demonstrating business value and return on investment</p></div><select value={selectedTimeframe} onChange={(e) => setSelectedTimeframe(e.target.value)} className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"><option value="6m">Last 6 Months</option><option value="1y">Last Year</option><option value="2y">Last 2 Years</option></select></div><div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Total Cost Savings</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">$2.4M</span><DollarSign className="w-8 h-8 text-green-600 dark:text-green-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" /><span className="text-sm text-green-600 dark:text-green-400">+15% vs last year</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Efficiency Gains</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-blue-600 dark:text-blue-400">34%</span><BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-1" /><span className="text-sm text-blue-600 dark:text-blue-400">+8% vs last year</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Risk Reduction</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-purple-600 dark:text-purple-400">67%</span><Target className="w-8 h-8 text-purple-600 dark:text-purple-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-purple-600 dark:text-purple-400 mr-1" /><span className="text-sm text-purple-600 dark:text-purple-400">+12% vs last year</span></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Compliance Score</h3><div className="flex items-center justify-between"><span className="text-3xl font-bold text-green-600 dark:text-green-400">94%</span><CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" /></div><div className="flex items-center mt-2"><TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400 mr-1" /><span className="text-sm text-green-600 dark:text-green-400">+3% vs last year</span></div></div></div><div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"><h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Business Impact Summary</h2><p className="text-gray-700 dark:text-gray-300">The AI governance platform has delivered significant business value through cost savings, efficiency improvements, risk reduction, and enhanced compliance. The platform has achieved a 340% ROI over the past year.</p></div></div>); };
