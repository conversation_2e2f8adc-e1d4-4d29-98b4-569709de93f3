import React, { useState } from 'react';
import { createPolicyGroup, updatePolicyGroup } from '../api/policyGroups.js';

export default function PolicyGroupModal({ onClose, onSaved, existing }) {
  const [form, setForm] = useState(
    existing
      ? {
          ...existing,
          tags: Array.isArray(existing.tags) ? existing.tags.join(', ') : existing.tags || '',
        }
      : {
          name: '',
          description: '',
          severity: 'medium',
          tags: '',
          is_template: false,
        }
  );
  const [saving, setSaving] = useState(false);
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((f) => ({ ...f, [name]: type === 'checkbox' ? checked : value }));
  };
  const handleSubmit = async () => {
    if (!form.name.trim()) return alert('Name required');
    setSaving(true);
    try {
      if (existing) {
        await updatePolicyGroup(existing.group_id, {
          ...form,
          tags: form.tags.split(',').map((t) => t.trim()).filter(Boolean),
        });
      } else {
        await createPolicyGroup({
          ...form,
          tags: form.tags.split(',').map((t) => t.trim()).filter(Boolean),
        });
      }
      onSaved();
      onClose();
    } catch (err) {
      alert(err.message || 'Failed to save');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 w-full max-w-md p-6 rounded-lg shadow-lg">
        <h3 className="text-xl font-semibold text-white mb-4">
          {existing ? 'Edit Policy Group' : 'Create Policy Group'}
        </h3>
        <div className="space-y-3">
          <input
            className="w-full px-3 py-2 rounded bg-gray-700 text-white"
            placeholder="Name"
            name="name"
            value={form.name}
            onChange={handleChange}
          />
          <textarea
            className="w-full px-3 py-2 rounded bg-gray-700 text-white"
            placeholder="Description"
            name="description"
            value={form.description}
            onChange={handleChange}
          />
          <div className="flex space-x-3">
            <select
              name="severity"
              value={form.severity}
              onChange={handleChange}
              className="flex-1 bg-gray-700 text-white px-2 py-2 rounded"
            >
              <option value="critical">critical</option>
              <option value="high">high</option>
              <option value="medium">medium</option>
              <option value="low">low</option>
            </select>
            <label className="flex items-center space-x-2 text-sm text-gray-300">
              <input
                type="checkbox"
                name="is_template"
                checked={form.is_template}
                onChange={handleChange}
              />
              <span>Template</span>
            </label>
          </div>
          <input
            className="w-full px-3 py-2 rounded bg-gray-700 text-white"
            placeholder="Tags (comma separated)"
            name="tags"
            value={form.tags}
            onChange={handleChange}
          />
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <button
            className="px-4 py-2 bg-gray-600 rounded text-white"
            onClick={onClose}
            disabled={saving}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-blue-600 rounded text-white disabled:opacity-50"
            onClick={handleSubmit}
            disabled={saving}
          >
            {saving ? 'Saving…' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
}
