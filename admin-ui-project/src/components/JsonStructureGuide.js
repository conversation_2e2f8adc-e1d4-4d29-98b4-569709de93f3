import React, { useState, useEffect } from 'react';
import { fetchSchemas } from '../utils/schemaApi';

const JsonStructureGuide = ({ policyType }) => {
  const [policySchemas, setPolicySchemas] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSchemas = async () => {
      try {
        const schemas = await fetchSchemas();
        setPolicySchemas(schemas);
      } catch (error) {
        console.error('Failed to load schemas:', error);
      } finally {
        setLoading(false);
      }
    };
    loadSchemas();
  }, []);

  if (!policyType) {
    return null;
  }

  if (loading) {
    return (
      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="text-sm text-gray-600">Loading schema information...</div>
      </div>
    );
  }

  // Function to get required fields and nested requirements from schema
  const getRequiredFields = (schema) => {
    const required = new Set();
    const nestedRequired = new Map(); // Map of parent object -> required sub-properties
    
    // Get top-level required fields
    if (schema.required) {
      schema.required.forEach(field => required.add(field));
    }
    
    // Get nested required fields and track parent objects
    const traverseObject = (obj, path = '') => {
      if (obj.properties) {
        Object.keys(obj.properties).forEach(prop => {
          const fullPath = path ? `${path}.${prop}` : prop;
          
          // If this is a nested object with required properties
          if (obj.properties[prop].type === 'object' && obj.properties[prop].properties) {
            const nestedObj = obj.properties[prop];
            if (nestedObj.required && nestedObj.required.length > 0) {
              nestedRequired.set(prop, nestedObj.required);
            }
            // Continue traversing
            traverseObject(nestedObj, fullPath);
          } else {
            // Regular property
            if (obj.required && obj.required.includes(prop)) {
              required.add(fullPath);
            }
          }
        });
      }
    };
    
    traverseObject(schema);
    return { required, nestedRequired };
  };

  // Function to get constraint information from schema
  const getConstraintInfo = (schema) => {
    const constraints = [];
    
    if (schema.minimum !== undefined) {
      constraints.push(`min: ${schema.minimum}`);
    }
    if (schema.maximum !== undefined) {
      constraints.push(`max: ${schema.maximum}`);
    }
    if (schema.minLength !== undefined) {
      constraints.push(`min length: ${schema.minLength}`);
    }
    if (schema.maxLength !== undefined) {
      constraints.push(`max length: ${schema.maxLength}`);
    }
    if (schema.minItems !== undefined) {
      constraints.push(`min items: ${schema.minItems}`);
    }
    if (schema.maxItems !== undefined) {
      constraints.push(`max items: ${schema.maxItems}`);
    }
    if (schema.pattern) {
      constraints.push(`pattern: ${schema.pattern}`);
    }
    if (schema.enum) {
      constraints.push(`enum: [${schema.enum.join(', ')}]`);
    }
    
    return constraints;
  };

  // Function to generate enhanced structure with dynamic requirements
  const generateEnhancedStructure = (policyType) => {
    const schema = policySchemas[policyType];
    if (!schema) {
      return `{
  "type": "${policyType}",                      // Required - Policy type identifier
  "severity": "medium",                         // Required - low/medium/high/critical
  "description": "Policy description",          // Optional - Human-readable description
  "enabled": true                               // Optional - Default: true (whether policy is active)
}`;
    }

    const { required, nestedRequired } = getRequiredFields(schema);
    
    // Generate structure based on schema
    let structure = '{\n';
    
    // Add properties based on schema
    Object.keys(schema.properties).forEach(prop => {
      const propSchema = schema.properties[prop];
      const isRequired = required.has(prop);
      const isParentOptional = !isRequired && nestedRequired.has(prop);
      
      // Determine comment type
      let comment;
      if (isRequired) {
        comment = 'Required';
      } else if (isParentOptional) {
        comment = 'Optional*'; // Indicates optional parent with required children
      } else {
        comment = 'Optional';
      }
      
      if (propSchema.type === 'object' && propSchema.properties) {
        // Handle nested objects
        const nestedRequiredProps = nestedRequired.get(prop) || [];
        structure += `  "${prop}": {                           // ${comment} - ${propSchema.description || prop}\n`;
        
        Object.keys(propSchema.properties).forEach(nestedProp => {
          const nestedSchema = propSchema.properties[nestedProp];
          const nestedIsRequired = nestedRequiredProps.includes(nestedProp);
          const nestedComment = nestedIsRequired ? 'Required*' : 'Optional';
          
          // Get constraint information
          const constraints = getConstraintInfo(nestedSchema);
          const constraintText = constraints.length > 0 ? ` (${constraints.join(', ')})` : '';
          const defaultValue = nestedSchema.default !== undefined ? ` (Default: ${JSON.stringify(nestedSchema.default)})` : '';
          const fullInfo = constraintText + defaultValue;
          
          if (nestedSchema.type === 'array') {
            structure += `    "${nestedProp}": [],                    // ${nestedComment} - ${nestedSchema.description || nestedProp}${fullInfo}\n`;
          } else if (nestedSchema.type === 'boolean') {
            structure += `    "${nestedProp}": ${nestedSchema.default || false},                    // ${nestedComment} - ${nestedSchema.description || nestedProp}${fullInfo}\n`;
          } else if (nestedSchema.type === 'integer') {
            structure += `    "${nestedProp}": ${nestedSchema.default || 0},                    // ${nestedComment} - ${nestedSchema.description || nestedProp}${fullInfo}\n`;
          } else if (nestedSchema.type === 'number') {
            structure += `    "${nestedProp}": ${nestedSchema.default || 0},                    // ${nestedComment} - ${nestedSchema.description || nestedProp}${fullInfo}\n`;
          } else {
            structure += `    "${nestedProp}": "${nestedSchema.default || ''}",                    // ${nestedComment} - ${nestedSchema.description || nestedProp}${fullInfo}\n`;
          }
        });
        
        structure += '  },\n';
      } else {
        // Handle top-level properties
        const constraints = getConstraintInfo(propSchema);
        const constraintText = constraints.length > 0 ? ` (${constraints.join(', ')})` : '';
        const defaultValue = propSchema.default !== undefined ? ` (Default: ${JSON.stringify(propSchema.default)})` : '';
        const fullInfo = constraintText + defaultValue;
        
        if (propSchema.type === 'array') {
          structure += `  "${prop}": [],                    // ${comment} - ${propSchema.description || prop}${fullInfo}\n`;
        } else if (propSchema.type === 'boolean') {
          structure += `  "${prop}": ${propSchema.default || false},                    // ${comment} - ${propSchema.description || prop}${fullInfo}\n`;
        } else if (propSchema.type === 'integer') {
          structure += `  "${prop}": ${propSchema.default || 0},                    // ${comment} - ${propSchema.description || prop}${fullInfo}\n`;
        } else if (propSchema.type === 'number') {
          structure += `  "${prop}": ${propSchema.default || 0},                    // ${comment} - ${propSchema.description || prop}${fullInfo}\n`;
        } else {
          structure += `  "${prop}": "${propSchema.default || ''}",                    // ${comment} - ${propSchema.description || prop}${fullInfo}\n`;
        }
      }
    });
    
    structure += '}';
    return structure;
  };

  // Function to apply color coding to comments
  const applyColorCoding = (text) => {
    return text
      .replace(
        /\/\/ Required -/g, 
        '<span style="color: #059669; font-weight: 600;">// Required -</span>'
      )
      .replace(
        /\/\/ Required\* -/g, 
        '<span style="color: #dc2626; font-weight: 600;">// Required* -</span>'
      )
      .replace(
        /\/\/ Optional -/g, 
        '<span style="color: #2563eb; font-weight: 600;">// Optional -</span>'
      )
      .replace(
        /\/\/ Optional\* -/g, 
        '<span style="color: #7c3aed; font-weight: 600;">// Optional* -</span>'
      )
      .replace(
        /\/\/ Default:/g, 
        '<span style="color: #6b7280; font-weight: 600;">// Default:</span>'
      )
      .replace(
        /\(min: ([^)]+)\)/g,
        '<span style="color: #059669;">(min: $1)</span>'
      )
      .replace(
        /\(max: ([^)]+)\)/g,
        '<span style="color: #dc2626;">(max: $1)</span>'
      )
      .replace(
        /\(min length: ([^)]+)\)/g,
        '<span style="color: #059669;">(min length: $1)</span>'
      )
      .replace(
        /\(max length: ([^)]+)\)/g,
        '<span style="color: #dc2626;">(max length: $1)</span>'
      )
      .replace(
        /\(min items: ([^)]+)\)/g,
        '<span style="color: #059669;">(min items: $1)</span>'
      )
      .replace(
        /\(max items: ([^)]+)\)/g,
        '<span style="color: #dc2626;">(max items: $1)</span>'
      )
      .replace(
        /\(pattern: ([^)]+)\)/g,
        '<span style="color: #7c2d12;">(pattern: $1)</span>'
      )
      .replace(
        /\(enum: \[([^\]]+)\]\)/g,
        '<span style="color: #1e40af;">(enum: [$1])</span>'
      );
  };

  // Check if schemas were loaded successfully
  if (!policySchemas || Object.keys(policySchemas).length === 0) {
    return (
      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="text-sm text-gray-600">Unable to load schema information. Please try refreshing the page.</div>
      </div>
    );
  }

  const enhancedStructure = generateEnhancedStructure(policyType);
  const colorCodedStructure = applyColorCoding(enhancedStructure);
  const policyTypeDisplay = policyType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

  return (
    <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
      <h4 className="text-sm font-medium text-gray-800 mb-3">
        📋 Enhanced JSON Structure for {policyTypeDisplay} Policy
      </h4>
      <div className="text-xs text-gray-600 mb-2">
        This shows the complete structure with inline comments. 
        <span className="text-green-600 font-medium"> Green = Required</span>, 
        <span className="text-red-600 font-medium"> Red = Required* (if parent present)</span>,
        <span className="text-blue-600 font-medium"> Blue = Optional</span>, 
        <span className="text-purple-600 font-medium"> Purple = Optional* (parent optional)</span>,
        <span className="text-gray-500 font-medium"> Gray = Default values</span>
      </div>
      <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto max-h-96 overflow-y-auto">
        <code 
          className="text-gray-800" 
          style={{ 
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            lineHeight: '1.4'
          }}
          dangerouslySetInnerHTML={{ __html: colorCodedStructure }}
        />
      </pre>
      <div className="mt-3 text-xs text-gray-600">
        💡 Tip: Use Ctrl+Space in the editor above for autocomplete suggestions
      </div>
    </div>
  );
};

export default JsonStructureGuide; 