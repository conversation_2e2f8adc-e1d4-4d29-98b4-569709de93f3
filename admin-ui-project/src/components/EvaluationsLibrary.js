import React, { useState, useEffect } from 'react';
import {
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { api } from '../utils/evaluationApi.js';
import Breadcrumb from './testing/Breadcrumb.js';

// Utility function for implementation type labels
const getImplementationTypeLabel = (type) => {
  switch (type) {
    case 'python-service':
      return 'Python Service';
    case 'builtin':
      return 'Built-in';
    default:
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

function EvaluationsLibrary() {
  const [metrics, setMetrics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    loadMetrics();
  }, [selectedCategory, selectedType]);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      
      // Fetch evaluators from the evaluator service
      const evaluatorsRes = await api.get('/evaluators');
      const evaluators = evaluatorsRes.data.evaluators || [];
      
      // Filter evaluators based on selected filters
      let filteredEvaluators = evaluators;
      if (selectedCategory !== 'all') {
        filteredEvaluators = filteredEvaluators.filter(e => e.category === selectedCategory);
      }
      
      // Map evaluators to the expected metric format
      const metricsData = filteredEvaluators.map(evaluator => ({
        id: evaluator.id,
        name: evaluator.name,
        description: evaluator.description,
        category: evaluator.category,
        implementation_type: 'python-service', // All evaluators from service are python-service
        config: {
          supported_modes: evaluator.supported_modes,
          config_schema: evaluator.config_schema
        }
      }));
      
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading evaluators:', error);
      setMetrics([]);
    } finally {
      setLoading(false);
    }
  };


  const getImplementationTypeColor = (type) => {
    switch (type) {
      case 'opik':
        return 'bg-purple-100 text-purple-800';
      case 'builtin':
        return 'bg-blue-100 text-blue-800';
      case 'custom':
        return 'bg-yellow-100 text-yellow-800';
      case 'python-service':
        return 'bg-green-100 text-green-800';
      case 'external':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'accuracy':
        return 'bg-green-100 text-green-800';
      case 'safety':
        return 'bg-red-100 text-red-800';
      case 'quality':
        return 'bg-blue-100 text-blue-800';
      case 'performance':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleMetricClick = (metric) => {
    setSelectedMetric(metric);
    setShowModal(true);
  };

  const breadcrumbItems = [
    { name: 'Testing & Evaluation', href: '/' },
    { name: 'Evaluator Library', href: '/evaluations' }
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <Breadcrumb items={breadcrumbItems} />
      {/* Header */}
      <div className="mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Evaluator Library</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
            Repository of evaluation metrics and scoring methods for AI model testing
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="all">All Categories</option>
            <option value="accuracy">Accuracy</option>
            <option value="safety">Safety</option>
            <option value="quality">Quality</option>
            <option value="performance">Performance</option>
            <option value="validation">Validation</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Implementation</label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="all">All Types</option>
            <option value="opik">Opik</option>
            <option value="builtin">Built-in</option>
            <option value="python-service">Python Service</option>
            <option value="custom">Custom</option>
            <option value="external">External</option>
          </select>
        </div>
      </div>


      {/* Metrics Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {loading ? (
          [...Array(6)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 animate-pulse">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded mr-3"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
            </div>
          ))
        ) : (
          metrics.map((metric) => (
            <div
              key={metric.id}
              className="bg-white dark:bg-gray-800 shadow rounded-lg hover:shadow-lg transition-shadow duration-200 overflow-hidden cursor-pointer"
              onClick={() => handleMetricClick(metric)}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(metric.category)}`}>
                    {metric.category}
                  </div>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getImplementationTypeColor(metric.implementation_type)}`}>
                    {getImplementationTypeLabel(metric.implementation_type)}
                  </div>
                </div>
                
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {metric.name}
                </h3>
                
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                  {metric.description}
                </p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500 dark:text-gray-400">Category:</div>
                    <div className="font-medium text-gray-900 dark:text-white capitalize">{metric.category}</div>
                  </div>
                  <div>
                    <div className="text-gray-500 dark:text-gray-400">Type:</div>
                    <div className="font-medium text-gray-900 dark:text-white">{getImplementationTypeLabel(metric.implementation_type)}</div>
                  </div>
                </div>
                
                {metric.config && (
                  <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
                    <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">Configuration:</div>
                    <div className="flex flex-wrap gap-1">
                      {metric.config.requires_ground_truth !== undefined && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          metric.config.requires_ground_truth ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300' : 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300'
                        }`}>
                          {metric.config.requires_ground_truth ? 'Needs Ground Truth' : 'No Ground Truth'}
                        </span>
                      )}
                      {metric.config.opik_metric && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300">
                          Opik: {metric.config.opik_metric}
                        </span>
                      )}
                      {metric.config.model && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
                          {metric.config.model}
                        </span>
                      )}
                      {metric.config.required_fields && metric.config.required_fields.length > 0 && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                          Fields: {metric.config.required_fields.length}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {metrics.length === 0 && !loading && (
        <div className="text-center py-12">
          <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No metrics available</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Evaluation metrics will appear here once they are configured.
          </p>
        </div>
      )}

      {showModal && selectedMetric && (
        <MetricDetailModal
          metric={selectedMetric}
          onClose={() => {
            setShowModal(false);
            setSelectedMetric(null);
          }}
        />
      )}
    </div>
  );
}

function MetricDetailModal({ metric, onClose }) {
  const getUseCases = (category) => {
    const useCases = {
      accuracy: ['Classification performance', 'Clinical diagnosis accuracy'],
      safety: ['Content moderation', 'PII detection', 'Privacy compliance'],
      quality: ['Summarization quality', 'Relevance scoring'],
      performance: ['Latency tracking', 'Throughput monitoring']
    };
    return useCases[category] || ['General evaluation'];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{metric.name}</h2>
            <p className="text-gray-600 dark:text-gray-300 mt-1">{metric.description}</p>
          </div>
          <button onClick={onClose} className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 text-2xl">✕</button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Metric Details</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Category:</span>
                  <span className="font-medium capitalize text-gray-900 dark:text-white">{metric.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Implementation:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{getImplementationTypeLabel(metric.implementation_type)}</span>
                </div>
                {metric.status && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Status:</span>
                    <span className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 px-2 py-1 rounded text-xs font-medium">{metric.status}</span>
                  </div>
                )}
              </div>

              {metric.config && (
                <>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 mt-8">Configuration</h3>
                  <div className="space-y-2 text-sm">
                    {metric.config.requires_ground_truth !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Requires Ground Truth:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{metric.config.requires_ground_truth ? 'Yes' : 'No'}</span>
                      </div>
                    )}
                    {metric.config.opik_metric && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Opik Metric:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{metric.config.opik_metric}</span>
                      </div>
                    )}
                    {metric.config.model && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Model:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{metric.config.model}</span>
                      </div>
                    )}
                    {metric.config.required_fields && metric.config.required_fields.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Required Fields:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{metric.config.required_fields.join(', ')}</span>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Common Use Cases</h3>
              <ul className="space-y-2">
                {getUseCases(metric.category).map((useCase, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 dark:text-green-400 mr-2">✓</span>
                    <span className="text-sm text-gray-700 dark:text-gray-300">{useCase}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button onClick={onClose} className="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 mr-3">Close</button>
          <button className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-lg">Use in Experiment</button>
        </div>
      </div>
    </div>
  );
}

export default EvaluationsLibrary;