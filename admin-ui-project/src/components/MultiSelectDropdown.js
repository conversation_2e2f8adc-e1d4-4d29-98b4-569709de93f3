import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

/**
 * Chip-style multi-select dropdown that visually matches the existing dark-theme <select> controls.
 * Props
 *  - options: [{ id, label }]
 *  - selected: array<string | number>
 *  - onChange(ids[])
 *  - placeholder: string
 */
export default function MultiSelectDropdown({ options, selected, onChange, placeholder = 'Select…', dataCy }) {
  const [open, setOpen] = useState(false);
  const ref = useRef(null);

  // Close on outside click
  useEffect(() => {
    const handler = (e) => {
      if (ref.current && !ref.current.contains(e.target)) setOpen(false);
    };
    document.addEventListener('click', handler);
    return () => document.removeEventListener('click', handler);
  }, []);

  const toggle = (id) => {
    if (selected.includes(id)) {
      onChange(selected.filter((x) => x !== id));
    } else {
      onChange([...selected, id]);
    }
  };

  const selectedLabels = selected
    .map((id) => options.find((o) => o.id === id)?.label || id)
    .filter(Boolean);

  return (
    <div className="relative" ref={ref}>
      {/* Trigger */}
      <button
        data-cy={dataCy}
        type="button"
        className="inline-flex items-center justify-between gap-2 px-3 py-2 min-w-[112px] rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        onClick={() => setOpen((o) => !o)}
      >
        <span className={selectedLabels.length === 0 ? 'text-gray-100 opacity-70' : 'truncate'}>
          {selectedLabels.length === 0
            ? placeholder
            : selectedLabels.slice(0, 2).join(', ') + (selectedLabels.length > 2 ? ` +${selectedLabels.length - 2}` : '')}
        </span>
        <ChevronDown className={`h-4 w-4 transition-transform ${open ? 'rotate-180' : ''}`} />
      </button>

      {/* Menu */}
      {open && (
        <div className="absolute z-20 mt-1 w-56 max-h-60 overflow-y-auto rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-lg">
          {options.map((opt) => {
            const isChecked = selected.includes(opt.id);
            return (
              <div
                key={opt.id}
                className={`flex items-center justify-between px-3 py-2 cursor-pointer text-sm text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 ${
                  isChecked ? 'bg-blue-100 dark:bg-blue-600/20' : ''
                }`}
                onClick={() => toggle(opt.id)}
              >
                <span className="truncate pr-4">{opt.label}</span>
                <input
                  type="checkbox"
                  readOnly
                  checked={isChecked}
                  className="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
              </div>
            );
          })}
          {options.length === 0 && (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">No options</div>
          )}
        </div>
      )}
    </div>
  );
}
