import React, { useState, useMemo } from 'react';
import {
  ShieldExclamationIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  UserIcon,
  IdentificationIcon,
  CreditCardIcon,
  PhoneIcon,
  AtSymbolIcon,
  MapPinIcon,
  CalendarIcon,
  KeyIcon
} from '@heroicons/react/24/outline';

// Icon mapping for PII types
const PII_TYPE_ICONS = {
  'PERSON': UserIcon,
  'EMAIL_ADDRESS': AtSymbolIcon,
  'PHONE_NUMBER': PhoneIcon,
  'CREDIT_CARD': CreditCardIcon,
  'SSN': IdentificationIcon,
  'LOCATION': MapPinIcon,
  'DATE_TIME': CalendarIcon,
  'IP_ADDRESS': KeyIcon,
  'US_SSN': IdentificationIcon,
  'UK_NHS': IdentificationIcon,
  'US_PASSPORT': IdentificationIcon,
  'US_DRIVER_LICENSE': IdentificationIcon
};

// Risk level colors
const getRiskLevelColor = (riskScore) => {
  if (riskScore >= 70) return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400';
  if (riskScore >= 40) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400';
  return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400';
};

const getRiskLevel = (riskScore) => {
  if (riskScore >= 70) return 'High Risk';
  if (riskScore >= 40) return 'Medium Risk';
  return 'Low Risk';
};

function PIIEvaluationResults({ evaluationData }) {
  const [expandedTests, setExpandedTests] = useState({});
  const [selectedTab, setSelectedTab] = useState('overview');

  // Process evaluation data to extract PII findings
  const piiFindings = useMemo(() => {
    if (!evaluationData?.evaluations) return [];

    // Find PII evaluation in the results
    const piiEval = evaluationData.evaluations.find(e => 
      e.evaluator_id === 'presidio_pii' || 
      e.metric_name?.toLowerCase().includes('pii')
    );

    if (!piiEval || !piiEval.results) return [];

    // Process each test result
    return piiEval.results.map((result, idx) => {
      const details = result.details || {};
      return {
        id: idx,
        test_case_id: result.test_case_id || `test_${idx + 1}`,
        input: result.input,
        output: result.output,
        passed: result.passed,
        score: result.score,
        input_pii: details.input_pii || [],
        output_pii: details.output_pii || [],
        total_pii_found: details.total_pii_found || 0,
        pii_types: details.pii_types || [],
        risk_score: details.risk_score || 0,
        summary: details.details || []
      };
    });
  }, [evaluationData]);

  // Calculate overall statistics
  const stats = useMemo(() => {
    const totalTests = piiFindings.length;
    const testsWithPII = piiFindings.filter(f => f.total_pii_found > 0).length;
    const totalPIIFound = piiFindings.reduce((sum, f) => sum + f.total_pii_found, 0);
    const avgRiskScore = totalTests > 0 
      ? piiFindings.reduce((sum, f) => sum + f.risk_score, 0) / totalTests 
      : 0;
    
    // Count PII by type
    const piiByType = {};
    piiFindings.forEach(f => {
      f.pii_types.forEach(type => {
        piiByType[type] = (piiByType[type] || 0) + 1;
      });
    });

    return {
      totalTests,
      testsWithPII,
      totalPIIFound,
      avgRiskScore,
      piiByType,
      passRate: totalTests > 0 ? ((totalTests - testsWithPII) / totalTests * 100).toFixed(1) : 0
    };
  }, [piiFindings]);

  const toggleTestExpansion = (testId) => {
    setExpandedTests(prev => ({
      ...prev,
      [testId]: !prev[testId]
    }));
  };

  const highlightPII = (text, piiEntities) => {
    if (!text || !piiEntities || piiEntities.length === 0) return text;

    // Sort entities by start position
    const sortedEntities = [...piiEntities].sort((a, b) => a.start - b.start);
    
    let result = [];
    let lastEnd = 0;

    sortedEntities.forEach((entity, idx) => {
      // Add text before the PII
      if (entity.start > lastEnd) {
        result.push(text.substring(lastEnd, entity.start));
      }
      
      // Add highlighted PII
      result.push(
        <span
          key={idx}
          className="bg-red-200 dark:bg-red-900/50 text-red-900 dark:text-red-200 px-1 rounded font-semibold"
          title={`${entity.entity_type} (confidence: ${(entity.score * 100).toFixed(0)}%)`}
        >
          {entity.text || text.substring(entity.start, entity.end)}
        </span>
      );
      
      lastEnd = entity.end;
    });

    // Add remaining text
    if (lastEnd < text.length) {
      result.push(text.substring(lastEnd));
    }

    return result;
  };

  return (
    <div className="p-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Safety Score</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {(100 - stats.avgRiskScore).toFixed(0)}%
              </p>
            </div>
            {stats.avgRiskScore < 30 ? (
              <ShieldCheckIcon className="h-8 w-8 text-green-500" />
            ) : (
              <ShieldExclamationIcon className="h-8 w-8 text-red-500" />
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Tests with PII</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.testsWithPII} / {stats.totalTests}
              </p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total PII Found</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {stats.totalPIIFound}
              </p>
            </div>
            <InformationCircleIcon className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Risk Level</p>
              <p className={`text-lg font-bold px-2 py-1 rounded ${getRiskLevelColor(stats.avgRiskScore)}`}>
                {getRiskLevel(stats.avgRiskScore)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* PII Type Distribution */}
      {Object.keys(stats.piiByType).length > 0 && (
        <div className="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">PII Types Detected</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(stats.piiByType).map(([type, count]) => {
              const IconComponent = PII_TYPE_ICONS[type] || InformationCircleIcon;
              return (
                <div
                  key={type}
                  className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg"
                >
                  <IconComponent className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.replace(/_/g, ' ')}
                  </span>
                  <span className="text-xs bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 px-2 py-0.5 rounded-full">
                    {count}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setSelectedTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedTab('details')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'details'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Detailed Findings
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {selectedTab === 'overview' && (
        <div className="space-y-4">
          {piiFindings.map((finding) => (
            <div
              key={finding.id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div
                className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50"
                onClick={() => toggleTestExpansion(finding.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <button className="text-gray-500 dark:text-gray-400">
                      {expandedTests[finding.id] ? (
                        <ChevronDownIcon className="h-5 w-5" />
                      ) : (
                        <ChevronRightIcon className="h-5 w-5" />
                      )}
                    </button>
                    <div>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {finding.test_case_id}
                      </span>
                      {finding.total_pii_found > 0 && (
                        <span className="ml-2 px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400">
                          {finding.total_pii_found} PII found
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskLevelColor(finding.risk_score)}`}>
                      {getRiskLevel(finding.risk_score)}
                    </span>
                    {finding.passed ? (
                      <ShieldCheckIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ShieldExclamationIcon className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                </div>
              </div>

              {expandedTests[finding.id] && (
                <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-700/50">
                  {finding.input_pii.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                        PII in Input ({finding.input_pii.length} entities)
                      </h4>
                      <div className="p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        <pre className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                          {highlightPII(
                            typeof finding.input === 'object' ? JSON.stringify(finding.input, null, 2) : finding.input,
                            finding.input_pii
                          )}
                        </pre>
                      </div>
                    </div>
                  )}

                  {finding.output_pii.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                        PII in Output ({finding.output_pii.length} entities)
                      </h4>
                      <div className="p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        <pre className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                          {highlightPII(finding.output, finding.output_pii)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {finding.total_pii_found === 0 && (
                    <div className="text-center py-4 text-green-600 dark:text-green-400">
                      <ShieldCheckIcon className="h-8 w-8 mx-auto mb-2" />
                      <p className="font-medium">No PII detected in this test case</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {selectedTab === 'details' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Test Case
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  PII Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Detected Text
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Confidence
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {piiFindings.flatMap(finding => [
                ...finding.input_pii.map(pii => ({
                  ...pii,
                  test_case: finding.test_case_id,
                  location: 'Input'
                })),
                ...finding.output_pii.map(pii => ({
                  ...pii,
                  test_case: finding.test_case_id,
                  location: 'Output'
                }))
              ]).map((pii, idx) => {
                const IconComponent = PII_TYPE_ICONS[pii.entity_type] || InformationCircleIcon;
                return (
                  <tr key={idx} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {pii.test_case}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <IconComponent className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {pii.entity_type.replace(/_/g, ' ')}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        pii.location === 'Input' 
                          ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                          : 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
                      }`}>
                        {pii.location}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm">
                      <span className="bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 px-2 py-1 rounded font-mono">
                        {pii.text || '***'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {(pii.score * 100).toFixed(0)}%
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          {piiFindings.every(f => f.total_pii_found === 0) && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No PII detected in any test cases
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default PIIEvaluationResults;