import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, AlertCircle } from 'lucide-react';

const EnumManagement = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [values, setValues] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingValue, setEditingValue] = useState(null);
  const [newValue, setNewValue] = useState({ value: '', display_name: '', description: '', sort_order: 0 });
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      fetchValues(selectedCategory.category_id);
    }
  }, [selectedCategory]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || ''}/api/v1/enums/categories`, {
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      
      const data = await response.json();
      setCategories(data.categories);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchValues = async (categoryId) => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || ''}/api/v1/enums/categories/${categoryId}/values`, {
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch values');
      }
      
      const data = await response.json();
      setValues(data.values);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveValue = async () => {
    try {
      setLoading(true);
      const url = editingValue 
        ? `${process.env.REACT_APP_API_BASE_URL || ''}/api/v1/enums/values/${editingValue.value_id}`
        : `${process.env.REACT_APP_API_BASE_URL || ''}/api/v1/enums/categories/${selectedCategory.category_id}/values`;
      
      const method = editingValue ? 'PUT' : 'POST';
      const body = editingValue 
        ? { ...newValue, is_active: true }
        : newValue;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify(body)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save value');
      }
      
      // Refresh values
      await fetchValues(selectedCategory.category_id);
      setEditingValue(null);
      setNewValue({ value: '', display_name: '', description: '', sort_order: 0 });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteValue = async (valueId) => {
    if (!window.confirm('Are you sure you want to delete this value?')) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || ''}/api/v1/enums/values/${valueId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete value');
      }
      
      // Refresh values
      await fetchValues(selectedCategory.category_id);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditValue = (value) => {
    setEditingValue(value);
    setNewValue({
      value: value.value,
      display_name: value.display_name || '',
      description: value.description || '',
      sort_order: value.sort_order || 0
    });
  };

  const handleCancelEdit = () => {
    setEditingValue(null);
    setNewValue({ value: '', display_name: '', description: '', sort_order: 0 });
  };

  if (loading && categories.length === 0) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Enum Value Management</h2>
        <p className="text-gray-600">Manage enum values for policy types and fields</p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
          <AlertCircle size={16} className="text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Categories Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
            <div className="space-y-2">
              {categories.map(category => (
                <button
                  key={category.category_id}
                  onClick={() => setSelectedCategory(category)}
                  className={`w-full text-left p-3 rounded-lg border transition-colors ${
                    selectedCategory?.category_id === category.category_id
                      ? 'bg-blue-50 border-blue-200 text-blue-900'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium">{category.name}</div>
                  <div className="text-sm text-gray-600">{category.description}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {category.policy_type} → {category.field_path}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Values Panel */}
        <div className="lg:col-span-2">
          {selectedCategory ? (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{selectedCategory.name}</h3>
                  <p className="text-sm text-gray-600">{selectedCategory.description}</p>
                </div>
                <button
                  onClick={() => setEditingValue({})}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Plus size={16} />
                  Add Value
                </button>
              </div>

              {/* Add/Edit Form */}
              {(editingValue !== null) && (
                <div className="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <h4 className="font-medium mb-3">
                    {editingValue.value_id ? 'Edit Value' : 'Add New Value'}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Value *</label>
                      <input
                        type="text"
                        value={newValue.value}
                        onChange={(e) => setNewValue(prev => ({ ...prev, value: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., doctor"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                      <input
                        type="text"
                        value={newValue.display_name}
                        onChange={(e) => setNewValue(prev => ({ ...prev, display_name: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Doctor"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <textarea
                        value={newValue.description}
                        onChange={(e) => setNewValue(prev => ({ ...prev, description: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                        rows={2}
                        placeholder="Description of this value"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                      <input
                        type="number"
                        value={newValue.sort_order}
                        onChange={(e) => setNewValue(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2 mt-4">
                    <button
                      onClick={handleSaveValue}
                      disabled={!newValue.value || loading}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400"
                    >
                      <Save size={16} />
                      Save
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                    >
                      <X size={16} />
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Values List */}
              <div className="space-y-2">
                {values.map(value => (
                  <div key={value.value_id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{value.value}</div>
                      {value.display_name && (
                        <div className="text-sm text-gray-600">{value.display_name}</div>
                      )}
                      {value.description && (
                        <div className="text-sm text-gray-500">{value.description}</div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">Order: {value.sort_order}</span>
                      <button
                        onClick={() => handleEditValue(value)}
                        className="p-1 text-blue-600 hover:text-blue-800"
                        title="Edit"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteValue(value.value_id)}
                        className="p-1 text-red-600 hover:text-red-800"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                ))}
                {values.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No values found for this category
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
              <div className="text-gray-500">Select a category to manage its enum values</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnumManagement; 