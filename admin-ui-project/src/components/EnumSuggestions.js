import React from 'react';
import { getAllEnumFields } from '../utils/schemaUtils';

const EnumSuggestions = ({ policyType }) => {
  if (!policyType) {
    return null;
  }

  const enumFields = getAllEnumFields(policyType);

  if (Object.keys(enumFields).length === 0) {
    return null;
  }

  return (
    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <h4 className="text-sm font-medium text-blue-800 mb-3">
        💡 Available Values for {policyType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Policy
      </h4>
      <div className="space-y-3">
        {Object.entries(enumFields).map(([fieldPath, enumValues]) => (
          <div key={fieldPath} className="text-sm">
            <div className="font-medium text-blue-700 mb-1">
              {fieldPath.split('.').pop().split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}:
            </div>
            <div className="flex flex-wrap gap-1">
              {enumValues.map(value => (
                <span
                  key={value}
                  className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-mono cursor-pointer hover:bg-blue-200"
                  title={`Click to copy "${value}"`}
                  onClick={() => {
                    navigator.clipboard.writeText(`"${value}"`);
                  }}
                >
                  "{value}"
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-3 text-xs text-blue-600">
        💡 Tip: Click on any value above to copy it to clipboard, then paste it in the JSON editor
      </div>
    </div>
  );
};

export default EnumSuggestions; 