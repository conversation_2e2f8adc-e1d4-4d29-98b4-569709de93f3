import React, { useEffect, useMemo, useRef, useState } from 'react';
import { X, Loader2, Plus, Trash2 } from 'lucide-react';
import { getApiBaseUrl } from '../api/getApiBase';
// External Integration - Temporary dev code
import useExternalIntegration from '../hooks/useExternalIntegration';
import ExternalIntegrationDialog from './ExternalIntegrationDialog';

export default function AgentDetailsModal({ agentId, onClose }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [agent, setAgent] = useState(null);
  const [rolePolicies, setRolePolicies] = useState([]); // rows: role_id, role_name, group_id, group_name, policy_id, policy_name

  // dropdown sources
  const [allRoles, setAllRoles] = useState([]);
  const [allGroups, setAllGroups] = useState([]);
  const [allPolicies, setAllPolicies] = useState([]);

  const [newRow, setNewRow] = useState({ roleId: '', groupId: '', policyId: '' });
  const [groupPolicyCache, setGroupPolicyCache] = useState({});
  const [loadingPolicies, setLoadingPolicies] = useState(false);
  const [multiPolicies, setMultiPolicies] = useState([]); // selected policies for bulk add
  const [showPolicyPicker, setShowPolicyPicker] = useState(false);
  const pickerRef = useRef(null);

  // External Integration - Temporary dev code
  const {
    dialogState,
    closeDialog,
    confirmOperation,
    handleAgentPolicyDelete
  } = useExternalIntegration();

  // Prevent background scrolling when modal is open
  useEffect(() => {
    // Save current overflow style
    const originalOverflow = document.body.style.overflow;
    // Prevent scrolling on mount
    document.body.style.overflow = 'hidden';
    
    // Re-enable scrolling on unmount
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  // Close picker on outside click
  useEffect(() => {
    if (!showPolicyPicker) return;
    const handler = (e) => {
      if (pickerRef.current && !pickerRef.current.contains(e.target)) {
        setShowPolicyPicker(false);
      }
    };
    document.addEventListener('mousedown', handler);
    return () => document.removeEventListener('mousedown', handler);
  }, [showPolicyPicker]);

  // Load policies for selected group (cached)
  useEffect(() => {
    const controller = new AbortController();
    async function loadPoliciesForGroup() {
      if (!newRow.groupId || groupPolicyCache[newRow.groupId]) return;
      try {
        setLoadingPolicies(true);
        const res = await fetch(`${getApiBaseUrl()}/api/v1/policy-groups/${newRow.groupId}/policies`, {
          headers: { Authorization: 'Bearer admin-token' },
          signal: controller.signal,
        });
        if (res.ok) {
          const list = await res.json();
          setGroupPolicyCache((prev) => ({ ...prev, [newRow.groupId]: Array.isArray(list) ? list : [] }));
        }
      } catch (_) {
        // ignore; fallback to allPolicies
      } finally {
        setLoadingPolicies(false);
      }
    }
    loadPoliciesForGroup();
    return () => controller.abort();
  }, [newRow.groupId, groupPolicyCache]);

  const filteredPolicies = useMemo(() => {
    if (newRow.groupId && groupPolicyCache[newRow.groupId]) {
      return groupPolicyCache[newRow.groupId];
    }
    return allPolicies;
  }, [newRow.groupId, groupPolicyCache, allPolicies]);

  useEffect(() => {
    const controller = new AbortController();
    async function load() {
      try {
        setLoading(true);
        setError(null);
        const base = getApiBaseUrl();
        const [agentRes, rolePoliciesRes, allRolesRes, allGroupsRes, allPoliciesRes] = await Promise.all([
          fetch(`${base}/api/v1/agents/${agentId}`, { headers: { Authorization: 'Bearer admin-token' }, signal: controller.signal }),
          fetch(`${base}/api/v1/agents/${agentId}/role-policies`, { headers: { Authorization: 'Bearer admin-token' }, signal: controller.signal }),
          // dropdown sources
          fetch(`${base}/api/v1/roles`, { headers: { Authorization: 'Bearer admin-token' }, signal: controller.signal }),
          fetch(`${base}/api/v1/policy-groups?status=active`, { headers: { Authorization: 'Bearer admin-token' }, signal: controller.signal }),
          fetch(`${base}/api/v1/policies?limit=200`, { headers: { Authorization: 'Bearer admin-token' }, signal: controller.signal }),
        ]);
        if (!agentRes.ok) throw new Error('Failed to load agent');
        const agentData = await agentRes.json();
        const rolePoliciesData = rolePoliciesRes.ok ? await rolePoliciesRes.json() : [];
        const allRolesData = allRolesRes.ok ? await allRolesRes.json() : [];
        const allGroupsData = allGroupsRes.ok ? await allGroupsRes.json() : [];
        const allPoliciesJson = allPoliciesRes.ok ? await allPoliciesRes.json() : null;
        const allPoliciesData = Array.isArray(allPoliciesJson?.policies) ? allPoliciesJson.policies : (Array.isArray(allPoliciesJson) ? allPoliciesJson : []);

        setAgent(agentData);
        setRolePolicies(rolePoliciesData);
        setAllRoles(allRolesData);
        setAllGroups(allGroupsData);
        setAllPolicies(allPoliciesData);
      } catch (e) {
        if (e.name !== 'AbortError') setError(e.message);
      } finally {
        setLoading(false);
      }
    }
    load();
    return () => controller.abort();
  }, [agentId]);

  const assignedCounts = useMemo(() => ({
    policies: new Set((rolePolicies || []).map((rp) => rp.policy_id)).size,
    roles: new Set((rolePolicies || []).map((rp) => rp.role_id)).size,
    groups: new Set((rolePolicies || []).map((rp) => rp.group_id)).size,
  }), [rolePolicies]);


  const addAssignment = async () => {
    if (!newRow.roleId && !newRow.groupId && !newRow.policyId) {
      alert('Select a Role or Policy Group or Policy');
      return;
    }
    try {
      const base = getApiBaseUrl();
      const res = await fetch(`${base}/api/v1/agents/${agent.agent_id}/role-policies`, {
        method: 'POST', headers: { 'Content-Type': 'application/json', Authorization: 'Bearer admin-token' }, body: JSON.stringify({ roleId: newRow.roleId, groupId: newRow.groupId, policyId: newRow.policyId })
      });
      if (!res.ok) throw new Error(await res.text());
      // reload
      setNewRow({ roleId: '', groupId: '', policyId: '' });
      const reload = await fetch(`${base}/api/v1/agents/${agent.agent_id}/role-policies`, { headers: { Authorization: 'Bearer admin-token' } });
      setRolePolicies(reload.ok ? await reload.json() : rolePolicies);
    } catch (e) {
      alert(e.message || 'Failed to add assignment');
    }
  };

  // Original remove logic - moved to be called by external integration
  const originalRemoveRow = async (rp) => {
    try {
      const res = await fetch(`${getApiBaseUrl()}/api/v1/agents/${agent.agent_id}/role-policies`, {
        method: 'DELETE', headers: { 'Content-Type': 'application/json', Authorization: 'Bearer admin-token' }, body: JSON.stringify({ roleId: rp.role_id, groupId: rp.group_id, policyId: rp.policy_id })
      });
      if (!res.ok && res.status !== 204) throw new Error(await res.text());
      setRolePolicies((prev) => prev.filter((x) => !(x.role_id === rp.role_id && x.group_id === rp.group_id && x.policy_id === rp.policy_id)));
    } catch (e) {
      alert('Failed to remove row');
    }
  };

  // External Integration wrapper - Shows authorization dialog and curl commands
  const removeRow = (rp) => {
    const getRemainingPolicies = async () => {
      // After deletion, get remaining policy IDs for this agent
      const remaining = rolePolicies.filter(
        x => !(x.role_id === rp.role_id && x.group_id === rp.group_id && x.policy_id === rp.policy_id)
      );
      return remaining.map(x => x.policy_id);
    };

    handleAgentPolicyDelete(
      agent.agent_id,     // agentId
      rp.policy_id,       // policyId  
      agent.name,         // agentName
      rp.policy_name,     // policyName
      async () => {       // onSuccess
        await originalRemoveRow(rp);
      },
      getRemainingPolicies // getRemainingPolicies
    );
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-xl shadow-2xl ring-1 ring-black/10 dark:ring-white/10 w-full max-w-6xl max-h-[85vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex-shrink-0 flex items-start justify-between p-5 border-b dark:border-gray-700">
          <div className="space-y-1 flex-1">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{agent?.name || 'Loading...'}</h2>
            <p className="text-sm text-gray-700 dark:text-gray-300">{agent?.description || 'No description available'}</p>
          </div>
          <button onClick={onClose} className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-700">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Meta row */}
        <div className="flex-shrink-0 px-5 py-3 border-b dark:border-gray-800 bg-white dark:bg-gray-800">
          <div className="flex flex-wrap items-center gap-4 text-sm">
            <span><span className="text-gray-600 dark:text-gray-300">Vendor:</span> <span className="text-gray-900 dark:text-gray-100 font-medium">{agent?.vendor || '—'}</span></span>
            <span><span className="text-gray-600 dark:text-gray-300">Department:</span> <span className="text-gray-900 dark:text-gray-100 font-medium">{agent?.department || '—'}</span></span>
            <span><span className="text-gray-600 dark:text-gray-300">Purpose:</span> <span className="text-gray-900 dark:text-gray-100 font-medium">{agent?.agent_type || '—'}</span></span>
            <span><span className="text-gray-600 dark:text-gray-300">Active:</span> <span className={agent?.is_active ? 'text-green-600 font-medium' : 'text-gray-500'}>{agent?.is_active ? 'Yes' : 'No'}</span></span>
            <span><span className="text-gray-600 dark:text-gray-300">Status:</span> <span className="text-gray-900 dark:text-gray-100 font-medium">{agent?.status || '—'}</span></span>
            <span><span className="text-gray-600 dark:text-gray-300">Assigned:</span> <span className="text-blue-600 font-medium">{assignedCounts.policies} policies via {assignedCounts.roles} roles & {assignedCounts.groups} groups</span></span>
          </div>
        </div>

        {/* Body */}
        <div className="flex-1 p-5 space-y-4 overflow-y-auto overflow-x-hidden">
          {/* Add row */}
          <div className="flex flex-wrap items-end gap-3">
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-300 mb-1">Role</label>
              <select value={newRow.roleId} onChange={(e) => setNewRow({ ...newRow, roleId: e.target.value })} className="min-w-[144px] rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 px-3 py-2 text-sm">
                <option value="">—</option>
                {allRoles.map((r) => <option key={r.role_id} value={r.role_id}>{r.name || r.code}</option>)}
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-300 mb-1">Policy Group</label>
              <select value={newRow.groupId} onChange={(e) => setNewRow({ ...newRow, groupId: e.target.value })} disabled={!newRow.roleId} className={`min-w-[160px] rounded-md border px-3 py-2 text-sm ${!newRow.roleId ? 'cursor-not-allowed opacity-60 border-gray-300 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-500' : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100'}`}>
                <option value="">—</option>
                {allGroups.map((g) => <option key={g.group_id} value={g.group_id}>{g.name}</option>)}
              </select>
            </div>
            <div className="relative" ref={pickerRef}>
              <label className="block text-xs text-gray-600 dark:text-gray-300 mb-1">Policy</label>
              <button
                type="button"
                onClick={() => setShowPolicyPicker((s) => !s)}
                disabled={!newRow.groupId}
                className={`px-3 py-2 rounded-md border text-sm ${!newRow.groupId ? 'cursor-not-allowed opacity-60 border-gray-300 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-500' : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700'}`}
              >
                {showPolicyPicker ? 'Hide policies' : 'Select policies'}
              </button>
              {showPolicyPicker && (
                <div 
                  className="absolute z-50 mt-2 w-72 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm shadow-lg p-2"
                  style={{ 
                    maxHeight: 'min(240px, calc(100vh - 400px))',
                    overflowY: 'auto',
                    overflowX: 'hidden'
                  }}>
                  {/* Select all option */}
                  <label className="flex items-center justify-between gap-2 py-1 px-1 text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                    <span className="truncate">Select all</span>
                    <input
                      type="checkbox"
                      checked={multiPolicies.length > 0 && multiPolicies.length === filteredPolicies.length}
                      onChange={(e) => {
                        setMultiPolicies(e.target.checked ? filteredPolicies.map((p) => p.policy_id) : []);
                      }}
                    />
                  </label>
                  <div className="my-1 border-t border-gray-200 dark:border-gray-700" />
                    {loadingPolicies ? (
                      <div className="text-gray-500">Loading…</div>
                    ) : (
                    filteredPolicies.map((p) => (
                      <label key={p.policy_id} className="flex items-center justify-between gap-2 py-1 px-1 text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded cursor-pointer">
                        <span className="truncate">{p.name}</span>
                        <input
                          type="checkbox"
                          checked={multiPolicies.includes(p.policy_id)}
                          onChange={(e) => {
                            setMultiPolicies((prev) => e.target.checked ? Array.from(new Set([...prev, p.policy_id])) : prev.filter((id) => id !== p.policy_id));
                          }}
                        />
                      </label>
                    ))
                    )}
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={async () => {
                  if (!newRow.roleId || !newRow.groupId) { alert('Select Role and Policy Group first'); return; }
                  if (multiPolicies.length > 0) {
                    try {
                      const res = await fetch(`${getApiBaseUrl()}/api/v1/agents/${agent.agent_id}/role-policies/bulk`, {
                        method: 'POST', headers: { 'Content-Type': 'application/json', Authorization: 'Bearer admin-token' }, body: JSON.stringify({ roleId: newRow.roleId, groupId: newRow.groupId, policyIds: multiPolicies })
                      });
                      if (!res.ok) throw new Error(await res.text());
                      setMultiPolicies([]);
                      const reload = await fetch(`${getApiBaseUrl()}/api/v1/agents/${agent.agent_id}/role-policies`, { headers: { Authorization: 'Bearer admin-token' } });
                      setRolePolicies(reload.ok ? await reload.json() : rolePolicies);
                      setShowPolicyPicker(false);
                    } catch (e) { alert(e.message || 'Failed to add policies'); }
                  } else {
                    alert('Select at least one policy');
                    return;
                  }
                }}
                disabled={multiPolicies.length === 0 || !newRow.groupId || !newRow.roleId}
                className={`inline-flex items-center gap-2 px-3 py-2 rounded-md ${multiPolicies.length === 0 || !newRow.groupId || !newRow.roleId ? 'bg-gray-400 cursor-not-allowed text-white' : 'bg-green-600 hover:bg-green-700 text-white'}`}
              >
                <Plus className="h-4 w-4" /> {multiPolicies.length > 1 ? `Add ${multiPolicies.length} policies` : 'Add policy'}
              </button>
            </div>
          </div>

          {/* Assignments table (role-scoped) */}
          <div className="overflow-x-auto rounded-lg ring-1 ring-gray-200 dark:ring-gray-700 max-h-[50vh] overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900/40">
                <tr>
                  {['Role', 'Policy Group', 'Policy', 'Actions'].map((h) => (
                    <th key={h} className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">{h}</th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {(rolePolicies || []).map((rp) => (
                  <tr key={`rp-${rp.role_id}-${rp.group_id}-${rp.policy_id}`}>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{rp.role_name || rp.role_code}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{rp.group_name}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">{rp.policy_name}</td>
                    <td className="px-4 py-2 text-sm">
                      <button aria-label="Remove" title="Remove" onClick={() => removeRow(rp)} className="text-red-500 hover:text-red-300 inline-flex items-center p-1 rounded">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {loading && (
            <div className="flex items-center justify-center py-8 text-gray-500"><Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading…</div>
          )}
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
        </div>
      </div>

      {/* External Integration Dialog - Temporary dev code */}
      <ExternalIntegrationDialog
        open={dialogState.open}
        onClose={closeDialog}
        onConfirm={confirmOperation}
        title={dialogState.title}
        message={dialogState.message}
        operation={dialogState.operation}
        isLoading={dialogState.isLoading}
        integrationResult={dialogState.integrationResult}
      />
    </div>
  );
}