import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Key, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>Circle, <PERSON><PERSON>s, Eye, EyeOff, Save, RefreshCw, X } from 'lucide-react';

interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  status: 'enabled' | 'disabled' | 'pending';
  category: 'authentication' | 'encryption' | 'access_control' | 'monitoring' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  lastUpdated: string;
  enabled: boolean;
}

interface SecurityConfig {
  twoFactorRequired: boolean;
  passwordMinLength: number;
  passwordComplexity: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  encryptionLevel: 'standard' | 'enhanced' | 'military';
  auditLogging: boolean;
  ipWhitelist: string[];
  ssoEnabled: boolean;
  mfaMethods: string[];
}

const DUMMY_POLICIES: SecurityPolicy[] = [
  {
    id: 'policy_001',
    name: 'Multi-Factor Authentication',
    description: 'Require 2FA for all user accounts',
    status: 'enabled',
    category: 'authentication',
    severity: 'high',
    lastUpdated: '2024-12-15T10:30:00Z',
    enabled: true
  },
  {
    id: 'policy_002',
    name: 'Password Complexity Requirements',
    description: 'Enforce strong password policies',
    status: 'enabled',
    category: 'authentication',
    severity: 'medium',
    lastUpdated: '2024-12-14T15:45:00Z',
    enabled: true
  },
  {
    id: 'policy_003',
    name: 'Data Encryption at Rest',
    description: 'Encrypt all stored data using AES-256',
    status: 'enabled',
    category: 'encryption',
    severity: 'critical',
    lastUpdated: '2024-12-13T09:20:00Z',
    enabled: true
  },
  {
    id: 'policy_004',
    name: 'Session Timeout',
    description: 'Automatically log out inactive users after 30 minutes',
    status: 'enabled',
    category: 'access_control',
    severity: 'medium',
    lastUpdated: '2024-12-12T14:15:00Z',
    enabled: true
  },
  {
    id: 'policy_005',
    name: 'IP Address Restrictions',
    description: 'Restrict access to approved IP addresses',
    status: 'enabled',
    category: 'access_control',
    severity: 'high',
    lastUpdated: '2024-12-11T11:30:00Z',
    enabled: true
  },
  {
    id: 'policy_006',
    name: 'Comprehensive Audit Logging',
    description: 'Log all security events and user actions',
    status: 'enabled',
    category: 'monitoring',
    severity: 'medium',
    lastUpdated: '2024-12-10T16:45:00Z',
    enabled: true
  },
  {
    id: 'policy_007',
    name: 'Single Sign-On Integration',
    description: 'Enable SSO with Active Directory',
    status: 'pending',
    category: 'authentication',
    severity: 'medium',
    lastUpdated: '2024-12-09T13:20:00Z',
    enabled: false
  },
  {
    id: 'policy_008',
    name: 'Real-time Threat Detection',
    description: 'Monitor for suspicious activity patterns',
    status: 'enabled',
    category: 'monitoring',
    severity: 'high',
    lastUpdated: '2024-12-08T10:15:00Z',
    enabled: true
  }
];

const INITIAL_CONFIG: SecurityConfig = {
  twoFactorRequired: true,
  passwordMinLength: 12,
  passwordComplexity: true,
  sessionTimeout: 30,
  maxLoginAttempts: 5,
  encryptionLevel: 'enhanced',
  auditLogging: true,
  ipWhitelist: ['***********/24', '10.0.0.0/8'],
  ssoEnabled: false,
  mfaMethods: ['authenticator_app', 'sms', 'email']
};

export const SecuritySettingsConsole: React.FC = () => {
  const [policies] = useState<SecurityPolicy[]>(DUMMY_POLICIES);
  const [config, setConfig] = useState<SecurityConfig>(INITIAL_CONFIG);
  const [activeTab, setActiveTab] = useState<'overview' | 'policies' | 'authentication' | 'encryption' | 'monitoring'>('overview');
  const [showPolicyModal, setShowPolicyModal] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<SecurityPolicy | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'enabled': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'disabled': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'pending': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'high': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'low': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'authentication': return <Key className="h-4 w-4" />;
      case 'encryption': return <Lock className="h-4 w-4" />;
      case 'access_control': return <Shield className="h-4 w-4" />;
      case 'monitoring': return <AlertTriangle className="h-4 w-4" />;
      case 'compliance': return <CheckCircle className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const handleConfigChange = (key: keyof SecurityConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const enabledPolicies = policies.filter(p => p.enabled).length;
  const totalPolicies = policies.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Security Settings Console</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Configure enterprise security policies and controls</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="text-sm text-gray-600 dark:text-gray-400">Security Score</div>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">92%</div>
          </div>
          <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
            <Save size={20} />
            Save Changes
          </button>
        </div>
      </div>

      {/* Security Overview Cards */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Policies</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{enabledPolicies}/{totalPolicies}</p>
              </div>
              <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">2FA Enabled</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {config.twoFactorRequired ? 'Yes' : 'No'}
                </p>
              </div>
              <Key className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Encryption Level</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize">{config.encryptionLevel}</p>
              </div>
              <Lock className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Audit Logging</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {config.auditLogging ? 'Active' : 'Inactive'}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'policies', label: 'Security Policies' },
            { id: 'authentication', label: 'Authentication' },
            { id: 'encryption', label: 'Encryption' },
            { id: 'monitoring', label: 'Monitoring' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'policies' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Security Policies</h2>
            <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
              Add Policy
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {policies.map(policy => (
              <div key={policy.id} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getCategoryIcon(policy.category)}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{policy.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{policy.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(policy.status)}`}>
                      {policy.status}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(policy.severity)}`}>
                      {policy.severity}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={policy.enabled}
                        onChange={() => {}}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Enabled</span>
                    </label>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedPolicy(policy);
                      setShowPolicyModal(true);
                    }}
                    className="text-primary hover:text-blue-700 transition-colors"
                  >
                    Configure
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'authentication' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Authentication Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Require 2FA for all user accounts</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.twoFactorRequired}
                    onChange={(e) => handleConfigChange('twoFactorRequired', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Minimum Password Length
                  </label>
                  <input
                    type="number"
                    value={config.passwordMinLength}
                    onChange={(e) => handleConfigChange('passwordMinLength', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Session Timeout (minutes)
                  </label>
                  <input
                    type="number"
                    value={config.sessionTimeout}
                    onChange={(e) => handleConfigChange('sessionTimeout', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Password Complexity</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Require uppercase, lowercase, numbers, and symbols</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.passwordComplexity}
                    onChange={(e) => handleConfigChange('passwordComplexity', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Single Sign-On</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Enable SSO integration with Active Directory</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.ssoEnabled}
                    onChange={(e) => handleConfigChange('ssoEnabled', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'encryption' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Encryption Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Encryption Level
                </label>
                <select
                  value={config.encryptionLevel}
                  onChange={(e) => handleConfigChange('encryptionLevel', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                >
                  <option value="standard">Standard (AES-128)</option>
                  <option value="enhanced">Enhanced (AES-256)</option>
                  <option value="military">Military Grade (AES-256 + RSA-4096)</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Data Encryption at Rest</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Encrypt all stored data</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Data Encryption in Transit</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Use TLS 1.3 for all communications</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'monitoring' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Monitoring & Logging</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Audit Logging</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Log all security events and user actions</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.auditLogging}
                    onChange={(e) => handleConfigChange('auditLogging', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Maximum Login Attempts
                </label>
                <input
                  type="number"
                  value={config.maxLoginAttempts}
                  onChange={(e) => handleConfigChange('maxLoginAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  IP Whitelist
                </label>
                <textarea
                  value={config.ipWhitelist.join('\n')}
                  onChange={(e) => handleConfigChange('ipWhitelist', e.target.value.split('\n'))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  placeholder="Enter IP addresses or ranges (one per line)"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Policy Configuration Modal */}
      {showPolicyModal && selectedPolicy && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Configure Policy: {selectedPolicy.name}
              </h2>
              <button 
                onClick={() => setShowPolicyModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <p className="text-gray-900 dark:text-gray-100">{selectedPolicy.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Status
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="enabled">Enabled</option>
                    <option value="disabled">Disabled</option>
                    <option value="pending">Pending</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Severity
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors">
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Enable Policy</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Activate this security policy</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedPolicy.enabled}
                    onChange={() => {}}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>
            </div>

            <div className="mt-6 flex justify-end gap-3">
              <button 
                onClick={() => setShowPolicyModal(false)}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-primary text-white rounded hover:bg-blue-700 transition-colors">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 