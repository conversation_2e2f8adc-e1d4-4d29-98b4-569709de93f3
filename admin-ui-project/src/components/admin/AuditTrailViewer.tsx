import React, { useState, useMemo } from 'react';
import { Search, Filter, Calendar, User, Shield, AlertTriangle, Eye, Download, RefreshCw, X, Clock, Activity, Settings, CheckCircle } from 'lucide-react';

interface AuditEvent {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  category: 'authentication' | 'data_access' | 'system_config' | 'security' | 'user_management' | 'policy_changes';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  ipAddress: string;
  userAgent: string;
  resource: string;
  outcome: 'success' | 'failure' | 'warning';
  sessionId: string;
  department: string;
  location: string;
}

const DUMMY_AUDIT_EVENTS: AuditEvent[] = [
  {
    id: 'audit_001',
    timestamp: '2024-12-15T14:30:00Z',
    userId: 'user_001',
    userName: '<PERSON>',
    action: 'LOGIN_SUCCESS',
    category: 'authentication',
    severity: 'low',
    description: 'User successfully logged in',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    resource: '/auth/login',
    outcome: 'success',
    sessionId: 'sess_123456',
    department: 'IT Administration',
    location: 'Main Campus'
  },
  {
    id: 'audit_002',
    timestamp: '2024-12-15T14:25:00Z',
    userId: 'user_002',
    userName: 'Sarah Chen',
    action: 'POLICY_UPDATE',
    category: 'policy_changes',
    severity: 'high',
    description: 'Updated security policy: Multi-Factor Authentication',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    resource: '/admin/security/policies',
    outcome: 'success',
    sessionId: 'sess_123457',
    department: 'Information Security',
    location: 'Main Campus'
  },
  {
    id: 'audit_003',
    timestamp: '2024-12-15T14:20:00Z',
    userId: 'user_003',
    userName: 'Mike Wilson',
    action: 'DATA_ACCESS',
    category: 'data_access',
    severity: 'medium',
    description: 'Accessed patient records for Emergency Department',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    resource: '/patients/records',
    outcome: 'success',
    sessionId: 'sess_123458',
    department: 'Emergency Medicine',
    location: 'Emergency Wing'
  },
  {
    id: 'audit_004',
    timestamp: '2024-12-15T14:15:00Z',
    userId: 'user_004',
    userName: 'Lisa Garcia',
    action: 'LOGIN_FAILED',
    category: 'authentication',
    severity: 'medium',
    description: 'Failed login attempt - incorrect password',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    resource: '/auth/login',
    outcome: 'failure',
    sessionId: 'sess_123459',
    department: 'Data Analytics',
    location: 'Research Building'
  },
  {
    id: 'audit_005',
    timestamp: '2024-12-15T14:10:00Z',
    userId: 'user_005',
    userName: 'David Kim',
    action: 'SYSTEM_CONFIG_CHANGE',
    category: 'system_config',
    severity: 'high',
    description: 'Modified database connection settings',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    resource: '/admin/system/database',
    outcome: 'success',
    sessionId: 'sess_123460',
    department: 'Quality Assurance',
    location: 'Quality Center'
  },
  {
    id: 'audit_006',
    timestamp: '2024-12-15T14:05:00Z',
    userId: 'user_001',
    userName: 'John Doe',
    action: 'USER_CREATED',
    category: 'user_management',
    severity: 'medium',
    description: 'Created new user account: jessica.lee',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    resource: '/admin/users',
    outcome: 'success',
    sessionId: 'sess_123456',
    department: 'IT Administration',
    location: 'Main Campus'
  },
  {
    id: 'audit_007',
    timestamp: '2024-12-15T14:00:00Z',
    userId: 'user_006',
    userName: 'Emma Rodriguez',
    action: 'SECURITY_ALERT',
    category: 'security',
    severity: 'critical',
    description: 'Multiple failed login attempts detected',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    resource: '/auth/login',
    outcome: 'warning',
    sessionId: 'sess_123461',
    department: 'Internal Audit',
    location: 'Administration Building'
  },
  {
    id: 'audit_008',
    timestamp: '2024-12-15T13:55:00Z',
    userId: 'user_002',
    userName: 'Sarah Chen',
    action: 'POLICY_ENFORCED',
    category: 'policy_changes',
    severity: 'medium',
    description: 'Enforced new password complexity requirements',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    resource: '/admin/security/policies',
    outcome: 'success',
    sessionId: 'sess_123457',
    department: 'Information Security',
    location: 'Main Campus'
  }
];

export const AuditTrailViewer: React.FC = () => {
  const [events] = useState<AuditEvent[]>(DUMMY_AUDIT_EVENTS);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterOutcome, setFilterOutcome] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '2024-12-01',
    end: '2024-12-15'
  });
  const [selectedEvent, setSelectedEvent] = useState<AuditEvent | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);

  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      const matchesSearch = 
        event.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.ipAddress.includes(searchTerm);
      
      const matchesCategory = filterCategory === 'all' || event.category === filterCategory;
      const matchesSeverity = filterSeverity === 'all' || event.severity === filterSeverity;
      const matchesOutcome = filterOutcome === 'all' || event.outcome === filterOutcome;
      
      const eventDate = new Date(event.timestamp);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      const matchesDate = eventDate >= startDate && eventDate <= endDate;
      
      return matchesSearch && matchesCategory && matchesSeverity && matchesOutcome && matchesDate;
    });
  }, [events, searchTerm, filterCategory, filterSeverity, filterOutcome, dateRange]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'high': return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'low': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'success': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'failure': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'warning': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'authentication': return <User className="h-4 w-4" />;
      case 'data_access': return <Eye className="h-4 w-4" />;
      case 'system_config': return <Settings className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'user_management': return <User className="h-4 w-4" />;
      case 'policy_changes': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const exportAuditLog = () => {
    // Simulate export functionality
    setShowExportModal(true);
    setTimeout(() => setShowExportModal(false), 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Audit Trail Viewer</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">View and analyze comprehensive audit logs</p>
        </div>
        <div className="flex items-center gap-4">
          <button 
            onClick={exportAuditLog}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <Download size={20} />
            Export Logs
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div className="relative lg:col-span-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search audit events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Categories</option>
          <option value="authentication">Authentication</option>
          <option value="data_access">Data Access</option>
          <option value="system_config">System Config</option>
          <option value="security">Security</option>
          <option value="user_management">User Management</option>
          <option value="policy_changes">Policy Changes</option>
        </select>

        <select
          value={filterSeverity}
          onChange={(e) => setFilterSeverity(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Severities</option>
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="critical">Critical</option>
        </select>

        <select
          value={filterOutcome}
          onChange={(e) => setFilterOutcome(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
        >
          <option value="all">All Outcomes</option>
          <option value="success">Success</option>
          <option value="failure">Failure</option>
          <option value="warning">Warning</option>
        </select>
      </div>

      {/* Date Range Filter */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Start Date
          </label>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            End Date
          </label>
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
          />
        </div>
      </div>

      {/* Audit Events Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Outcome
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredEvents.map(event => (
                <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {new Date(event.timestamp).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{event.userName}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{event.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{event.action}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{event.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(event.category)}
                      <span className="text-sm text-gray-900 dark:text-gray-100 capitalize">
                        {event.category.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(event.severity)}`}>
                      {event.severity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${getOutcomeColor(event.outcome)}`}>
                      {event.outcome}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {event.ipAddress}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setSelectedEvent(event)}
                      className="text-primary hover:text-blue-700 transition-colors"
                    >
                      <Eye size={16} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Audit Event Details
              </h2>
              <button 
                onClick={() => setSelectedEvent(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Event Information
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock size={16} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {new Date(selectedEvent.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <User size={16} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {selectedEvent.userName} ({selectedEvent.userId})
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Activity size={16} className="text-gray-400" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {selectedEvent.action}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Security Details
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">IP Address:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.ipAddress}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Session ID:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.sessionId}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.location}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Event Classification
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Category:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(selectedEvent.severity)}`}>
                        {selectedEvent.category.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Severity:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(selectedEvent.severity)}`}>
                        {selectedEvent.severity}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Outcome:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getOutcomeColor(selectedEvent.outcome)}`}>
                        {selectedEvent.outcome}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Technical Details
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Resource:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.resource}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Department:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.department}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <p className="text-sm text-gray-900 dark:text-gray-100">{selectedEvent.description}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    User Agent
                  </label>
                  <p className="text-xs text-gray-600 dark:text-gray-400 break-all">{selectedEvent.userAgent}</p>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end gap-3">
              <button 
                onClick={() => setSelectedEvent(null)}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Export Success Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Export Successful</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Audit logs have been exported successfully.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 