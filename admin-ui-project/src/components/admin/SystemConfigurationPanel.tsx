import React, { useState } from 'react';
import { Settings, Database, Bell, Globe, Server, Zap, Save, RefreshCw, X, CheckCircle, AlertTriangle, Mail } from 'lucide-react';

interface SystemConfig {
  platformName: string;
  environment: 'development' | 'staging' | 'production';
  timezone: string;
  language: string;
  dateFormat: string;
  maxFileUploadSize: number;
  sessionTimeout: number;
  maintenanceMode: boolean;
  debugMode: boolean;
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  integrations: {
    slack: boolean;
    teams: boolean;
    email: boolean;
    sms: boolean;
    webhook: boolean;
  };
  databaseSettings: {
    host: string;
    port: number;
    name: string;
    connectionPool: number;
    timeout: number;
  };
  apiSettings: {
    rateLimit: number;
    timeout: number;
    corsEnabled: boolean;
    apiVersion: string;
  };
}

const INITIAL_CONFIG: SystemConfig = {
  platformName: 'Vitea Governance Platform',
  environment: 'production',
  timezone: 'America/New_York',
  language: 'en',
  dateFormat: 'MM/DD/YYYY',
  maxFileUploadSize: 50,
  sessionTimeout: 30,
  maintenanceMode: false,
  debugMode: false,
  autoBackup: true,
  backupFrequency: 'daily',
  emailNotifications: true,
  smsNotifications: false,
  pushNotifications: true,
  integrations: {
    slack: true,
    teams: false,
    email: true,
    sms: false,
    webhook: true
  },
  databaseSettings: {
    host: 'db.viteahealth.com',
    port: 5432,
    name: 'vitea_governance',
    connectionPool: 20,
    timeout: 30
  },
  apiSettings: {
    rateLimit: 1000,
    timeout: 30,
    corsEnabled: true,
    apiVersion: 'v1.0'
  }
};

export const SystemConfigurationPanel: React.FC = () => {
  const [config, setConfig] = useState<SystemConfig>(INITIAL_CONFIG);
  const [activeTab, setActiveTab] = useState<'general' | 'integrations' | 'database' | 'api' | 'notifications'>('general');
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleConfigChange = (key: keyof SystemConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
  };

  const handleIntegrationChange = (integration: keyof SystemConfig['integrations'], value: boolean) => {
    setConfig(prev => ({
      ...prev,
      integrations: { ...prev.integrations, [integration]: value }
    }));
    setHasUnsavedChanges(true);
  };

  const handleDatabaseChange = (key: keyof SystemConfig['databaseSettings'], value: any) => {
    setConfig(prev => ({
      ...prev,
      databaseSettings: { ...prev.databaseSettings, [key]: value }
    }));
    setHasUnsavedChanges(true);
  };

  const handleApiChange = (key: keyof SystemConfig['apiSettings'], value: any) => {
    setConfig(prev => ({
      ...prev,
      apiSettings: { ...prev.apiSettings, [key]: value }
    }));
    setHasUnsavedChanges(true);
  };

  const handleSave = () => {
    setHasUnsavedChanges(false);
    setShowSaveModal(true);
    setTimeout(() => setShowSaveModal(false), 2000);
  };

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'production': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'staging': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'development': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      default: return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">System Configuration</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Configure platform-wide settings and preferences</p>
        </div>
        <div className="flex items-center gap-4">
          {hasUnsavedChanges && (
            <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
              <AlertTriangle size={16} />
              <span className="text-sm">Unsaved changes</span>
            </div>
          )}
          <button 
            onClick={handleSave}
            disabled={!hasUnsavedChanges}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save size={20} />
            Save Changes
          </button>
        </div>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Environment</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 capitalize">{config.environment}</p>
            </div>
            <Server className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Database Status</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">Connected</p>
            </div>
            <Database className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">API Status</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">Healthy</p>
            </div>
            <Zap className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Maintenance Mode</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {config.maintenanceMode ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <Settings className="h-8 w-8 text-gray-600 dark:text-gray-400" />
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'general', label: 'General Settings' },
            { id: 'integrations', label: 'Integrations' },
            { id: 'database', label: 'Database' },
            { id: 'api', label: 'API Settings' },
            { id: 'notifications', label: 'Notifications' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'general' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">General Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Platform Name
                </label>
                <input
                  type="text"
                  value={config.platformName}
                  onChange={(e) => handleConfigChange('platformName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Environment
                </label>
                <select
                  value={config.environment}
                  onChange={(e) => handleConfigChange('environment', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                >
                  <option value="development">Development</option>
                  <option value="staging">Staging</option>
                  <option value="production">Production</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Timezone
                </label>
                <select
                  value={config.timezone}
                  onChange={(e) => handleConfigChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                >
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="UTC">UTC</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Language
                </label>
                <select
                  value={config.language}
                  onChange={(e) => handleConfigChange('language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max File Upload Size (MB)
                </label>
                <input
                  type="number"
                  value={config.maxFileUploadSize}
                  onChange={(e) => handleConfigChange('maxFileUploadSize', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Session Timeout (minutes)
                </label>
                <input
                  type="number"
                  value={config.sessionTimeout}
                  onChange={(e) => handleConfigChange('sessionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Maintenance Mode</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Enable maintenance mode to restrict access</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.maintenanceMode}
                    onChange={(e) => handleConfigChange('maintenanceMode', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Debug Mode</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Enable detailed logging and debugging</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.debugMode}
                    onChange={(e) => handleConfigChange('debugMode', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Auto Backup</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Automatically backup system data</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.autoBackup}
                    onChange={(e) => handleConfigChange('autoBackup', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              {config.autoBackup && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Backup Frequency
                  </label>
                  <select
                    value={config.backupFrequency}
                    onChange={(e) => handleConfigChange('backupFrequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'integrations' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Integrations</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Slack Integration</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Send notifications to Slack channels</p>
                  </div>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.integrations.slack}
                    onChange={(e) => handleIntegrationChange('slack', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <Bell className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Microsoft Teams</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Send notifications to Teams channels</p>
                  </div>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.integrations.teams}
                    onChange={(e) => handleIntegrationChange('teams', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <Mail className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Email Integration</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Send email notifications</p>
                  </div>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.integrations.email}
                    onChange={(e) => handleIntegrationChange('email', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                    <Bell className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">SMS Integration</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Send SMS notifications</p>
                  </div>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.integrations.sms}
                    onChange={(e) => handleIntegrationChange('sms', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                    <Zap className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Webhook Integration</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Send webhook notifications</p>
                  </div>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.integrations.webhook}
                    onChange={(e) => handleIntegrationChange('webhook', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'database' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Database Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Database Host
                </label>
                <input
                  type="text"
                  value={config.databaseSettings.host}
                  onChange={(e) => handleDatabaseChange('host', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Database Port
                </label>
                <input
                  type="number"
                  value={config.databaseSettings.port}
                  onChange={(e) => handleDatabaseChange('port', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Database Name
                </label>
                <input
                  type="text"
                  value={config.databaseSettings.name}
                  onChange={(e) => handleDatabaseChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Connection Pool Size
                </label>
                <input
                  type="number"
                  value={config.databaseSettings.connectionPool}
                  onChange={(e) => handleDatabaseChange('connectionPool', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Connection Timeout (seconds)
                </label>
                <input
                  type="number"
                  value={config.databaseSettings.timeout}
                  onChange={(e) => handleDatabaseChange('timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'api' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">API Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Rate Limit (requests per minute)
                </label>
                <input
                  type="number"
                  value={config.apiSettings.rateLimit}
                  onChange={(e) => handleApiChange('rateLimit', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Timeout (seconds)
                </label>
                <input
                  type="number"
                  value={config.apiSettings.timeout}
                  onChange={(e) => handleApiChange('timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Version
                </label>
                <input
                  type="text"
                  value={config.apiSettings.apiVersion}
                  onChange={(e) => handleApiChange('apiVersion', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary transition-colors"
                />
              </div>
            </div>

            <div className="mt-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">CORS Enabled</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Allow cross-origin requests</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.apiSettings.corsEnabled}
                    onChange={(e) => handleApiChange('corsEnabled', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'notifications' && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Notification Settings</h2>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Email Notifications</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Send notifications via email</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.emailNotifications}
                    onChange={(e) => handleConfigChange('emailNotifications', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">SMS Notifications</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Send notifications via SMS</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.smsNotifications}
                    onChange={(e) => handleConfigChange('smsNotifications', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Push Notifications</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Send push notifications to users</p>
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.pushNotifications}
                    onChange={(e) => handleConfigChange('pushNotifications', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Save Success Modal */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Settings Saved</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Your configuration changes have been saved successfully.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 