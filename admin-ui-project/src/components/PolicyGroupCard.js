import React from 'react';
import SeverityPill from './SeverityPill.js';
import StatusPill from './StatusPill.js';

export default function PolicyGroupCard({ group, onEdit, onToggleStatus, onViewPolicies }) {
  const dim = group.status === 'deprecated';
  
  const handleCardClick = (e) => {
    // Don't trigger if clicking on action buttons
    if (e.target.closest('.action-buttons')) return;
    if (onViewPolicies) onViewPolicies(group);
  };

  return (
    <div 
      className={`p-4 rounded-lg shadow relative transition-all duration-200 ${
        dim ? 'bg-gray-700 opacity-60' : 'bg-gray-800 hover:bg-gray-750'
      } ${onViewPolicies ? 'cursor-pointer hover:shadow-lg' : ''}`}
      onClick={handleCardClick}
    >
      {/* actions */}
      <div className="action-buttons absolute top-1.5 right-1.5 space-x-1.5 text-xs">
        {onEdit && (
          <button
            className="text-blue-300 hover:text-blue-100"
            title="Edit"
            onClick={() => onEdit(group)}
          >
            Edit
          </button>
        )}
        {onToggleStatus && (
          <button
            className="text-red-300 hover:text-red-100"
            title={group.status === 'active' ? 'Deprecate' : 'Restore'}
            onClick={() => onToggleStatus(group)}
          >
            {group.status === 'active' ? 'Deprecate' : 'Restore'}
          </button>
        )}
      </div>
      <h2 className="text-lg font-semibold text-white mb-1">{group.name}</h2>
      {group.is_template && (
        <span className="inline-block bg-blue-800 text-blue-100 text-xs px-1.5 py-0.5 rounded-full mr-1.5">Template</span>
      )}
      <div className="space-x-1 mb-1.5 inline-flex">
        <SeverityPill level={group.severity} />
        <StatusPill status={group.status} />
      </div>
      <p className="text-sm text-gray-300 mb-2.5 line-clamp-3 min-h-[2.5rem]">{group.description || 'No description'}</p>
      <div className="text-xs text-gray-400 space-x-3 mb-2.5">
        <span>v{group.version}</span>
        <span>{group.uses_count ?? 0} uses</span>
        {group.agents_count !== undefined && <span>{group.agents_count} agents</span>}
      </div>
      {/* tags */}
      <div className="flex flex-wrap gap-0.5 mb-1.5">
        {(group.tags || []).slice(0, 4).map((tag) => (
          <span key={tag} className="bg-gray-700 text-gray-200 text-[10px] px-1.5 py-0.5 rounded-full">{tag}</span>
        ))}
        {group.tags && group.tags.length > 4 && (
          <span className="bg-gray-700 text-gray-200 text-[10px] px-1.5 py-0.5 rounded-full">+{group.tags.length - 4}</span>
        )}
      </div>
      {group.created_by_name && (
        <p className="text-xs text-gray-500 mt-1.5">By {group.created_by_name}</p>
      )}
    </div>
  );
}
