// External Integration Service - Development Mode
// Temporary throw-away code for rapid testing and debugging

import { externalSystemConfig, generateCurlCommand, logIntegrationCall } from '../config/externalIntegration';

class ExternalIntegrationService {
  
  /**
   * Update policy status globally in external system
   * @param {string} policyId - UUID of the policy
   * @param {boolean} isActive - New active status
   * @returns {Promise<object>} - Result with curl command and response
   */
  async updatePolicyGlobally(policyId, isActive) {
    const url = `${externalSystemConfig.baseUrl}${externalSystemConfig.endpoints.updatePolicy}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${externalSystemConfig.token}`
    };
    
    const payload = {
      policy_id: policyId,
      is_active: isActive
    };
    
    const curlCommand = generateCurlCommand(url, 'PUT', headers, payload);
    
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify(payload)
      });
      
      const responseData = await response.json();
      
      logIntegrationCall('Global Policy Update', curlCommand, {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      });
      
      return {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        data: responseData,
        curlCommand,
        payload
      };
      
    } catch (error) {
      logIntegrationCall('Global Policy Update', curlCommand, null, error);
      
      return {
        success: false,
        error: error.message,
        curlCommand,
        payload
      };
    }
  }
  
  /**
   * Update agent policy assignments in external system
   * @param {string} agentId - UUID of the agent
   * @param {boolean} agentActive - Agent active status
   * @param {Array<string>} policyIds - Array of policy UUIDs
   * @returns {Promise<object>} - Result with curl command and response
   */
  async updateAgentPolicies(agentId, agentActive, policyIds = []) {
    const url = `${externalSystemConfig.baseUrl}${externalSystemConfig.endpoints.updateAgentPolicy}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${externalSystemConfig.token}`
    };
    
    const payload = {
      agent_id: agentId,
      agent_active: agentActive,
      policy_ids: agentActive ? policyIds : []
    };
    
    const curlCommand = generateCurlCommand(url, 'PUT', headers, payload);
    
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify(payload)
      });
      
      const responseData = await response.json();
      
      logIntegrationCall('Agent Policy Update', curlCommand, {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      });
      
      return {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        data: responseData,
        curlCommand,
        payload
      };
      
    } catch (error) {
      logIntegrationCall('Agent Policy Update', curlCommand, null, error);
      
      return {
        success: false,
        error: error.message,
        curlCommand,
        payload
      };
    }
  }
  
  /**
   * Handle agent policy deletion - updates external system with remaining policies
   * @param {string} agentId - UUID of the agent
   * @param {Array<string>} remainingPolicyIds - Policy IDs that remain after deletion
   * @returns {Promise<object>} - Result with curl command and response
   */
  async handleAgentPolicyDeletion(agentId, remainingPolicyIds) {
    return this.updateAgentPolicies(agentId, true, remainingPolicyIds);
  }
  
  /**
   * Handle agent activation/deactivation
   * @param {string} agentId - UUID of the agent
   * @param {boolean} isActive - New agent active status
   * @param {Array<string>} assignedPolicyIds - All policies assigned to agent
   * @returns {Promise<object>} - Result with curl command and response
   */
  async handleAgentToggle(agentId, isActive, assignedPolicyIds) {
    return this.updateAgentPolicies(agentId, isActive, assignedPolicyIds);
  }
}

// Export singleton instance
export default new ExternalIntegrationService();