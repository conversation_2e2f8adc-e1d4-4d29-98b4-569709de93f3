import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { policyGroupsApi } from '../api/policyGroupsApi.js';
import { rolesApi } from '../api/rolesApi.js';
import { agentsApi } from '../api/agentsApi.js';
import { assignmentsApi } from '../api/assignmentsApi.js';

export const store = configureStore({
  reducer: {
    [policyGroupsApi.reducerPath]: policyGroupsApi.reducer,
    [rolesApi.reducerPath]: rolesApi.reducer,
    [agentsApi.reducerPath]: agentsApi.reducer,
    [assignmentsApi.reducerPath]: assignmentsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(policyGroupsApi.middleware, rolesApi.middleware, agentsApi.middleware, assignmentsApi.middleware),
});

setupListeners(store.dispatch);