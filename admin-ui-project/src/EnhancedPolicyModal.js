import React, { useState, useEffect } from 'react';
import { <PERSON>, Co<PERSON>, Search, Loader2, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { 
  validatePolicyDefinition, 
  generateDefaultPolicy,
  getSchemaForPolicyType
} from './utils/schemaUtils';
import { getSchemaNames, clearSchemaCache } from './utils/schemaApi';
import JsonEditorWithAutocomplete from './components/JsonEditorWithAutocomplete';
import EnumSuggestions from './components/EnumSuggestions';
import JsonStructureGuide from './components/JsonStructureGuide';
import MultiSelectDropdown from './components/MultiSelectDropdown';
import { listPolicyGroups, createPolicyGroup } from './api/policyGroups';

// Get API base URL safely - use nginx routing
const getApiBaseUrl = () => {
  // Use relative URLs to allow nginx to handle routing
  return `${window.location.protocol}//${window.location.host}`;
};

const EnhancedPolicyModal = ({ onClose, onSave, existingPolicy = null }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    policyTemplate: '',
    definition: '{\n  \n}'
  });
  
  const [isCloning, setIsCloning] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showPolicySelector, setShowPolicySelector] = useState(false);
  const [availablePolicies, setAvailablePolicies] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingTemplate, setLoadingTemplate] = useState(false);
  const [errors, setErrors] = useState({});
  const [groups, setGroups] = useState([]);
  const [linksLoaded, setLinksLoaded] = useState(false);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [jsonError, setJsonError] = useState(null);
  const [availableSchemas, setAvailableSchemas] = useState([]);

  // Helper function to format schema names for display
  const formatSchemaName = (schemaName) => {
    const schemaDisplayMap = {
      'medical_privacy': 'Medical Privacy Policy',
      'data_privacy': 'Data Privacy Policy', 
      'access_control': 'Access Control Policy',
      'compliance': 'Compliance Policy'
    };
    
    // Return mapped name or convert snake_case to Title Case
    return schemaDisplayMap[schemaName] || 
           schemaName.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Prevent background scrolling when modal is open
  useEffect(() => {
    // Save current overflow style
    const originalOverflow = document.body.style.overflow;
    // Prevent scrolling on mount
    document.body.style.overflow = 'hidden';
    
    // Re-enable scrolling on unmount
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, []);

  // Real-time JSON validation including schema validation
  useEffect(() => {
    const validateJsonDefinition = async () => {
      if (formData.definition && formData.policyTemplate) {
        try {
          const parsedDefinition = JSON.parse(formData.definition);
          setJsonError(null);
          
          // Run schema-driven validation in real-time
          try {
            const schema = await getSchemaForPolicyType(formData.policyTemplate);
            if (schema && schema.required && Array.isArray(schema.required)) {
              const missingRequired = schema.required.filter(field => 
                !parsedDefinition.hasOwnProperty(field) || 
                parsedDefinition[field] === null || 
                parsedDefinition[field] === undefined || 
                (typeof parsedDefinition[field] === 'string' && parsedDefinition[field].trim() === '')
              );
              
              if (missingRequired.length > 0) {
                setJsonError(`Missing required fields: ${missingRequired.join(', ')}`);
              }
            }
          } catch (schemaError) {
            console.error('Error checking schema validation:', schemaError);
          }
          
        } catch (error) {
          setJsonError(error.message);
        }
      }
    };
    
    validateJsonDefinition();
  }, [formData.definition, formData.policyTemplate]);

  // Load available policies for cloning
  useEffect(() => {
    if (showPolicySelector) {
      fetchAvailablePolicies();
    }
  }, [showPolicySelector]);

  // Load available schemas
  useEffect(() => {
    async function loadAvailableSchemas() {
      try {
        const schemaNames = await getSchemaNames();
        setAvailableSchemas(schemaNames);
      } catch (error) {
        console.error('Failed to load schemas:', error);
        // Fallback to hardcoded schemas
        setAvailableSchemas(['medical_privacy', 'data_privacy', 'access_control', 'compliance']);
      }
    }
    loadAvailableSchemas();
  }, []);

  // Load all policy groups and, if editing, pre-select linked ones
  useEffect(() => {
    async function loadGroupsAndSelection() {
      try {
        setLinksLoaded(false);
        const allGroups = await listPolicyGroups('active');
        setGroups(allGroups);

        // For new policies, start with no groups selected
        if (!existingPolicy) {
          setSelectedGroups([]);
        }

        if (existingPolicy?.policy_id) {
          const res = await fetch(`${getApiBaseUrl()}/api/v1/policy-groups/policy/${existingPolicy.policy_id}`, {
            headers: { Authorization: 'admin' }
          });
          if (res.ok) {
            const linked = await res.json();
            const linkedIds = linked.map(g => g.group_id);
            setSelectedGroups(prev => (prev.length ? prev : linkedIds));
          }
        }

        // For new policy creation or after linked groups loaded, mark ready
        setLinksLoaded(true);
      } catch (err) {
        console.error('Failed to load policy groups', err);
      }
    }
    loadGroupsAndSelection();
  }, [existingPolicy]);

  // Initialize form with existing policy data if editing or cloning
  useEffect(() => {
    if (existingPolicy) {
      console.log('Loading existing policy:', existingPolicy);
      console.log('Policy definition:', existingPolicy.definition);
      console.log('Policy definition type:', typeof existingPolicy.definition);
      
      // Check if this is an edit operation (policy has policy_id) or clone operation
      if (existingPolicy.policy_id) {
        // This is an edit operation
        setIsEditing(true);
        setIsCloning(false);
        
        // Handle the definition field properly
        let definitionString = '{\n  \n}';
        if (existingPolicy.definition) {
          try {
            // If it's already a string, parse it first
            const definitionObj = typeof existingPolicy.definition === 'string' 
              ? JSON.parse(existingPolicy.definition) 
              : existingPolicy.definition;
            
            definitionString = JSON.stringify(definitionObj, null, 2);
            console.log('Parsed definition string:', definitionString);
          } catch (error) {
            console.error('Error parsing policy definition:', error);
            definitionString = '{\n  \n}';
          }
        } else {
          console.warn('Policy definition is empty or null');
        }
        
        setFormData({
          name: existingPolicy.name,
          description: existingPolicy.description || '',
          policyTemplate: existingPolicy.category,
          definition: definitionString
        });
      } else {
        // This is a clone operation
        setIsEditing(false);
        setIsCloning(true);
        
        // Handle the definition field properly for cloning
        let definitionString = '{\n  \n}';
        if (existingPolicy.definition) {
          try {
            const definitionObj = typeof existingPolicy.definition === 'string' 
              ? JSON.parse(existingPolicy.definition) 
              : existingPolicy.definition;
            
            definitionString = JSON.stringify(definitionObj, null, 2);
          } catch (error) {
            console.error('Error parsing policy definition for cloning:', error);
            definitionString = '{\n  \n}';
          }
        }
        
        setFormData({
          name: `${existingPolicy.name} (Copy)`,
          description: existingPolicy.description || '',
          policyTemplate: existingPolicy.category,
          definition: definitionString
        });
      }
    }
  }, [existingPolicy]);

  const fetchAvailablePolicies = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/policies`, {
        headers: {
          'Authorization': 'admin'
        }
      });
      const data = await response.json();
      
      console.log('API Response:', data);
      console.log('Policies array:', data.policies);
      console.log('Policies length:', data.policies?.length);
      
      if (Array.isArray(data.policies)) {
        // Filter out any null/undefined policies and log the result
        const validPolicies = data.policies.filter(policy => policy != null);
        console.log('Valid policies:', validPolicies);
        console.log('Valid policies length:', validPolicies.length);
        setAvailablePolicies(validPolicies);
      } else {
        console.error('Unexpected response format:', data);
        setAvailablePolicies([]);
      }
    } catch (error) {
      console.error('Error fetching policies:', error);
      setAvailablePolicies([]);
    }
  };

  const handleTemplateChange = async (template) => {
    setFormData(prev => ({ ...prev, policyTemplate: template }));
    
    if (template && !isEditing) {
      setLoadingTemplate(true);
      try {
        // Generate default policy based on template (now async)
        const defaultPolicy = await generateDefaultPolicy(template);
        setFormData(prev => ({ 
          ...prev, 
          policyTemplate: template,
          definition: JSON.stringify(defaultPolicy, null, 2)
        }));
      } catch (error) {
        console.error('Error generating default policy:', error);
      } finally {
        setLoadingTemplate(false);
      }
    }
  };

  const validateForm = async () => {
    console.log('=== validateForm called ===');
    const newErrors = {};
    
    // Validate required form fields
    if (!formData.name.trim()) {
      newErrors.name = 'Policy name is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.policyTemplate) {
      newErrors.policyTemplate = 'Policy template is required';
    }
    if (!selectedGroups || selectedGroups.length === 0) {
      newErrors.groups = 'Policy group selection is required';
    }
    
    // Validate JSON definition
    if (!formData.definition.trim()) {
      newErrors.definition = 'Policy definition is required';
    } else {
      try {
        const parsedDefinition = JSON.parse(formData.definition);
        
        // Validate against schema - now async
        const validationResult = await validatePolicyDefinition(parsedDefinition, formData.policyTemplate);
        if (!validationResult.isValid) {
          const errorMessages = validationResult.errors || [];
          newErrors.definition = `Schema validation failed: ${errorMessages.join(', ')}`;
        }
        
        // Schema-driven validation for required fields
        try {
          console.log('Validating with policyTemplate:', formData.policyTemplate);
          const schema = await getSchemaForPolicyType(formData.policyTemplate);
          console.log('Retrieved schema:', schema);
          
          if (schema && schema.required && Array.isArray(schema.required)) {
            console.log('Schema required fields:', schema.required);
            console.log('Policy definition keys:', Object.keys(parsedDefinition));
            
            const missingRequired = schema.required.filter(field => 
              !parsedDefinition.hasOwnProperty(field) || 
              parsedDefinition[field] === null || 
              parsedDefinition[field] === undefined || 
              (typeof parsedDefinition[field] === 'string' && parsedDefinition[field].trim() === '')
            );
            
            console.log('Missing required fields:', missingRequired);
            
            if (missingRequired.length > 0) {
              newErrors.definition = `Policy definition is missing required fields: ${missingRequired.join(', ')}`;
            }
          } else {
            console.log('No schema found or no required fields');
          }
        } catch (error) {
          console.error('Error checking required fields:', error);
          // Fallback to basic validation if schema loading fails
          if (!parsedDefinition.type) {
            newErrors.definition = 'Policy definition must include a "type" field';
          }
        }
        
      } catch (error) {
        newErrors.definition = `Invalid JSON format: ${error.message}`;
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    console.log('=== handleSave called ===');
    const validationResult = await validateForm();
    console.log('=== validation result:', validationResult, '===');
    if (!validationResult) {
      console.log('=== validation failed, returning ===');
      return;
    }
    console.log('=== validation passed, proceeding with save ===');

    setLoading(true);
    try {
      const parsedDefinition = JSON.parse(formData.definition);
      
      // Extract severity from JSON definition for backward compatibility
      const severity = parsedDefinition.severity || 'medium';
      
      // Use selected groups directly
      let finalGroupIds = selectedGroups;

      const policyData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        category: formData.policyTemplate,
        severity: severity, // Use severity from JSON
        policy_type: 'opa', // Always OPA for now
        definition: parsedDefinition,
        applies_to_roles: parsedDefinition.allowed_roles || [],
        is_active: parsedDefinition.enabled !== false, // Default to true unless explicitly set to false
        groupIds: finalGroupIds
      };

      // Debug logging
      console.log('Saving policy with data:', {
        parsedDefinition,
        enabled: parsedDefinition.enabled,
        is_active: parsedDefinition.enabled !== false,
        policyData
      });

      if (isEditing && existingPolicy) {
        // Update existing policy
        console.log('Sending PUT request to:', `/api/v1/policies/${existingPolicy.policy_id}`);
        console.log('Request body:', policyData);
        
        const response = await fetch(`${getApiBaseUrl()}/api/v1/policies/${existingPolicy.policy_id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'admin'
          },
          body: JSON.stringify(policyData),
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`Failed to update policy: ${response.statusText} - ${errorText}`);
        }
        
        const updatedPolicy = await response.json();
        console.log('Updated policy:', updatedPolicy);
        onSave(updatedPolicy);
      } else {
        // Create new policy
        const response = await fetch(`${getApiBaseUrl()}/api/v1/policies`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'admin'
          },
          body: JSON.stringify(policyData),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          
          // Handle specific error cases
          if (response.status === 409) {
            // Duplicate name - show user-friendly message
            throw new Error('A policy with this name already exists. Please choose a different name.');
          } else {
            // Other errors - show the actual error message
            throw new Error(`Failed to create policy: ${response.statusText} - ${errorText}`);
          }
        }
        
        const newPolicy = await response.json();
        onSave(newPolicy);
      }
    } catch (error) {
      console.error('Error saving policy:', error);
      setErrors({ general: error.message });
    } finally {
      setLoading(false);
    }
  };

  const handlePolicySelect = (policy) => {
    setFormData({
      name: `${policy.name} (Copy)`,
      description: policy.description || '',
      policyTemplate: policy.category,
      definition: typeof policy.definition === 'string' 
        ? policy.definition 
        : JSON.stringify(policy.definition, null, 2)
    });
    setShowPolicySelector(false);
    setIsCloning(true);
    setIsEditing(false);
  };

  const filteredPolicies = availablePolicies.filter(policy =>
    policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (policy.description && policy.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Debug logging for policy filtering
  console.log('Available policies:', availablePolicies);
  console.log('Search term:', searchTerm);
  console.log('Filtered policies:', filteredPolicies);

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-xl shadow-2xl ring-1 ring-black/10 dark:ring-white/10 w-full max-w-6xl max-h-[90vh] flex flex-col overflow-hidden">
        <div className="flex-shrink-0 flex items-center justify-between p-6 border-b dark:border-gray-800">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {isEditing ? 'Edit Policy' : isCloning ? 'Clone Policy' : 'Create New Policy'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-200"
          >
            <X size={24} />
          </button>
        </div>

        <div className="flex-1 p-6 space-y-6 overflow-y-auto overflow-x-hidden">
          {/* Policy Selection for Cloning */}
          {!isEditing && (
            <div className="border border-gray-200 dark:border-gray-800 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Clone Existing Policy</h3>
                <button
                  onClick={() => setShowPolicySelector(!showPolicySelector)}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  {showPolicySelector ? 'Cancel' : 'Select Policy to Clone'}
                </button>
              </div>
              
              {showPolicySelector && (
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="Search policies..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"
                    />
                  </div>
                  
                  {filteredPolicies.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No policies found
                    </div>
                  ) : (
                    <div className="grid gap-3 max-h-60 overflow-y-auto">
                      {filteredPolicies.map(policy => (
                        <div
                          key={policy.policy_id}
                          onClick={() => handlePolicySelect(policy)}
                          className="p-3 border rounded-lg hover:bg-blue-50 dark:hover:bg-gray-800 cursor-pointer border-gray-200 dark:border-gray-700"
                        >
                          <div className="font-medium text-gray-900 dark:text-gray-100">{policy.name}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">{policy.description}</div>
                          <div className="flex gap-2 mt-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              policy.severity === 'critical' ? 'bg-red-100 text-red-800' :
                              policy.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                              policy.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {policy.severity}
                            </span>
                            <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 rounded-full">
                              {policy.category}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Policy Form */}
          <div className="space-y-4">
            {/* Row 1: Name + Description side by side */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Policy Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, name: e.target.value }));
                    // Real-time validation
                    const newErrors = { ...errors };
                    if (!e.target.value.trim()) {
                      newErrors.name = 'Policy name is required';
                    } else {
                      delete newErrors.name;
                    }
                    setErrors(newErrors);
                  }}
                  placeholder="Policy Name (e.g., 'Block PII in responses')"
                  className={`w-full p-2 border rounded bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 ${errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-700'}`}
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, description: e.target.value }));
                    // Real-time validation
                    const newErrors = { ...errors };
                    if (!e.target.value.trim()) {
                      newErrors.description = 'Description is required';
                    } else {
                      delete newErrors.description;
                    }
                    setErrors(newErrors);
                  }}
                  placeholder="A brief description of the policy's purpose and effect."
                  rows={2}
                  className={`w-full p-2 border rounded bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 ${errors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-700'}`}
                />
                {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
              </div>
            </div>

            {/* Row 2: Policy Template + Groups */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Policy Template *
                </label>
                <select
                  value={formData.policyTemplate}
                  onChange={(e) => {
                    handleTemplateChange(e.target.value);
                    // Real-time validation
                    const newErrors = { ...errors };
                    if (!e.target.value) {
                      newErrors.policyTemplate = 'Policy template is required';
                    } else {
                      delete newErrors.policyTemplate;
                    }
                    setErrors(newErrors);
                  }}
                  className={`w-full p-2 border rounded bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 ${errors.policyTemplate ? 'border-red-500' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <option value="">Select a policy template...</option>
                  {availableSchemas.map(schema => (
                    <option key={schema} value={schema}>
                      {formatSchemaName(schema)}
                    </option>
                  ))}
                </select>
                {errors.policyTemplate && <p className="text-red-500 text-sm mt-1">{errors.policyTemplate}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Policy Groups *
                  <span className="inline-block ml-2 align-middle text-gray-400" title="Select a policy group to proceed">
                    <Info size={14} />
                  </span>
                </label>
                <MultiSelectDropdown
                  options={groups.map(g => ({ id: g.group_id || g.policy_group_id || g.id, label: g.name }))}
                  selected={selectedGroups}
                  onChange={(newSelection) => {
                    setSelectedGroups(newSelection);
                    // Real-time validation
                    const newErrors = { ...errors };
                    if (!newSelection || newSelection.length === 0) {
                      newErrors.groups = 'Policy group selection is required';
                    } else {
                      delete newErrors.groups;
                    }
                    setErrors(newErrors);
                  }}
                  placeholder="Select groups"
                  dataCy="modal-policy-groups-dd"
                />
                {errors.groups && <p className="text-red-500 text-sm mt-1">{errors.groups}</p>}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Policy Definition (JSON) *
                </label>
                {!formData.policyTemplate && (
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                    <Info size={16} />
                    <span className="text-sm">Select a policy template to load JSON structure</span>
                  </div>
                )}
                {loadingTemplate && (
                  <div className="flex items-center gap-2 text-blue-400">
                    <Loader2 className="animate-spin" size={16} />
                    <span className="text-sm">Loading template...</span>
                  </div>
                )}
                {formData.policyTemplate && !loadingTemplate && !jsonError && !isEditing && (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircle size={16} />
                    <span className="text-sm">Template loaded</span>
                  </div>
                )}
                {formData.policyTemplate && !loadingTemplate && !jsonError && isCloning && (
                  <div className="flex items-center gap-2 text-blue-400">
                    <Copy size={16} />
                    <span className="text-sm">Original policy definition loaded</span>
                  </div>
                )}
                {formData.policyTemplate && !loadingTemplate && !jsonError && isEditing && (
                  <div className="flex items-center gap-2 text-purple-400">
                    <CheckCircle size={16} />
                    <span className="text-sm">Original policy definition loaded</span>
                  </div>
                )}
                {jsonError ? (
                  <div className="flex items-center gap-2 text-red-500">
                    <AlertCircle size={16} />
                    <span className="text-sm">Invalid JSON</span>
                  </div>
                ) : null}
              </div>
              
              <div className="border rounded-lg overflow-hidden border-gray-300 dark:border-gray-700">
                {!formData.policyTemplate && (
                  <div className="bg-gray-50 dark:bg-gray-800/50 p-4 text-center text-gray-500 dark:text-gray-400">
                    <Info size={20} className="mx-auto mb-2" />
                    <p className="text-sm">Select a policy template above to load JSON structure, or start writing your JSON definition</p>
                  </div>
                )}
                
                {formData.policyTemplate && (
                  <>
                    <JsonEditorWithAutocomplete
                      value={formData.definition}
                      onChange={(value) => {
                        setFormData(prev => ({ ...prev, definition: value }));
                        // Real-time validation
                        const newErrors = { ...errors };
                        if (!value.trim()) {
                          newErrors.definition = 'Policy definition is required';
                        } else {
                          try {
                            JSON.parse(value);
                            delete newErrors.definition;
                          } catch (error) {
                            newErrors.definition = `Invalid JSON format: ${error.message}`;
                          }
                        }
                        setErrors(newErrors);
                      }}
                      policyType={formData.policyTemplate}
                      height="300px"
                    />
                    
                    {errors.definition && (
                      <div className="p-3 bg-red-50 border-t border-red-200">
                        <p className="text-red-600 text-sm">{errors.definition}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Available Values Section */}
            {formData.policyTemplate && (
              <EnumSuggestions policyType={formData.policyTemplate} />
            )}

            {/* JSON Structure Guide */}
            {formData.policyTemplate && (
              <JsonStructureGuide policyType={formData.policyTemplate} />
            )}
          </div>

          {/* Error Display */}
          {errors.general && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                console.log('=== Button clicked ===');
                console.log('Button disabled state:', loading || !linksLoaded || Object.keys(errors).length > 0);
                console.log('Loading:', loading, 'LinksLoaded:', linksLoaded, 'Errors:', Object.keys(errors));
                handleSave();
              }}
              disabled={loading || !linksLoaded || Object.keys(errors).length > 0 || jsonError}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading && <Loader2 className="animate-spin" size={16} />}
              {isEditing ? 'Update Policy' : 'Create Policy'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedPolicyModal; 