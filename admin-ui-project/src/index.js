import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import PolicyGroupsPage from './pages/PolicyGroupsPage.js';
import AgentRegistryPage from './pages/AgentRegistryPage.js';
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage.js';
import { isPolicyGroupFeatureEnabled } from './utils/featureFlags.js';
import './index.css';
import { Provider } from 'react-redux';
import { store } from './store/index.js';
import MainLayout from './layouts/MainLayout.js';
import PolicyConfigurationPage from './pages/PolicyConfigurationPage.js';
// Import new page components
import EnterpriseRiskOverviewPage from './pages/ciso/EnterpriseRiskOverviewPage.js';
import ThreatIntelligencePage from './pages/ciso/ThreatIntelligencePage.js';
import DatasetManagementPage from './pages/testing/DatasetManagementPage.js';
import DatasetDetail from './components/testing/DatasetDetail.js';
import EvaluationsLibrary from './components/EvaluationsLibrary.js';
import EvaluationResults from './components/EvaluationResults.js';
import EvaluationAnalysisDetail from './components/EvaluationAnalysisDetail.js';
import EvaluationsLibraryPage from './pages/testing/EvaluationsLibraryPage.js';
import ExperimentsTestRunsPage from './pages/testing/ExperimentsTestRunsPage.js';
import RedTeamConsolePage from './pages/testing/RedTeamConsolePage.js';
import LiveAgentMonitorPage from './pages/monitoring/LiveAgentMonitorPage.js';
import PerformanceAnalyticsPage from './pages/monitoring/PerformanceAnalyticsPage.js';
import UsageTrackingPage from './pages/monitoring/UsageTrackingPage.js';
import AuditTrailPage from './pages/monitoring/AuditTrailPage.js';
import UserManagementPage from './pages/admin/UserManagementPage.js';
import SecuritySettingsPage from './pages/admin/SecuritySettingsPage.js';
import SystemConfigurationPage from './pages/admin/SystemConfigurationPage.js';
import AdminAuditLogsPage from './pages/admin/AdminAuditLogsPage.js';
import SupportCenterPage from './pages/support/SupportCenterPage.js';
import DocumentationCenterPage from './pages/support/DocumentationCenterPage.js';
import TrainingMaterialsHubPage from './pages/support/TrainingMaterialsHubPage.js';
import ExternalIntegrationTest from './components/ExternalIntegrationTest.js';

const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter basename="/admin">
        <Routes>
          <Route element={<MainLayout />}>
            <Route index element={<Navigate to="/policy-configuration" replace />} />
            <Route path="/policy-configuration" element={<PolicyConfigurationPage />} />
            <Route path="/agent-registry" element={<AgentRegistryPage />} />
            {isPolicyGroupFeatureEnabled() && (
              <Route path="/policy-groups" element={<PolicyGroupsPage />} />
            )}
            {/* CISO Executive Dashboard Routes */}
            <Route path="/ciso/risk" element={<EnterpriseRiskOverviewPage />} />
            <Route path="/ciso/threats" element={<ThreatIntelligencePage />} />
            
            {/* Testing & Evaluation Routes */}
            <Route path="/datasets" element={<DatasetManagementPage />} />
            <Route path="/datasets/:id" element={<DatasetDetail />} />
            <Route path="/evaluations" element={<EvaluationsLibrary />} />
            <Route path="/experiments" element={<ExperimentsTestRunsPage />} />
            <Route path="/evaluation-results" element={<EvaluationResults />} />
            <Route path="/evaluation-results/:id" element={<EvaluationAnalysisDetail />} />
            <Route path="/red-team" element={<RedTeamConsolePage />} />
            
            {/* Runtime Monitoring & Observability Routes */}
            <Route path="/monitoring/live" element={<LiveAgentMonitorPage />} />
            <Route path="/monitoring/performance" element={<PerformanceAnalyticsPage />} />
            <Route path="/monitoring/usage" element={<UsageTrackingPage />} />
            <Route path="/monitoring/audit" element={<AuditTrailPage />} />
            
            {/* Administration Routes */}
            <Route path="/admin/users" element={<UserManagementPage />} />
            <Route path="/admin/security" element={<SecuritySettingsPage />} />
            <Route path="/admin/system" element={<SystemConfigurationPage />} />
            <Route path="/admin/audit" element={<AdminAuditLogsPage />} />
            
            {/* Support & Help Routes */}
            <Route path="/support/center" element={<SupportCenterPage />} />
            <Route path="/support/documentation" element={<DocumentationCenterPage />} />
            <Route path="/support/training" element={<TrainingMaterialsHubPage />} />
            
            {/* Development Testing Route */}
            <Route path="/test/external-integration" element={<ExternalIntegrationTest />} />
          </Route>
          <Route path="/analytics" element={<AnalyticsDashboardPage />} />
        </Routes>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);
