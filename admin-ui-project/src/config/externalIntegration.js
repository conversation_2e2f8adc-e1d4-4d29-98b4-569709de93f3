// External System Integration Configuration
// This is temporary development code for rapid testing

export const externalSystemConfig = {
  baseUrl: process.env.REACT_APP_EXTERNAL_SYSTEM_BASE_URL || 'https://your-hgcs-domain.com',
  token: process.env.REACT_APP_EXTERNAL_SYSTEM_TOKEN || 'YOUR_TOKEN',
  debugMode: process.env.REACT_APP_EXTERNAL_INTEGRATION_DEBUG === 'true',
  
  endpoints: {
    updatePolicy: process.env.REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT || '/api/v1/policy/update_policy',
    updateAgentPolicy: process.env.REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT || '/api/v1/policy/update_agent_policy'
  }
};

// Generate curl command for debugging
export const generateCurlCommand = (url, method, headers, body) => {
  let curl = `curl -X ${method} "${url}"`;
  
  // Add headers
  Object.entries(headers).forEach(([key, value]) => {
    curl += ` \\\n  -H "${key}: ${value}"`;
  });
  
  // Add body if present
  if (body) {
    curl += ` \\\n  -d '${JSON.stringify(body, null, 2)}'`;
  }
  
  return curl;
};

// Log integration call for development
export const logIntegrationCall = (operation, curlCommand, response, error = null) => {
  if (!externalSystemConfig.debugMode) return;
  
  console.group(`🔗 External Integration: ${operation}`);
  console.log('📤 Curl Command:');
  console.log(curlCommand);
  
  if (error) {
    console.error('❌ Error:', error);
  } else if (response) {
    console.log('📥 Response:', response);
  }
  
  console.groupEnd();
};