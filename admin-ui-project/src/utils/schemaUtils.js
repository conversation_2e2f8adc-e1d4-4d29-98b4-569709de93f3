import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { fetchSchemas, fetchSchema, fetchSchemaTemplate } from './schemaApi';

// Initialize AJV with additional formats
const ajv = new Ajv({
  allErrors: true,
  verbose: true,
  useDefaults: true,
  coerceTypes: true
});

// Add formats safely
try {
  addFormats(ajv);
} catch (error) {
  console.error('Error adding formats to AJV:', error);
  // Continue without formats if there's an issue
}

// Cache for schemas
let schemasCache = null;

/**
 * Load schemas from API and cache them
 * @returns {Promise<Object>} Schemas object
 */
const loadSchemas = async () => {
  if (!schemasCache) {
    schemasCache = await fetchSchemas();
  }
  return schemasCache;
};

/**
 * Clear schema cache (call when schemas are updated)
 */
export const clearSchemaCache = () => {
  schemasCache = null;
};

/**
 * Get schema for a specific policy type
 * @param {string} policyType - The policy type (e.g., 'medical_privacy', 'data_privacy')
 * @returns {Promise<Object|null>} - The schema object or null if not found
 */
export const getSchemaForPolicyType = async (policyType) => {
  const schemas = await loadSchemas();
  if (!policyType || !schemas[policyType]) {
    return null;
  }
  return schemas[policyType];
};

/**
 * Validate a policy definition against its schema
 * @param {Object} policyDefinition - The policy definition to validate
 * @param {string} policyType - The policy type
 * @returns {Promise<Object>} - Validation result with isValid, errors, and warnings
 */
export const validatePolicyDefinition = async (policyDefinition, policyType) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return {
      isValid: false,
      errors: [`Unknown policy type: ${policyType}`],
      warnings: []
    };
  }

  try {
    const validate = ajv.compile(schema);
    const isValid = validate(policyDefinition);
    
    return {
      isValid,
      errors: isValid ? [] : validate.errors?.map(err => `${err.instancePath} ${err.message}`) || [],
      warnings: []
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Schema validation error: ${error.message}`],
      warnings: []
    };
  }
};

/**
 * Generate default values for a policy type based on its schema
 * @param {string} policyType - The policy type
 * @returns {Promise<Object>} - Default policy definition
 */
export const generateDefaultPolicy = async (policyType) => {
  try {
    // First try to get template from API (database > auto-generated > hardcoded)
    const apiTemplate = await fetchSchemaTemplate(policyType);
    if (apiTemplate) {
      console.log(`Using API template for ${policyType}`);
      return apiTemplate;
    }
  } catch (error) {
    console.warn(`Failed to fetch template from API for ${policyType}, falling back to hardcoded:`, error);
  }
  
  // Fallback to hardcoded templates (temporary during migration)
  console.warn(`Using hardcoded template for ${policyType} - API template not available`);
  const templates = {
    medical_privacy: {
      type: "medical_privacy",
      severity: "medium",
      allowed_roles: ["doctor"],
      hipaa_compliance: true,
      protected_fields: ["diagnosis"],
      audit_requirements: {
        log_access: true,
        retention_period: 7,
        encryption_required: true,
        access_timeout: 30
      },
      data_handling: {
        anonymization: false,
        pseudonymization: true,
        data_minimization: true
      }
    },
    data_privacy: {
      type: "data_privacy",
      severity: "medium",
      allowed_roles: ["admin"],
      data_classification: "confidential",
      protected_fields: ["personal_info"],
      consent_requirements: {
        explicit_consent: true,
        consent_expiry: 12,
        withdrawal_allowed: true
      },
      data_retention: {
        retention_period: 24,
        auto_deletion: true,
        archive_after: 12
      }
    },
    access_control: {
      type: "access_control",
      severity: "medium",
      allowed_roles: ["admin"],
      time_restrictions: {
        start_time: "09:00",
        end_time: "17:00",
        timezone: "UTC",
        allowed_days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
      },
      session_management: {
        max_session_duration: 60,
        inactivity_timeout: 15,
        concurrent_sessions: 3
      }
    },
    compliance: {
      type: "compliance",
      severity: "medium",
      allowed_roles: ["compliance_officer"],
      regulatory_framework: "gdpr",
      compliance_requirements: ["data_encryption"],
      audit_frequency: "quarterly",
      reporting_requirements: {
        incident_reporting: true,
        reporting_timeframe: 24,
        regulatory_notifications: true
      }
    }
  };

  // If we have a predefined template, use it
  if (templates[policyType]) {
    return templates[policyType];
  }

  // For dynamic schemas, generate defaults based on the schema structure
  try {
    const schema = await getSchemaForPolicyType(policyType);
    if (schema) {
      return generateDefaultFromSchema(schema, policyType);
    }
  } catch (error) {
    console.warn('Error loading schema for policy type:', policyType, error);
  }

  // Fallback for unknown schemas
  return { type: policyType };
};

/**
 * Generate default values based on a JSON schema
 * @param {Object} schema - The JSON schema
 * @param {string} policyType - The policy type
 * @returns {Object} - Default policy definition
 */
function generateDefaultFromSchema(schema, policyType) {
  const result = { type: policyType };
  
  if (schema.properties) {
    Object.keys(schema.properties).forEach(key => {
      const prop = schema.properties[key];
      // Special handling for 'type' field - always use the policyType
      if (key === 'type') {
        result[key] = policyType;
      } else {
        result[key] = getDefaultValueForProperty(prop);
      }
    });
  }
  
  return result;
}

/**
 * Get default value for a schema property
 * @param {Object} property - The schema property definition
 * @returns {*} - Default value
 */
function getDefaultValueForProperty(property) {
  // Use default if specified
  if (property.default !== undefined) {
    return property.default;
  }
  
  // Generate defaults based on type
  switch (property.type) {
    case 'string':
      return property.enum ? property.enum[0] : '';
    case 'number':
    case 'integer':
      return property.minimum !== undefined ? property.minimum : 0;
    case 'boolean':
      return false;
    case 'array':
      return [];
    case 'object':
      const obj = {};
      if (property.properties) {
        Object.keys(property.properties).forEach(key => {
          obj[key] = getDefaultValueForProperty(property.properties[key]);
        });
      }
      return obj;
    default:
      return null;
  }
}

/**
 * Get available policy types
 * @returns {Promise<Array>} - Array of available policy types
 */
export const getAvailablePolicyTypes = async () => {
  const schemas = await loadSchemas();
  return Object.keys(schemas);
};

/**
 * Get field descriptions and metadata for a policy type
 * @param {string} policyType - The policy type
 * @returns {Promise<Object>} - Field metadata including descriptions, enums, etc.
 */
export const getPolicyTypeMetadata = async (policyType) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return {};
  }

  const metadata = {};
  
  const extractFieldMetadata = (obj, path = '') => {
    if (obj.properties) {
      Object.keys(obj.properties).forEach(key => {
        const prop = obj.properties[key];
        const fullPath = path ? `${path}.${key}` : key;
        
        metadata[fullPath] = {
          description: prop.description || '',
          type: prop.type || 'unknown',
          required: obj.required?.includes(key) || false,
          enum: prop.enum || null,
          pattern: prop.pattern || null,
          minimum: prop.minimum || null,
          maximum: prop.maximum || null,
          default: prop.default || null
        };

        // Recursively process nested objects
        if (prop.type === 'object' && prop.properties) {
          extractFieldMetadata(prop, fullPath);
        }
      });
    }
  };

  extractFieldMetadata(schema);
  return metadata;
};

/**
 * Get enum values for a specific field
 * @param {string} policyType - The policy type
 * @param {string} fieldPath - The field path (e.g., 'allowed_roles', 'audit_requirements.log_access')
 * @returns {Promise<Array|null>} - Array of enum values or null if not found
 */
export const getFieldEnumValues = async (policyType, fieldPath) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return null;
  }

  const pathParts = fieldPath.split('.');
  let current = schema;

  for (const part of pathParts) {
    if (current.properties && current.properties[part]) {
      current = current.properties[part];
    } else {
      return null;
    }
  }

  return current.enum || null;
};

/**
 * Get enum values for a specific field in a policy type
 * @param {string} policyType - The policy type
 * @param {string} fieldPath - The field path (e.g., "allowed_roles", "protected_fields")
 * @returns {Promise<Array>} - Array of enum values
 */
export const getEnumValuesForField = async (policyType, fieldPath) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return [];
  }

  // Handle nested field paths like "audit_requirements.log_access"
  const pathParts = fieldPath.split('.');
  let currentSchema = schema;

  for (const part of pathParts) {
    if (currentSchema.properties && currentSchema.properties[part]) {
      currentSchema = currentSchema.properties[part];
    } else {
      return [];
    }
  }

  // Check if it's an array of enums
  if (currentSchema.type === 'array' && currentSchema.items && currentSchema.items.enum) {
    return currentSchema.items.enum;
  }
  
  // Check if it's a single enum
  if (currentSchema.enum) {
    return currentSchema.enum;
  }

  return [];
};

/**
 * Get all available enum fields for a policy type
 * @param {string} policyType - The policy type
 * @returns {Promise<Object>} - Object with field paths as keys and enum arrays as values
 */
export const getAllEnumFields = async (policyType) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return {};
  }

  const enumFields = {};

  const extractEnums = (obj, path = '') => {
    if (obj.properties) {
      Object.keys(obj.properties).forEach(key => {
        const prop = obj.properties[key];
        const fullPath = path ? `${path}.${key}` : key;
        
        if (prop.type === 'array' && prop.items && prop.items.enum) {
          enumFields[fullPath] = prop.items.enum;
        } else if (prop.enum) {
          enumFields[fullPath] = prop.enum;
        } else if (prop.type === 'object' && prop.properties) {
          extractEnums(prop, fullPath);
        }
      });
    }
  };

  extractEnums(schema);
  return enumFields;
};

/**
 * Format validation errors for display
 * @param {Array} errors - Array of validation error strings
 * @returns {Array} - Formatted error messages
 */
export const formatValidationErrors = (errors) => {
  return errors.map(error => {
    // Remove the leading slash and convert to readable format
    const cleanError = error.replace(/^\//, '').replace(/\//g, ' → ');
    return cleanError;
  });
};

/**
 * Check if a field is required for a policy type
 * @param {string} policyType - The policy type
 * @param {string} fieldPath - The field path
 * @returns {Promise<boolean>} - Whether the field is required
 */
export const isFieldRequired = async (policyType, fieldPath) => {
  const schema = await getSchemaForPolicyType(policyType);
  if (!schema) {
    return false;
  }

  const pathParts = fieldPath.split('.');
  let current = schema;

  for (const part of pathParts) {
    if (current.properties && current.properties[part]) {
      current = current.properties[part];
    } else {
      return false;
    }
  }

  return schema.required?.includes(pathParts[pathParts.length - 1]) || false;
};

export default {
  getSchemaForPolicyType,
  validatePolicyDefinition,
  generateDefaultPolicy,
  getAvailablePolicyTypes,
  getPolicyTypeMetadata,
  getFieldEnumValues,
  formatValidationErrors,
  isFieldRequired,
  clearSchemaCache
};