/**
 * Schema API utilities for fetching schemas from the backend
 * Replaces direct JSON file imports with API calls
 */

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8001';

// Cache schemas in memory with expiration
let schemaCache = null;
let cacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch all schemas from API
 * @returns {Promise<Object>} Object with schema names as keys and schema objects as values
 */
export async function fetchSchemas() {
  // Return cached schemas if still valid
  if (schemaCache && (Date.now() - cacheTime) < CACHE_DURATION) {
    console.log('Returning cached schemas');
    return schemaCache;
  }

  try {
    console.log('Fetching schemas from API...');
    const response = await fetch(`${API_BASE_URL}/api/v1/schemas`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch schemas: ${response.statusText}`);
    }
    
    const schemas = await response.json();
    
    // Cache the schemas
    schemaCache = schemas;
    cacheTime = Date.now();
    
    console.log(`Loaded ${Object.keys(schemas).length} schemas from API`);
    return schemas;
  } catch (error) {
    console.error('Error fetching schemas from API:', error);
    
    // Return empty object to prevent app crash
    return {};
  }
}

/**
 * Fetch schema list with metadata
 * @returns {Promise<Array>} Array of schema metadata objects
 */
export async function fetchSchemaList() {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/schemas/list`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch schema list: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.schemas || [];
  } catch (error) {
    console.error('Error fetching schema list:', error);
    return [];
  }
}

/**
 * Get specific schema by name
 * @param {string} schemaName - The name of the schema to fetch
 * @returns {Promise<Object|null>} Schema object or null if not found
 */
export async function fetchSchema(schemaName) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/schemas/${schemaName}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch schema: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.schema_content || null;
  } catch (error) {
    console.error(`Error fetching schema ${schemaName}:`, error);
    return null;
  }
}

/**
 * Get available schema names (for dropdowns)
 * @returns {Promise<Array>} Array of schema names
 */
export async function getSchemaNames() {
  const schemas = await fetchSchemas();
  return Object.keys(schemas);
}

/**
 * Get schema categories from schemas
 * This extracts unique categories from schema names
 * @returns {Promise<Array>} Array of unique categories
 */
export async function getSchemaCategories() {
  const schemas = await fetchSchemas();
  const schemaNames = Object.keys(schemas);
  
  // Map schema names to display categories
  const categoryMap = {
    'medical_privacy': 'Medical Privacy',
    'data_privacy': 'Data Privacy', 
    'access_control': 'Access Control',
    'compliance': 'Compliance'
  };
  
  return schemaNames.map(name => categoryMap[name] || name);
}

/**
 * Clear schema cache (call after updates)
 */
export function clearSchemaCache() {
  schemaCache = null;
  cacheTime = 0;
  console.log('Schema cache cleared');
}

/**
 * Fetch default template for a schema
 * @param {string} schemaName - The name of the schema
 * @returns {Promise<Object|null>} Template object or null if not found
 */
export async function fetchSchemaTemplate(schemaName) {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/schemas/${schemaName}/template`);
    
    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`No template found for schema: ${schemaName}`);
        return null;
      }
      throw new Error(`Failed to fetch template: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.template || null;
  } catch (error) {
    console.error(`Error fetching template for ${schemaName}:`, error);
    return null;
  }
}

// Export all functions as default object too
export default {
  fetchSchemas,
  fetchSchemaList,
  fetchSchema,
  getSchemaNames,
  getSchemaCategories,
  clearSchemaCache,
  fetchSchemaTemplate
};