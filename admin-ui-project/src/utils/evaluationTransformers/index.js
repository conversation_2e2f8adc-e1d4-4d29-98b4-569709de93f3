/**
 * Evaluation Transformers
 * 
 * These transformers convert raw evaluation data from different evaluator types
 * into a consistent format that can be consumed by the UI components.
 */

import { transformSecurityEvaluation } from './securityTransformer';
import { transformLLMJudgeEvaluation } from './llmJudgeTransformer';
import { transformPIIEvaluation } from './piiTransformer';

/**
 * Detect evaluator type and apply appropriate transformation
 * @param {Object} evaluationData - The overall evaluation data
 * @param {Object} evaluation - The specific evaluation result
 * @returns {Object} Transformed data ready for UI consumption
 */
export const transformEvaluationData = (evaluationData, evaluation) => {
  // Detect evaluator type
  const evaluatorId = evaluation.evaluator_id || '';
  const evaluatorType = evaluation.details?.evaluator_type || '';
  
  // Security Judge Evaluator
  if (evaluatorId === 'security_judge' || evaluatorType === 'security_judge') {
    return transformSecurityEvaluation(evaluationData, evaluation);
  }
  
  // Standard LLM Judge Evaluator
  if (evaluatorId === 'geval_judge' || evaluatorType === 'llm_judge') {
    return transformLLMJudgeEvaluation(evaluationData, evaluation);
  }
  
  // PII Detection Evaluator
  if (evaluatorId === 'presidio_pii' || 
      evaluatorType === 'pii_detection' ||
      evaluation.metric_name?.toLowerCase().includes('pii')) {
    return transformPIIEvaluation(evaluationData, evaluation);
  }
  
  // Default: return unchanged
  return evaluationData;
};

/**
 * Check if evaluation data needs transformation
 * @param {Object} evaluation - The evaluation result
 * @returns {boolean} True if transformation is needed
 */
export const needsTransformation = (evaluation) => {
  const evaluatorId = evaluation.evaluator_id || '';
  const evaluatorType = evaluation.details?.evaluator_type || '';
  
  return (
    evaluatorId === 'security_judge' ||
    evaluatorId === 'geval_judge' ||
    evaluatorId === 'presidio_pii' ||
    evaluatorType === 'security_judge' ||
    evaluatorType === 'llm_judge' ||
    evaluatorType === 'pii_detection' ||
    evaluation.metric_name?.toLowerCase().includes('pii')
  );
};

// Export individual transformers for direct use
export {
  transformSecurityEvaluation,
  transformLLMJudgeEvaluation,
  transformPIIEvaluation
};