/**
 * Transformer for LLM Judge (G-Eval) Evaluator Results
 * Converts raw LLM judge evaluation data into the format expected by the UI
 */

export const transformLLMJudgeEvaluation = (evaluationData, llmJudgeEval) => {
  // Extract details from the LLM judge evaluation
  const details = llmJudgeEval.details || {};
  const testResults = details.test_results || [];
  
  // Calculate overall metrics
  const totalTests = testResults.length;
  const passedTests = testResults.filter(t => t.passed).length;
  const failedTests = totalTests - passedTests;
  const passRate = totalTests > 0 ? (passedTests / totalTests) : 0;
  
  // Calculate average scores for each criterion
  const criteriaScores = {};
  const criteriaDescriptions = {};
  
  testResults.forEach(test => {
    if (test.criteria_results) {
      test.criteria_results.forEach(criterion => {
        if (!criteriaScores[criterion.name]) {
          criteriaScores[criterion.name] = [];
          criteriaDescriptions[criterion.name] = criterion.description;
        }
        criteriaScores[criterion.name].push(criterion.score || 0);
      });
    }
  });
  
  // Calculate average scores and format metrics
  const metrics = {};
  Object.entries(criteriaScores).forEach(([name, scores]) => {
    const avgScore = scores.length > 0 
      ? scores.reduce((a, b) => a + b, 0) / scores.length 
      : 0;
    
    metrics[name.toLowerCase()] = {
      score: Math.round(avgScore * 10) / 10, // Round to 1 decimal
      max: 10,
      description: criteriaDescriptions[name] || name
    };
  });
  
  // Add default metrics if not present
  const defaultMetrics = {
    correctness: 'Factual accuracy of responses',
    completeness: 'Coverage of all aspects',
    coherence: 'Logical flow and consistency',
    relevance: 'Staying on topic',
    helpfulness: 'Overall utility'
  };
  
  Object.entries(defaultMetrics).forEach(([key, desc]) => {
    if (!metrics[key]) {
      metrics[key] = {
        score: 0,
        max: 10,
        description: desc
      };
    }
  });
  
  // Calculate score distribution
  const distribution = [
    { range: "9-10", count: 0, percentage: 0 },
    { range: "7-8", count: 0, percentage: 0 },
    { range: "5-6", count: 0, percentage: 0 },
    { range: "3-4", count: 0, percentage: 0 },
    { range: "0-2", count: 0, percentage: 0 }
  ];
  
  testResults.forEach(test => {
    const score = test.score || 0;
    const normalizedScore = (score / 100) * 10; // Convert to 0-10 scale
    
    if (normalizedScore >= 9) distribution[0].count++;
    else if (normalizedScore >= 7) distribution[1].count++;
    else if (normalizedScore >= 5) distribution[2].count++;
    else if (normalizedScore >= 3) distribution[3].count++;
    else distribution[4].count++;
  });
  
  // Calculate percentages
  distribution.forEach(range => {
    range.percentage = totalTests > 0 
      ? Math.round((range.count / totalTests) * 100) 
      : 0;
  });
  
  // Transform test cases for UI
  const testCases = testResults.map((test, idx) => ({
    id: test.id || idx + 1,
    input: test.input || '',
    expected: test.expected_output || test.expected || '',
    actual: test.actual_output || test.output || '',
    score: Math.round((test.score / 100) * 10), // Convert to 0-10 scale
    verdict: test.passed ? 'PASS' : 'FAIL',
    confidence: test.confidence || 0.95,
    reasoning: test.reasoning || test.details?.reasoning || '',
    detailedScores: extractDetailedScores(test),
    scenario: test.scenario || null,
    turns: test.turns || null,
    test_case_type: test.test_case_type || 'single_turn'
  }));
  
  // Generate insights
  const insights = generateLLMJudgeInsights(testResults, metrics);
  
  return {
    ...evaluationData,
    isLLMJudgeEvaluation: true,
    overall: {
      score: llmJudgeEval.score || 85,
      maxScore: 100,
      passRate: passRate,
      totalTests: totalTests,
      passedTests: passedTests,
      failedTests: failedTests,
      averageConfidence: calculateAverageConfidence(testResults)
    },
    metrics,
    distribution,
    testCases,
    insights,
    metadata: {
      evaluator_name: 'LLM as Judge',
      model_used: details.model_used || 'gpt-4',
      evaluation_timestamp: llmJudgeEval.completed_at || new Date().toISOString(),
      evaluation_criteria: details.evaluation_criteria || Object.keys(metrics),
      scoring_scale: details.scoring_scale || '1-10'
    }
  };
};

// Extract detailed scores from test result
const extractDetailedScores = (test) => {
  const scores = {};
  
  if (test.criteria_results) {
    test.criteria_results.forEach(criterion => {
      scores[criterion.name.toLowerCase()] = criterion.score || 0;
    });
  } else if (test.criteria_scores) {
    // Fallback to criteria_scores if available
    Object.entries(test.criteria_scores).forEach(([key, value]) => {
      scores[key.toLowerCase()] = value;
    });
  }
  
  // Ensure we have standard metrics
  const standardMetrics = ['correctness', 'completeness', 'coherence', 'relevance', 'helpfulness'];
  standardMetrics.forEach(metric => {
    if (!(metric in scores)) {
      scores[metric] = 0;
    }
  });
  
  return scores;
};

// Calculate average confidence across all tests
const calculateAverageConfidence = (testResults) => {
  if (testResults.length === 0) return 0.9;
  
  const totalConfidence = testResults.reduce((sum, test) => 
    sum + (test.confidence || 0.95), 0
  );
  
  return Math.round((totalConfidence / testResults.length) * 100) / 100;
};

// Generate insights for LLM Judge evaluation
const generateLLMJudgeInsights = (testResults, metrics) => {
  const patterns = [];
  const recommendations = [];
  
  // Analyze metric performance
  const metricScores = Object.entries(metrics).map(([name, data]) => ({
    name,
    score: data.score
  })).sort((a, b) => a.score - b.score);
  
  // Identify weak areas
  const weakMetrics = metricScores.filter(m => m.score < 6);
  if (weakMetrics.length > 0) {
    patterns.push(`Weak performance in: ${weakMetrics.map(m => m.name).join(', ')}`);
    weakMetrics.forEach(metric => {
      if (metric.name === 'correctness') {
        recommendations.push('Improve factual accuracy through better training data or fact-checking mechanisms');
      } else if (metric.name === 'completeness') {
        recommendations.push('Ensure responses cover all aspects of the query');
      } else if (metric.name === 'coherence') {
        recommendations.push('Enhance logical flow and consistency in responses');
      } else if (metric.name === 'relevance') {
        recommendations.push('Improve context understanding to stay on topic');
      }
    });
  }
  
  // Identify strong areas
  const strongMetrics = metricScores.filter(m => m.score >= 8);
  if (strongMetrics.length > 0) {
    patterns.push(`Strong performance in: ${strongMetrics.map(m => m.name).join(', ')}`);
  }
  
  // Analyze failure patterns
  const failedTests = testResults.filter(t => !t.passed);
  if (failedTests.length > 0) {
    const failureRate = Math.round((failedTests.length / testResults.length) * 100);
    patterns.push(`${failureRate}% failure rate across ${failedTests.length} test cases`);
    
    // Look for common failure reasons
    const failureReasons = {};
    failedTests.forEach(test => {
      if (test.reasoning) {
        // Simple keyword extraction for common issues
        if (test.reasoning.toLowerCase().includes('incorrect')) {
          failureReasons['Factual errors'] = (failureReasons['Factual errors'] || 0) + 1;
        }
        if (test.reasoning.toLowerCase().includes('incomplete')) {
          failureReasons['Incomplete responses'] = (failureReasons['Incomplete responses'] || 0) + 1;
        }
        if (test.reasoning.toLowerCase().includes('irrelevant')) {
          failureReasons['Off-topic responses'] = (failureReasons['Off-topic responses'] || 0) + 1;
        }
      }
    });
    
    Object.entries(failureReasons).forEach(([reason, count]) => {
      if (count > 1) {
        patterns.push(`${reason} detected in ${count} cases`);
      }
    });
  } else {
    patterns.push('All test cases passed successfully');
  }
  
  // Add general recommendations if not already added
  if (recommendations.length === 0) {
    recommendations.push('Continue monitoring model performance');
    recommendations.push('Consider expanding test coverage');
  }
  
  return {
    patterns,
    recommendations
  };
};

export default transformLLMJudgeEvaluation;