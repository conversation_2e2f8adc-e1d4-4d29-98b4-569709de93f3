/**
 * Transformer for Security Judge Evaluator Results
 * Converts raw security evaluation data into the format expected by the UI
 */

export const transformSecurityEvaluation = (evaluationData, securityEval) => {
  // Extract test results from the security evaluation details
  const testResults = securityEval.details?.test_results || [];
  
  // Calculate summary statistics
  const failedTests = testResults.filter(t => t.verdict === 'FAIL');
  const passedTests = testResults.filter(t => t.verdict === 'PASS');
  
  // Count severity levels
  const criticalFailures = failedTests.filter(t => t.severity === 'critical').length;
  const highSeverityFailures = failedTests.filter(t => t.severity === 'high').length;
  const mediumSeverityFailures = failedTests.filter(t => t.severity === 'medium').length;
  const lowSeverityFailures = failedTests.filter(t => t.severity === 'low').length;
  
  // Calculate average defense score
  const totalDefenseScore = testResults.reduce((sum, t) => sum + (t.defense_score || 0), 0);
  const averageDefenseScore = testResults.length > 0 
    ? Math.round(totalDefenseScore / testResults.length) 
    : 0;
  
  // Group tests by security category
  const categoryCounts = {};
  testResults.forEach(test => {
    const category = test.security_category || 'Adversarial & Loophole';
    if (!categoryCounts[category]) {
      categoryCounts[category] = { 
        total: 0, 
        passed: 0, 
        failed: 0,
        criticalCount: 0,
        highCount: 0,
        mediumCount: 0
      };
    }
    categoryCounts[category].total++;
    
    if (test.verdict === 'PASS') {
      categoryCounts[category].passed++;
    } else {
      categoryCounts[category].failed++;
      // Track severity within category
      if (test.severity === 'critical') categoryCounts[category].criticalCount++;
      if (test.severity === 'high') categoryCounts[category].highCount++;
      if (test.severity === 'medium') categoryCounts[category].mediumCount++;
    }
  });
  
  // Transform category data for UI with icons
  const securityCategories = {};
  Object.entries(categoryCounts).forEach(([category, counts]) => {
    securityCategories[category] = {
      total: counts.total,
      passed: counts.passed,
      failed: counts.failed,
      score: counts.total > 0 ? Math.round((counts.passed / counts.total) * 100) : 0,
      criticalCount: counts.criticalCount,
      highCount: counts.highCount,
      mediumCount: counts.mediumCount,
      description: getCategoryDescription(category),
      icon: getCategoryIcon(category),
      color: getCategoryColor(category)
    };
  });
  
  // Add placeholder categories if not present
  const allCategories = ['Adversarial & Loophole', 'Data Privacy', 'Harmful Content'];
  allCategories.forEach(category => {
    if (!securityCategories[category]) {
      securityCategories[category] = {
        total: 0,
        passed: 0,
        failed: 0,
        score: 0,
        upcoming: true,
        description: getCategoryDescription(category),
        icon: getCategoryIcon(category),
        color: getCategoryColor(category)
      };
    }
  });
  
  // Generate insights based on the results
  const insights = generateSecurityInsights(
    testResults, 
    failedTests, 
    passedTests, 
    criticalFailures, 
    averageDefenseScore
  );
  
  // Extract unique recommendations from failed tests
  const recommendations = extractRecommendations(failedTests);
  
  return {
    ...evaluationData,
    isSecurityEvaluation: true,
    overall: {
      totalTests: testResults.length,
      passedTests: passedTests.length,
      failedTests: failedTests.length,
      passRate: testResults.length > 0 
        ? (passedTests.length / testResults.length) 
        : 0,
      securityScore: testResults.length > 0 
        ? Math.round((passedTests.length / testResults.length) * 100) 
        : 0,
      criticalFailures,
      highSeverityFailures,
      mediumSeverityFailures,
      lowSeverityFailures,
      averageDefenseScore
    },
    securityCategories,
    testCases: testResults.map((test, idx) => ({
      id: test.id || idx + 1,
      verdict: test.verdict,
      security_category: test.security_category || 'Adversarial & Loophole',
      severity: test.severity || 'medium',
      defense_score: test.defense_score || 0,
      reasoning: test.reasoning || '',
      vulnerabilities: test.vulnerabilities || [],
      recommendations: test.recommendations || [],
      input: test.input || '',
      expected: test.expected || test.expected_output || '',
      actual: test.actual || test.actual_output || '',
      model_used: test.model_used || 'Unknown',
      evaluator_type: 'security_judge'
    })),
    insights: {
      patterns: insights.patterns,
      recommendations: recommendations
    },
    metadata: {
      evaluator_name: 'Security Judge',
      evaluation_timestamp: securityEval.completed_at || new Date().toISOString(),
      model_evaluated: securityEval.details?.model_used || 'Unknown',
      total_duration: securityEval.duration_ms || null
    }
  };
};

// Helper function to get category descriptions
const getCategoryDescription = (category) => {
  const descriptions = {
    'Adversarial & Loophole': 'Defense against prompt injections, jailbreaks, and manipulation attempts',
    'Data Privacy': 'Protection of personal and sensitive information',
    'Harmful Content': 'Prevention of dangerous, illegal, or harmful content generation'
  };
  return descriptions[category] || 'Security evaluation category';
};

// Helper function to map categories to icon names (used by the component)
const getCategoryIcon = (category) => {
  const icons = {
    'Adversarial & Loophole': 'ShieldExclamationIcon',
    'Data Privacy': 'LockClosedIcon',
    'Harmful Content': 'ExclamationTriangleIcon'
  };
  return icons[category] || 'ShieldCheckIcon';
};

// Helper function to get category colors
const getCategoryColor = (category) => {
  const colors = {
    'Adversarial & Loophole': 'red',
    'Data Privacy': 'purple',
    'Harmful Content': 'orange'
  };
  return colors[category] || 'blue';
};

// Generate insights based on evaluation results
const generateSecurityInsights = (
  testResults, 
  failedTests, 
  passedTests, 
  criticalFailures, 
  averageDefenseScore
) => {
  const patterns = [];
  const failureRate = testResults.length > 0 
    ? Math.round((failedTests.length / testResults.length) * 100) 
    : 0;
  
  // Overall performance insight
  if (failureRate > 60) {
    patterns.push(`High failure rate (${failureRate}%) indicates significant security vulnerabilities`);
  } else if (failureRate > 30) {
    patterns.push(`Moderate failure rate (${failureRate}%) suggests room for security improvements`);
  } else if (failureRate > 0) {
    patterns.push(`Low failure rate (${failureRate}%) shows good security posture with minor issues`);
  } else {
    patterns.push('Perfect security score - all tests passed successfully');
  }
  
  // Critical issues insight
  if (criticalFailures > 0) {
    patterns.push(`${criticalFailures} critical security ${criticalFailures === 1 ? 'failure' : 'failures'} require immediate attention`);
  }
  
  // Defense score insight
  if (averageDefenseScore < 30) {
    patterns.push('Very low average defense score indicates weak security boundaries');
  } else if (averageDefenseScore < 60) {
    patterns.push('Moderate defense capabilities - significant hardening needed');
  } else if (averageDefenseScore < 80) {
    patterns.push('Good defense capabilities with room for improvement');
  } else {
    patterns.push('Strong defense mechanisms in place');
  }
  
  // Analyze failure patterns
  const failureCategories = {};
  failedTests.forEach(test => {
    const cat = test.security_category || 'Unknown';
    failureCategories[cat] = (failureCategories[cat] || 0) + 1;
  });
  
  const mostFailedCategory = Object.entries(failureCategories)
    .sort((a, b) => b[1] - a[1])[0];
  
  if (mostFailedCategory) {
    patterns.push(`Most failures in "${mostFailedCategory[0]}" category (${mostFailedCategory[1]} failures)`);
  }
  
  // Check for specific vulnerability patterns
  const hasPromptInjection = failedTests.some(t => 
    t.vulnerabilities?.some(v => 
      v.toLowerCase().includes('injection') || 
      v.toLowerCase().includes('jailbreak')
    )
  );
  
  if (hasPromptInjection) {
    patterns.push('Model is vulnerable to prompt injection attacks');
  }
  
  return { patterns };
};

// Extract and deduplicate recommendations
const extractRecommendations = (failedTests) => {
  const allRecommendations = failedTests.flatMap(t => t.recommendations || []);
  
  // Count frequency of each recommendation
  const recCounts = {};
  allRecommendations.forEach(rec => {
    recCounts[rec] = (recCounts[rec] || 0) + 1;
  });
  
  // Sort by frequency and take top recommendations
  return Object.entries(recCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([rec]) => rec);
};

export default transformSecurityEvaluation;