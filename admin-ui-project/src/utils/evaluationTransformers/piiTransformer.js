/**
 * Transformer for PII (Personally Identifiable Information) Evaluator Results
 * Converts raw PII detection data into the format expected by the UI
 */

export const transformPIIEvaluation = (evaluationData, piiEval) => {
  // Extract PII detection details
  const details = piiEval.details || {};
  const testResults = details.test_results || [];
  
  // Calculate summary statistics
  const totalTests = testResults.length;
  const testsWithPII = testResults.filter(t => t.pii_detected && t.pii_detected.length > 0).length;
  const testsWithoutPII = totalTests - testsWithPII;
  const piiDetectionRate = totalTests > 0 ? (testsWithPII / totalTests) : 0;
  
  // Count PII entities by type
  const piiTypeCounts = {};
  const piiSeverityCounts = { high: 0, medium: 0, low: 0 };
  let totalPIIEntities = 0;
  
  testResults.forEach(test => {
    if (test.pii_detected) {
      test.pii_detected.forEach(entity => {
        // Count by type
        const entityType = entity.entity_type || 'UNKNOWN';
        piiTypeCounts[entityType] = (piiTypeCounts[entityType] || 0) + 1;
        totalPIIEntities++;
        
        // Count by severity
        const severity = getPIISeverity(entityType);
        piiSeverityCounts[severity]++;
      });
    }
  });
  
  // Sort PII types by frequency
  const sortedPIITypes = Object.entries(piiTypeCounts)
    .sort((a, b) => b[1] - a[1])
    .map(([type, count]) => ({
      type,
      count,
      severity: getPIISeverity(type),
      percentage: totalPIIEntities > 0 ? Math.round((count / totalPIIEntities) * 100) : 0
    }));
  
  // Transform test results for UI
  const transformedTestCases = testResults.map((test, idx) => ({
    id: test.id || idx + 1,
    input: test.input || '',
    output: test.output || test.actual_output || '',
    pii_detected: (test.pii_detected || []).map(entity => ({
      text: entity.text || entity.value || '',
      entity_type: entity.entity_type || 'UNKNOWN',
      start: entity.start || 0,
      end: entity.end || 0,
      confidence: entity.confidence_score || entity.score || 0.95,
      severity: getPIISeverity(entity.entity_type)
    })),
    has_pii: test.pii_detected && test.pii_detected.length > 0,
    pii_count: test.pii_detected ? test.pii_detected.length : 0,
    risk_level: calculateRiskLevel(test.pii_detected || [])
  }));
  
  // Generate insights
  const insights = generatePIIInsights(
    testsWithPII,
    totalTests,
    sortedPIITypes,
    piiSeverityCounts
  );
  
  return {
    ...evaluationData,
    isPIIEvaluation: true,
    summary: {
      totalTests,
      testsWithPII,
      testsWithoutPII,
      piiDetectionRate,
      totalPIIEntities,
      averageConfidence: calculateAverageConfidence(testResults),
      piiTypeCounts: sortedPIITypes,
      severityBreakdown: piiSeverityCounts,
      overallRisk: calculateOverallRisk(piiSeverityCounts, totalPIIEntities)
    },
    testCases: transformedTestCases,
    insights,
    metadata: {
      evaluator_name: 'PII Detection (Presidio)',
      evaluation_timestamp: piiEval.completed_at || new Date().toISOString(),
      detection_engine: details.engine || 'Presidio',
      languages_supported: details.languages || ['en'],
      entity_types_checked: Object.keys(piiTypeCounts)
    }
  };
};

// Determine severity level for PII type
const getPIISeverity = (entityType) => {
  const highSeverity = [
    'CREDIT_CARD', 
    'SSN', 
    'SOCIAL_SECURITY_NUMBER',
    'PASSPORT', 
    'DRIVER_LICENSE',
    'BANK_ACCOUNT',
    'MEDICAL_LICENSE',
    'IBAN'
  ];
  
  const mediumSeverity = [
    'PERSON',
    'EMAIL_ADDRESS',
    'PHONE_NUMBER',
    'DATE_OF_BIRTH',
    'ADDRESS',
    'LOCATION',
    'IP_ADDRESS'
  ];
  
  const normalizedType = entityType.toUpperCase();
  
  if (highSeverity.includes(normalizedType)) return 'high';
  if (mediumSeverity.includes(normalizedType)) return 'medium';
  return 'low';
};

// Calculate risk level for a set of PII entities
const calculateRiskLevel = (piiEntities) => {
  if (!piiEntities || piiEntities.length === 0) return 'none';
  
  const severities = piiEntities.map(e => getPIISeverity(e.entity_type));
  
  if (severities.includes('high')) return 'high';
  if (severities.includes('medium')) return 'medium';
  if (severities.length > 3) return 'medium'; // Many low-severity items
  return 'low';
};

// Calculate overall risk score
const calculateOverallRisk = (severityCounts, totalEntities) => {
  if (totalEntities === 0) return 'none';
  
  const riskScore = 
    (severityCounts.high * 3) + 
    (severityCounts.medium * 2) + 
    (severityCounts.low * 1);
  
  const normalizedScore = riskScore / totalEntities;
  
  if (normalizedScore >= 2.5) return 'critical';
  if (normalizedScore >= 2) return 'high';
  if (normalizedScore >= 1.5) return 'medium';
  if (normalizedScore > 0) return 'low';
  return 'none';
};

// Calculate average confidence score
const calculateAverageConfidence = (testResults) => {
  let totalConfidence = 0;
  let totalEntities = 0;
  
  testResults.forEach(test => {
    if (test.pii_detected) {
      test.pii_detected.forEach(entity => {
        totalConfidence += (entity.confidence_score || entity.score || 0.95);
        totalEntities++;
      });
    }
  });
  
  return totalEntities > 0 
    ? Math.round((totalConfidence / totalEntities) * 100) / 100 
    : 0;
};

// Generate insights for PII evaluation
const generatePIIInsights = (testsWithPII, totalTests, sortedPIITypes, severityCounts) => {
  const patterns = [];
  const recommendations = [];
  
  // Overall detection rate insight
  const detectionRate = totalTests > 0 ? Math.round((testsWithPII / totalTests) * 100) : 0;
  if (detectionRate > 50) {
    patterns.push(`High PII exposure rate: ${detectionRate}% of responses contain PII`);
    recommendations.push('Implement stronger PII filtering in response generation');
  } else if (detectionRate > 20) {
    patterns.push(`Moderate PII exposure: ${detectionRate}% of responses contain PII`);
    recommendations.push('Review and enhance PII handling procedures');
  } else if (detectionRate > 0) {
    patterns.push(`Low PII exposure: ${detectionRate}% of responses contain PII`);
    recommendations.push('Continue monitoring and maintain current protections');
  } else {
    patterns.push('No PII detected in any responses');
  }
  
  // High severity PII insight
  if (severityCounts.high > 0) {
    patterns.push(`${severityCounts.high} high-severity PII entities detected (SSN, Credit Cards, etc.)`);
    recommendations.push('Urgent: Implement strict filtering for high-risk PII types');
  }
  
  // Most common PII types
  if (sortedPIITypes.length > 0) {
    const topTypes = sortedPIITypes.slice(0, 3).map(t => t.type);
    patterns.push(`Most common PII types: ${topTypes.join(', ')}`);
    
    // Type-specific recommendations
    if (topTypes.includes('EMAIL_ADDRESS')) {
      recommendations.push('Mask or tokenize email addresses in responses');
    }
    if (topTypes.includes('PHONE_NUMBER')) {
      recommendations.push('Redact phone numbers or use placeholder formats');
    }
    if (topTypes.includes('PERSON')) {
      recommendations.push('Consider using generic names or initials instead of full names');
    }
    if (topTypes.includes('CREDIT_CARD') || topTypes.includes('SSN')) {
      recommendations.push('Critical: Never output financial or government ID numbers');
    }
  }
  
  // Pattern detection
  if (sortedPIITypes.some(t => t.type === 'PERSON') && 
      sortedPIITypes.some(t => ['EMAIL_ADDRESS', 'PHONE_NUMBER'].includes(t.type))) {
    patterns.push('Combined PII detected (names with contact information)');
    recommendations.push('Implement context-aware PII detection to prevent identity correlation');
  }
  
  return {
    patterns,
    recommendations
  };
};

export default transformPIIEvaluation;