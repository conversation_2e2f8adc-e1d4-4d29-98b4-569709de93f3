{"name": "vitea-policy-admin", "version": "1.0.0", "private": true, "homepage": "/admin", "description": "Policy Management Admin Interface for Vitea Application", "dependencies": {"@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.7.0", "@reduxjs/toolkit": "^1.9.5", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.5.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-editor-ajrm": "^2.5.14", "react-redux": "^8.1.3", "react-router-dom": "^6.23.0", "react-scripts": "5.0.1"}, "devDependencies": {"cypress": "^13.6.2", "jest-fetch-mock": "^3.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "cy:open": "cypress open", "cy:run": "cypress run", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}