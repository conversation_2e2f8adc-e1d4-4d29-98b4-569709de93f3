<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="description" content="Vitea Policy Management Admin Interface - Manage and configure all guardrail policies for healthcare AI systems" />
    <meta name="keywords" content="policy management, healthcare, compliance, admin interface, vitea" />
    <title>Vitea Policy Admin - Healthcare Policy Management</title>
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Tailwind CSS CDN for styling support -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f9fafb;
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }

      /* Error boundary styles */
      .error-boundary {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        padding: 2rem;
        text-align: center;
        background-color: #fef2f2;
      }

      .error-icon {
        width: 64px;
        height: 64px;
        margin-bottom: 1rem;
        color: #dc2626;
      }

      .error-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #dc2626;
        margin-bottom: 0.5rem;
      }

      .error-message {
        color: #6b7280;
        margin-bottom: 1rem;
      }

      .error-button {
        background-color: #dc2626;
        color: white;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        font-weight: 500;
      }

      .error-button:hover {
        background-color: #b91c1c;
      }
    </style>
  </head>
  <body class="dark">
    <noscript>
      <div style="text-align: center; padding: 2rem;">
        <h1>JavaScript Required</h1>
        <p>You need to enable JavaScript to run the Vitea Policy Admin interface.</p>
      </div>
    </noscript>
    
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading Vitea Policy Admin</div>
        <div class="loading-subtitle">Healthcare Policy Management Interface</div>
      </div>
    </div>

    <!-- Error boundary fallback -->
    <script>
      window.addEventListener('error', function(e) {
        console.error('Application error:', e.error);
        // Could implement error reporting here
      });

      window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
        // Could implement error reporting here
      });
    </script>
  </body>
</html>
