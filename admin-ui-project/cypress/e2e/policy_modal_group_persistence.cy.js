/// <reference types="cypress" />

// Verify that selecting a Policy Group in the Edit Policy modal persists after save.

describe('Policy Modal – Group selection persists', () => {
  it('selects a group, saves, and pre-selects on reopen', () => {
    cy.visit('/');

    // open the first policy row's Edit button
    cy.get('table tbody tr').first().within(() => {
      cy.get('button[title="Edit Policy"]').click();
    });

    // modal should appear
    cy.contains('h2', 'Edit Policy', { timeout: 10000 }).should('be.visible');

    // open Policy Groups dropdown
    cy.get('[data-cy=modal-policy-groups-dd]', { timeout: 10000 }).scrollIntoView().click({ force: true });

    // pick first option & capture its label text
    cy.get('div.absolute:visible').first().find('div.cursor-pointer').first().as('firstOption');
    cy.get('@firstOption').invoke('text').then(label => {
      const trimmed = label.trim();
      cy.wrap(trimmed).as('selectedLabel');
    });
    cy.get('@firstOption').click({ force: true });

    // save the policy
    cy.contains('button', /update policy/i).click();

    // wait for modal to close
    cy.contains('h2', 'Edit Policy').should('not.exist');

    // reopen the same policy modal
    cy.get('table tbody tr').first().within(() => {
      cy.get('button[title="Edit Policy"]').click();
    });
    cy.contains('h2', 'Edit Policy').should('be.visible');

    // open dropdown again
    cy.get('[data-cy=modal-policy-groups-dd]', { timeout: 10000 }).scrollIntoView().click({ force: true });

    // the previously selected label should have a checkbox checked
    cy.get('@selectedLabel').then(lbl => {
      cy.contains('div.cursor-pointer', lbl)
        .find('input[type="checkbox"]')
        .should('be.checked');
    });

    // close modal (Cancel)
    cy.contains('button', 'Cancel').click();
  });
});
