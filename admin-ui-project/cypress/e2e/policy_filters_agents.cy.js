/// <reference types="cypress" />

// Generic test that applies an Agent filter first, captures row count, then adds a
// Policy Group filter and ensures the table reduces (AND logic).
describe('Policy Table – Agent + Policy Group filtering', () => {
  it('applies Agent then Group filters and narrows results', () => {
    cy.visit('/');

    // wait for table to render rows
    cy.get('table tbody tr', { timeout: 10000 }).should('have.length.gt', 0).then((baselineRows) => {
      const baseline = baselineRows.length;
      // ---------- Apply Agent filter ----------
      cy.get('[data-cy=agents-dd]', { timeout: 10000 }).scrollIntoView().click({ force: true });
      cy.get('div.absolute:visible').first().find('div.cursor-pointer').first().click({ force: true });
      // wait for filtered rows to render
      cy.get('table tbody tr', { timeout: 10000 }).should('have.length.lte', baseline).then(rowsAfterAgent => {
        const afterAgent = rowsAfterAgent.length;

      
        // ---------- Apply Policy Group filter ----------
        cy.get('[data-cy=policy-groups-dd]').scrollIntoView().click({ force: true });
        cy.get('div.absolute:visible')
          .first()
          .find('div.cursor-pointer')
          .first()
          .click({ force: true });

        cy.get('table tbody', { timeout: 10000 }).then($tbody => {
          const rowCount = $tbody.find('tr').length;
          expect(rowCount).to.be.lte(afterAgent);
        });
      });
    });
  });
});
