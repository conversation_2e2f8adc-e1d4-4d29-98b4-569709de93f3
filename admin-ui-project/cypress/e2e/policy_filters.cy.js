/// <reference types="cypress" />

// Basic smoke test for Policy filters (Phase 6)

describe('Policy Table Filters', () => {
  let baseline = 0;

  before(() => {
    // visit once & grab baseline row count
    cy.visit('/');
    cy.get('table tbody tr').its('length').then(n => {
      baseline = n;
    });
  });

  it('Filters by Policy Group', () => {
    // open dropdown
    cy.contains('button', 'Policy Groups').click();
    // choose first option (assumes at least one exists)
    cy.contains('div', 'PHI Redaction').click();

    // rows should be <= baseline
    cy.get('table tbody tr').its('length').should('be.lte', baseline);
  });
});
