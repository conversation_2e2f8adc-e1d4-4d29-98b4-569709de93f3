# Admin UI Environment Configuration
# Copy this file to .env and update the values as needed

# Policy Management API (pilot-api)
# Used for: policies, policy groups, enums, agents
# Default: http://localhost:8001
REACT_APP_API_BASE_URL=http://localhost:8001

# Testing/Observability API (eval-api)  
# Used for: datasets, evaluations, experiments, testing features
# Default: http://localhost:8000
REACT_APP_API_URL=http://localhost:8000

# Optional: Development server port
# PORT=3004

# Optional: Disable host check for development (use with caution)
# DANGEROUSLY_DISABLE_HOST_CHECK=true

# Optional: Authentication settings
# REACT_APP_AUTH_ENABLED=false
# REACT_APP_AUTH_PROVIDER=azure

# Optional: Feature flags
# REACT_APP_ENABLE_TESTING_FEATURES=true
# REACT_APP_ENABLE_ADMIN_FEATURES=true