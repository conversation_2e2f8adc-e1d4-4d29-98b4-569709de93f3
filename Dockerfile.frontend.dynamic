# Multi-stage build for React applications
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Build Admin UI
COPY admin-ui-project/package*.json ./admin-ui/
WORKDIR /app/admin-ui
RUN npm ci
COPY admin-ui-project/ .
# Set environment variables for admin UI build
ARG REACT_APP_API_BASE_URL
ARG REACT_APP_API_URL
ARG REACT_APP_AZURE_CLIENT_ID
ARG REACT_APP_AZURE_TENANT_ID
ARG REACT_APP_APP_NAME
ARG REACT_APP_EXTERNAL_SYSTEM_BASE_URL
ARG REACT_APP_EXTERNAL_SYSTEM_TOKEN
ARG REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT
ARG REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT
ENV REACT_APP_API_BASE_URL=$REACT_APP_API_BASE_URL
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_AZURE_CLIENT_ID=$REACT_APP_AZURE_CLIENT_ID
ENV REACT_APP_AZURE_TENANT_ID=$REACT_APP_AZURE_TENANT_ID
ENV REACT_APP_APP_NAME=$REACT_APP_APP_NAME
ENV REACT_APP_EXTERNAL_SYSTEM_BASE_URL=$REACT_APP_EXTERNAL_SYSTEM_BASE_URL
ENV REACT_APP_EXTERNAL_SYSTEM_TOKEN=$REACT_APP_EXTERNAL_SYSTEM_TOKEN
ENV REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT=$REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT
ENV REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT=$REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT
RUN npm run build

# Build Frontend (Chatbot)
WORKDIR /app
COPY frontend-project/package*.json ./frontend/
WORKDIR /app/frontend
RUN npm ci
COPY frontend-project/ .
# Set build environment variables for frontend
ARG REACT_APP_AZURE_CLIENT_ID
ARG REACT_APP_AZURE_TENANT_ID
ARG REACT_APP_API_BASE_URL
ARG REACT_APP_API_URL
ARG REACT_APP_APP_NAME
ARG REACT_APP_EXTERNAL_SYSTEM_BASE_URL
ARG REACT_APP_EXTERNAL_SYSTEM_TOKEN
ARG REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT
ARG REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT
ENV REACT_APP_AZURE_CLIENT_ID=$REACT_APP_AZURE_CLIENT_ID
ENV REACT_APP_AZURE_TENANT_ID=$REACT_APP_AZURE_TENANT_ID
ENV REACT_APP_API_BASE_URL=$REACT_APP_API_BASE_URL
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_APP_NAME=$REACT_APP_APP_NAME
ENV REACT_APP_EXTERNAL_SYSTEM_BASE_URL=$REACT_APP_EXTERNAL_SYSTEM_BASE_URL
ENV REACT_APP_EXTERNAL_SYSTEM_TOKEN=$REACT_APP_EXTERNAL_SYSTEM_TOKEN
ENV REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT=$REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT
ENV REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT=$REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT
RUN npm run build

# Production stage - Nginx
FROM nginx:alpine

# Install envsubst
RUN apk add --no-cache gettext

# Copy nginx config template
COPY nginx.conf.template /etc/nginx/templates/default.conf.template

# Copy built applications
COPY --from=builder /app/admin-ui/build /usr/share/nginx/html/admin
COPY --from=builder /app/frontend/build /usr/share/nginx/html/frontend

# Set default API URL
ENV API_URL=http://vitea-api:8000

# Expose port 80
EXPOSE 80

# Start nginx with envsubst
CMD envsubst '${API_URL}' < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'