version: '3.8'

services:
  # PostgreSQL Database
  pilot-postgres:
    image: postgres:15-alpine
    container_name: pilot-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-vitea_db}
      POSTGRES_USER: ${DB_USER:-dbadmin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-vitea123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/pilot-db-schema-bak-final-20250901_002200.sql:/docker-entrypoint-initdb.d/01-schema-backup.sql
      - ./scripts/pilot-db-functions-triggers-bak-v1.sql:/docker-entrypoint-initdb.d/02-functions-triggers.sql
      - ./scripts/pilot-db-data-bak-v2-20250901_104440.sql:/docker-entrypoint-initdb.d/03-data-backup.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dbadmin -d vitea_db"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - vitea-shared-network

  # API Server
  pilot-api:
    build:
      context: ./enhanced-api-project
      dockerfile: Dockerfile
    container_name: pilot-api
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: ${PORT:-8000}
      DB_HOST: ${DB_HOST:-pilot-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-vitea123}
      DB_NAME: ${DB_NAME:-vitea_db}
      DB_USER: ${DB_USER:-dbadmin}
      # External Integrations
      INTEGRATIONS_ENABLED: ${INTEGRATIONS_ENABLED:-false}
      INTEGRATION_WEBHOOK_URLS: ${INTEGRATION_WEBHOOK_URLS:-}
      INTEGRATION_WEBHOOK_SECRET_ACTIVE: ${INTEGRATION_WEBHOOK_SECRET_ACTIVE:-}
      # Azure OpenAI configuration
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT}
      AZURE_OPENAI_KEY: ${AZURE_OPENAI_KEY}
      AZURE_OPENAI_DEPLOYMENT_NAME: ${AZURE_OPENAI_DEPLOYMENT_NAME}
      # MCP configuration
      MCP_SERVER_ENABLED: ${MCP_SERVER_ENABLED:-true}
      MCP_SERVER_HOST: ${MCP_SERVER_HOST:-vitea-mcp-server_mcp-server_1}
    ports:
      - "8001:8000"
    depends_on:
      pilot-postgres:
        condition: service_healthy
    volumes:
      - ./enhanced-api-project/configs:/app/configs:ro
    networks:
      - vitea-shared-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vitea MCP Server (New FastMCP-based server)
  # Note: MCP server is now managed separately via start-all-services.sh
  # and runs from ../vitea-mcp-server directory

  # Frontend (Chatbot + Admin UI)
  pilot-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend.dynamic
      args:
        REACT_APP_AZURE_CLIENT_ID: ${REACT_APP_AZURE_CLIENT_ID}
        REACT_APP_AZURE_TENANT_ID: ${REACT_APP_AZURE_TENANT_ID}
        REACT_APP_API_BASE_URL: ${REACT_APP_API_BASE_URL}
        REACT_APP_API_URL: ${REACT_APP_API_URL}
        REACT_APP_APP_NAME: ${REACT_APP_APP_NAME:-Vitea Policy Admin}
        REACT_APP_EXTERNAL_SYSTEM_BASE_URL: ${REACT_APP_EXTERNAL_SYSTEM_BASE_URL:-http://localhost:8000}
        REACT_APP_EXTERNAL_SYSTEM_TOKEN: ${REACT_APP_EXTERNAL_SYSTEM_TOKEN:-YOUR_TOKEN}
        REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT: ${REACT_APP_EXTERNAL_POLICY_UPDATE_ENDPOINT:-/api/v1/policy/update_policy}
        REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT: ${REACT_APP_EXTERNAL_AGENT_POLICY_UPDATE_ENDPOINT:-/api/v1/policy/update_agent_policy}
    container_name: pilot-frontend
    environment:
      API_URL: http://pilot-api:8000
    ports:
      - "3001:80"
    depends_on:
      - pilot-api
    networks:
      - vitea-shared-network

volumes:
  postgres_data:
    driver: local

networks:
  vitea-shared-network:
    external: true